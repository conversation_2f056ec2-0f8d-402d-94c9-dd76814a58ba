import pytest
from path.path_planner import <PERSON>Planner, PathNode
from path.coordinate_system import Vector3D

@pytest.fixture
def planner():
    """Pytest fixture for PathPlanner."""
    return PathPlanner()

@pytest.fixture
def node_a():
    return PathNode("A", Vector3D(0, 0, 0), "ground_station")

@pytest.fixture
def node_b():
    return PathNode("B", Vector3D(100, 0, 0), "ground_station")

@pytest.fixture
def node_c():
    return PathNode("C", Vector3D(200, 0, 0), "satellite")

def test_add_and_remove_node(planner, node_a):
    """Test adding and removing a node from the planner."""
    assert len(planner.nodes) == 0
    
    planner.add_node(node_a)
    assert len(planner.nodes) == 1
    assert "A" in planner.nodes
    
    planner.remove_node("A")
    assert len(planner.nodes) == 0

def test_add_and_remove_connection(planner, node_a, node_b, node_c):
    """Test adding and removing connections between nodes."""
    planner.add_node(node_a)
    planner.add_node(node_b)
    planner.add_node(node_c)
    
    planner.add_connection("A", "B")
    assert "B" in planner.connections["A"]
    assert "A" in planner.connections["B"]
    
    planner.add_connection("A", "C")
    assert "C" in planner.connections["A"]
    assert len(planner.connections["A"]) == 2
    
    planner.remove_connection("A", "B")
    assert "B" not in planner.connections["A"]
    assert "A" not in planner.connections["B"]
    assert len(planner.connections["A"]) == 1

def test_plan_path_invalid_nodes(planner):
    """Test planning a path with non-existent nodes."""
    result = planner.plan_path("nonexistent1", "nonexistent2")
    assert not result.success

# A more comprehensive test would mock the connections and check the output of
# the A* algorithm, which is more involved.
def test_plan_path_simple_astar(planner, node_a, node_b):
    """A simple test for the A* path planning logic."""
    planner.add_node(node_a)
    planner.add_node(node_b)
    planner.add_connection("A", "B")

    # Mock _can_connect to always return true for this simple test
    planner._can_connect = lambda n1, n2: True
    
    result = planner.plan_path("A", "B")
    
    assert result.success
    assert len(result.nodes) == 2
    assert result.nodes[0].node_id == "A"
    assert result.nodes[1].node_id == "B"
    assert len(result.segments) == 1
    assert result.total_distance == 100 