"""
路径控制模块使用示例

展示路径控制模块的主要功能，包括：
1. 坐标系统转换示例
2. 轨道计算示例
3. 路径规划示例
4. 链路管理示例
5. 路径优化示例
6. 路径预测示例
"""

import math
import time
from datetime import datetime, timezone
from typing import List

# 导入路径控制模块
from .coordinate_system import Vector3D, LatLonAlt, CoordinateSystem
from .orbit_calculator import OrbitCalculator, OrbitParameters, SatelliteState
from .path_planner import PathPlanner, PathNode
from .link_manager import LinkManager, LinkQuality, LinkState
from .optimization import PathOptimizer, OptimizationCriteria
from .path_predictor import PathPredictor


def example_coordinate_system():
    """坐标系统转换示例"""
    print("=== 坐标系统转换示例 ===")
    
    # 地理坐标示例（北京）
    beijing_lla = LatLonAlt(latitude=39.9042, longitude=116.4074, altitude=50.0)
    print(f"北京地理坐标: 纬度={beijing_lla.latitude}°, 经度={beijing_lla.longitude}°, 高度={beijing_lla.altitude}m")
    
    # 转换为ECEF坐标
    beijing_ecef = CoordinateSystem.lla_to_ecef(beijing_lla)
    print(f"北京ECEF坐标: X={beijing_ecef.x:.2f}m, Y={beijing_ecef.y:.2f}m, Z={beijing_ecef.z:.2f}m")
    
    # 反向转换验证
    beijing_lla_back = CoordinateSystem.ecef_to_lla(beijing_ecef)
    print(f"反向转换验证: 纬度={beijing_lla_back.latitude:.6f}°, 经度={beijing_lla_back.longitude:.6f}°")
    
    # 向量计算示例
    vec1 = Vector3D(1000, 2000, 3000)
    vec2 = Vector3D(500, 1500, 2500)
    print(f"向量1: {vec1}")
    print(f"向量2: {vec2}")
    print(f"向量相加: {vec1 + vec2}")
    print(f"向量距离: {vec1.distance_to(vec2):.2f}m")
    print(f"向量角度: {math.degrees(vec1.angle_to(vec2)):.2f}°")
    print()


def example_orbit_calculation():
    """轨道计算示例"""
    print("=== 轨道计算示例 ===")
    
    orbit_calc = OrbitCalculator()
    
    # 创建一个典型的LEO卫星轨道参数
    leo_orbit = OrbitParameters(
        semi_major_axis=6778137.0,  # 400km高度
        eccentricity=0.001,
        inclination=math.radians(98.7),  # 太阳同步轨道
        raan=math.radians(0.0),
        argument_of_perigee=math.radians(0.0),
        true_anomaly=math.radians(0.0),
        epoch=2460000.0  # 儒略日
    )
    
    print(f"轨道参数:")
    print(f"  半长轴: {leo_orbit.semi_major_axis/1000:.1f} km")
    print(f"  偏心率: {leo_orbit.eccentricity:.4f}")
    print(f"  倾角: {math.degrees(leo_orbit.inclination):.1f}°")
    print(f"  轨道周期: {leo_orbit.period/60:.1f} 分钟")
    print(f"  远地点高度: {leo_orbit.apogee_altitude/1000:.1f} km")
    print(f"  近地点高度: {leo_orbit.perigee_altitude/1000:.1f} km")
    
    # 计算卫星状态
    sat_state = orbit_calc.kepler_to_cartesian(leo_orbit)
    print(f"卫星当前状态:")
    print(f"  位置: ({sat_state.position.x/1000:.1f}, {sat_state.position.y/1000:.1f}, {sat_state.position.z/1000:.1f}) km")
    print(f"  速度: {sat_state.speed/1000:.2f} km/s")
    print(f"  高度: {sat_state.altitude/1000:.1f} km")
    
    # 轨道传播示例
    future_time = leo_orbit.epoch + 90/1440  # 90分钟后
    future_state = orbit_calc.propagate_orbit(sat_state, future_time)
    print(f"90分钟后卫星状态:")
    print(f"  位置: ({future_state.position.x/1000:.1f}, {future_state.position.y/1000:.1f}, {future_state.position.z/1000:.1f}) km")
    print(f"  高度: {future_state.altitude/1000:.1f} km")
    
    # 地面轨迹计算
    ground_track = orbit_calc.calculate_ground_track(leo_orbit, 3600, 300)  # 1小时，5分钟间隔
    print(f"地面轨迹点数: {len(ground_track)}")
    if ground_track:
        print(f"首点: 纬度={ground_track[0].latitude:.2f}°, 经度={ground_track[0].longitude:.2f}°")
        print(f"末点: 纬度={ground_track[-1].latitude:.2f}°, 经度={ground_track[-1].longitude:.2f}°")
    
    print()


def example_path_planning():
    """路径规划示例"""
    print("=== 路径规划示例 ===")
    
    path_planner = PathPlanner()
    
    # 创建模拟的卫星状态
    satellite_states = []
    for i in range(5):
        # 在不同轨道位置创建卫星
        angle = i * 72  # 72度间隔
        altitude = 400000 + i * 50000  # 400-600km高度
        
        x = (CoordinateSystem.EARTH_RADIUS + altitude) * math.cos(math.radians(angle))
        y = (CoordinateSystem.EARTH_RADIUS + altitude) * math.sin(math.radians(angle))
        z = 0
        
        position = Vector3D(x, y, z)
        velocity = Vector3D(-y/1000, x/1000, 0)  # 简化的轨道速度
        
        sat_state = SatelliteState(position, velocity, 2460000.0)
        satellite_states.append(sat_state)
    
    # 创建地面站
    ground_stations = [
        LatLonAlt(39.9042, 116.4074, 50),    # 北京
        LatLonAlt(40.7128, -74.0060, 10),    # 纽约
        LatLonAlt(51.5074, -0.1278, 20),     # 伦敦
    ]
    
    # 更新网络拓扑
    path_planner.update_topology(satellite_states, ground_stations)
    
    print(f"网络节点数: {len(path_planner.nodes)}")
    print(f"网络连接数: {sum(len(conns) for conns in path_planner.connections.values()) // 2}")
    
    # 规划从第一个卫星到第一个地面站的路径
    start_node_id = "sat_0"
    end_node_id = "gs_0"
    
    if start_node_id in path_planner.nodes and end_node_id in path_planner.nodes:
        path_result = path_planner.plan_path(start_node_id, end_node_id, "astar")
        
        if path_result.success:
            print(f"路径规划成功!")
            print(f"  跳数: {path_result.hop_count}")
            print(f"  总距离: {path_result.total_distance/1000:.1f} km")
            print(f"  总延迟: {path_result.total_delay*1000:.2f} ms")
            print(f"  平均质量: {path_result.average_quality:.3f}")
            
            print("路径详情:")
            for i, node in enumerate(path_result.nodes):
                print(f"  节点{i}: {node.node_id} ({node.node_type})")
        else:
            print("路径规划失败")
    
    # 统计信息
    stats = path_planner.get_planning_statistics()
    print(f"规划统计: {stats}")
    print()


def example_link_management():
    """链路管理示例"""
    print("=== 链路管理示例 ===")
    
    link_manager = LinkManager()
    
    # 创建两个测试节点位置
    pos1 = Vector3D(6778137, 0, 0)       # 地球表面
    pos2 = Vector3D(6778137, 1000000, 0) # 1000km外
    
    # 创建链路
    link_id = link_manager.create_link("sat_1", "sat_2", "satellite_to_satellite")
    print(f"创建链路: {link_id}")
    
    # 更新链路质量
    link_manager.update_link_quality(link_id, pos1, pos2)
    
    # 获取链路信息
    if link_id in link_manager.links:
        link = link_manager.links[link_id]
        print(f"链路状态: {link.state.value}")
        print(f"链路质量:")
        print(f"  信号强度: {link.quality.signal_strength:.1f} dBm")
        print(f"  信噪比: {link.quality.snr:.1f} dB")
        print(f"  延迟: {link.quality.latency:.2f} ms")
        print(f"  带宽: {link.quality.bandwidth/1e9:.2f} Gbps")
        print(f"  质量评分: {link.quality.quality_score:.3f}")
    
    # 测试不同距离下的链路质量
    distances = [500000, 1000000, 1500000, 2000000]  # 500km到2000km
    print("\n不同距离下的链路质量:")
    for dist in distances:
        pos2_test = Vector3D(6778137 + dist, 0, 0)
        link_manager.update_link_quality(link_id, pos1, pos2_test)
        link = link_manager.links[link_id]
        print(f"  {dist/1000:.0f}km: 信号{link.quality.signal_strength:.1f}dBm, "
              f"SNR{link.quality.snr:.1f}dB, 质量{link.quality.quality_score:.3f}")
    
    # 统计信息
    stats = link_manager.get_statistics()
    print(f"\n链路管理统计: {stats}")
    print()


def example_path_optimization():
    """路径优化示例"""
    print("=== 路径优化示例 ===")
    
    # 创建模拟路径（简化）
    nodes = []
    for i in range(4):
        pos = Vector3D(6778137 + i*200000, i*100000, 0)
        node = PathNode(f"node_{i}", pos, "satellite")
        nodes.append(node)
    
    # 构造一个简单的路径结果（这里简化处理）
    from .path_planner import PathResult, PathSegment
    
    segments = []
    total_distance = 0
    total_delay = 0
    total_cost = 0
    
    for i in range(len(nodes) - 1):
        node1, node2 = nodes[i], nodes[i+1]
        distance = node1.distance_to(node2)
        delay = distance / 299792458
        quality = 0.8
        bandwidth = 1e9
        
        segment = PathSegment(node1, node2, distance, delay, quality, bandwidth)
        segments.append(segment)
        total_distance += distance
        total_delay += delay
        total_cost += distance / 1000  # 简化成本计算
    
    original_path = PathResult(nodes, segments, total_distance, total_delay, total_cost, True)
    
    # 创建优化器和优化准则
    optimizer = PathOptimizer()
    criteria = OptimizationCriteria(
        minimize_delay=1.0,
        maximize_bandwidth=0.8,
        minimize_hops=1.2,
        maximize_reliability=0.9,
        minimize_cost=1.1
    )
    
    # 执行优化
    optimization_result = optimizer.optimize_path(original_path, criteria)
    
    print(f"优化结果:")
    print(f"  成功: {optimization_result.success}")
    print(f"  改进百分比: {optimization_result.improvement_percentage:.2f}%")
    print(f"  优化类型: {optimization_result.optimization_type}")
    
    print(f"性能对比:")
    print(f"  原始延迟: {original_path.total_delay*1000:.2f} ms")
    print(f"  优化延迟: {optimization_result.optimized_path.total_delay*1000:.2f} ms")
    print(f"  原始跳数: {original_path.hop_count}")
    print(f"  优化跳数: {optimization_result.optimized_path.hop_count}")
    
    # 统计信息
    stats = optimizer.get_optimization_statistics()
    print(f"优化统计: {stats}")
    print()


def example_path_prediction():
    """路径预测示例"""
    print("=== 路径预测示例 ===")
    
    predictor = PathPredictor()
    orbit_calc = OrbitCalculator()
    
    # 创建测试卫星状态
    leo_orbit = OrbitParameters(
        semi_major_axis=6778137.0,
        eccentricity=0.001,
        inclination=math.radians(98.7),
        raan=math.radians(0.0),
        argument_of_perigee=math.radians(0.0),
        true_anomaly=math.radians(0.0),
        epoch=2460000.0
    )
    
    sat_state = orbit_calc.kepler_to_cartesian(leo_orbit)
    satellite_states = [sat_state]
    
    # 预测30分钟后的位置
    future_time = leo_orbit.epoch + 30/1440  # 30分钟
    predicted_states = predictor.predict_satellite_positions(satellite_states, future_time)
    
    print(f"卫星位置预测:")
    print(f"  当前位置: ({sat_state.position.x/1000:.1f}, {sat_state.position.y/1000:.1f}, {sat_state.position.z/1000:.1f}) km")
    print(f"  预测位置: ({predicted_states[0].position.x/1000:.1f}, {predicted_states[0].position.y/1000:.1f}, {predicted_states[0].position.z/1000:.1f}) km")
    
    # 链路质量预测
    distance = 1000000  # 1000km
    predicted_quality = predictor.predict_link_quality(distance, "satellite_to_satellite")
    print(f"链路质量预测 (距离{distance/1000}km):")
    print(f"  信号强度: {predicted_quality.signal_strength:.1f} dBm")
    print(f"  信噪比: {predicted_quality.snr:.1f} dB")
    print(f"  延迟: {predicted_quality.latency:.2f} ms")
    print(f"  带宽: {predicted_quality.bandwidth/1e9:.2f} Gbps")
    
    # 拓扑变化预测
    ground_stations = [LatLonAlt(39.9042, 116.4074, 50)]
    topology_changes = predictor.predict_topology_changes(
        satellite_states, ground_stations, future_time
    )
    
    print(f"拓扑变化预测:")
    print(f"  预测事件数量: {len(topology_changes)}")
    for change in topology_changes[:3]:  # 显示前3个事件
        print(f"  事件类型: {change['type']}")
    
    # 统计信息
    stats = predictor.get_prediction_statistics()
    print(f"预测统计: {stats}")
    print()


def run_comprehensive_example():
    """综合示例：完整的路径控制流程"""
    print("=== 综合示例：完整的路径控制流程 ===")
    
    # 1. 初始化各模块
    orbit_calc = OrbitCalculator()
    path_planner = PathPlanner()
    link_manager = LinkManager()
    optimizer = PathOptimizer()
    predictor = PathPredictor()
    
    print("1. 创建卫星星座...")
    # 创建一个小型卫星星座
    satellite_states = []
    for i in range(6):
        # Walker星座配置
        altitude = 550000  # 550km
        inclination = math.radians(53.0)
        raan = math.radians(i * 60)  # 6个轨道面
        
        orbit = OrbitParameters(
            semi_major_axis=CoordinateSystem.EARTH_RADIUS + altitude,
            eccentricity=0.0001,
            inclination=inclination,
            raan=raan,
            argument_of_perigee=0.0,
            true_anomaly=math.radians(i * 60),
            epoch=2460000.0
        )
        
        sat_state = orbit_calc.kepler_to_cartesian(orbit)
        satellite_states.append(sat_state)
    
    print(f"创建了 {len(satellite_states)} 颗卫星")
    
    # 2. 建立网络拓扑
    print("2. 建立网络拓扑...")
    ground_stations = [
        LatLonAlt(39.9042, 116.4074, 50),   # 北京
        LatLonAlt(40.7128, -74.0060, 10),   # 纽约
        LatLonAlt(-33.8688, 151.2093, 20),  # 悉尼
    ]
    
    path_planner.update_topology(satellite_states, ground_stations)
    print(f"网络节点: {len(path_planner.nodes)}")
    print(f"网络连接: {sum(len(conns) for conns in path_planner.connections.values()) // 2}")
    
    # 3. 路径规划
    print("3. 执行路径规划...")
    path_result = path_planner.plan_path("gs_0", "gs_1", "astar")  # 北京到纽约
    
    if path_result.success:
        print(f"路径规划成功: {path_result.hop_count} 跳, {path_result.total_distance/1000:.0f} km")
        
        # 4. 路径优化
        print("4. 执行路径优化...")
        criteria = OptimizationCriteria(minimize_delay=1.0, minimize_hops=1.2)
        opt_result = optimizer.optimize_path(path_result, criteria)
        
        if opt_result.success:
            print(f"优化成功，改进 {opt_result.improvement_percentage:.1f}%")
        
        # 5. 性能预测
        print("5. 执行性能预测...")
        future_time = 2460000.0 + 1/24  # 1小时后
        predicted_sats = predictor.predict_satellite_positions(satellite_states, future_time)
        print(f"预测 {len(predicted_sats)} 颗卫星的未来位置")
        
        # 6. 链路质量管理
        print("6. 链路质量管理...")
        active_links = 0
        for i, sat1 in enumerate(satellite_states):
            for j, sat2 in enumerate(satellite_states[i+1:], i+1):
                distance = sat1.position.distance_to(sat2.position)
                if distance <= 2000000:  # 2000km内
                    link_id = link_manager.create_link(f"sat_{i}", f"sat_{j}", "satellite_to_satellite")
                    if link_id:
                        link_manager.update_link_quality(link_id, sat1.position, sat2.position)
                        active_links += 1
        
        print(f"管理 {active_links} 条活跃链路")
        
        # 7. 综合统计
        print("7. 系统性能统计...")
        print(f"  轨道计算次数: {orbit_calc.get_calculation_statistics()['total_calculations']}")
        print(f"  路径规划次数: {path_planner.get_planning_statistics()['total_plannings']}")
        print(f"  链路质量更新: {link_manager.get_statistics()['quality_updates']}")
        print(f"  优化执行次数: {optimizer.get_optimization_statistics()['total_optimizations']}")
        print(f"  预测执行次数: {predictor.get_prediction_statistics()['total_predictions']}")
        
    else:
        print("路径规划失败")
    
    print("=== 综合示例完成 ===")


def main():
    """主函数：运行所有示例"""
    print("SDT卫星数字孪生系统 - 路径控制模块示例")
    print("=" * 50)
    
    try:
        example_coordinate_system()
        example_orbit_calculation()
        example_path_planning()
        example_link_management()
        example_path_optimization()
        example_path_prediction()
        run_comprehensive_example()
        
        print("所有示例运行完成!")
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 