#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路由算法
实现各种网络路由算法，包括Dijkstra、BFS等
"""

import numpy as np
import heapq
from typing import List, Optional, Tuple, Dict, Set
from collections import deque


def dijkstra(adj_matrix: np.ndarray, start_node: int, end_node: int) -> List[int]:
    """
    使用Dijkstra算法计算最短路径
    
    Args:
        adj_matrix: 邻接矩阵，形状为 (N, N)
        start_node: 起始节点索引
        end_node: 目标节点索引
        
    Returns:
        最短路径节点列表，如果无路径则返回空列表
    """
    N = adj_matrix.shape[0]
    
    # 输入验证
    if not (0 <= start_node < N and 0 <= end_node < N):
        raise ValueError("节点索引超出范围")
    
    if start_node == end_node:
        return [start_node]
    
    # 初始化距离和前驱节点
    distances = np.full(N, np.inf)
    predecessors = np.full(N, -1, dtype=int)
    visited = np.zeros(N, dtype=bool)
    
    distances[start_node] = 0.0
    
    # 优先队列：(距离, 节点)
    pq = [(0.0, start_node)]
    
    while pq:
        current_dist, current_node = heapq.heappop(pq)
        
        # 如果已访问过，跳过
        if visited[current_node]:
            continue
        
        # 标记为已访问
        visited[current_node] = True
        
        # 如果到达目标节点，提前退出
        if current_node == end_node:
            break
        
        # 检查所有邻居
        for neighbor in range(N):
            if adj_matrix[current_node, neighbor] > 0 and not visited[neighbor]:
                # 计算新距离（这里假设邻接矩阵存储的是权重，1表示连通）
                new_dist = current_dist + adj_matrix[current_node, neighbor]
                
                if new_dist < distances[neighbor]:
                    distances[neighbor] = new_dist
                    predecessors[neighbor] = current_node
                    heapq.heappush(pq, (new_dist, neighbor))
    
    # 重构路径
    if distances[end_node] == np.inf:
        return []  # 无路径
    
    path = []
    current = end_node
    while current != -1:
        path.append(current)
        current = predecessors[current]
    
    path.reverse()
    return path


def bfs_shortest_path(adj_matrix: np.ndarray, start_node: int, end_node: int) -> List[int]:
    """
    使用BFS算法计算最短路径（跳数最少）
    
    Args:
        adj_matrix: 邻接矩阵，形状为 (N, N)
        start_node: 起始节点索引
        end_node: 目标节点索引
        
    Returns:
        最短路径节点列表，如果无路径则返回空列表
    """
    N = adj_matrix.shape[0]
    
    # 输入验证
    if not (0 <= start_node < N and 0 <= end_node < N):
        raise ValueError("节点索引超出范围")
    
    if start_node == end_node:
        return [start_node]
    
    # BFS初始化
    visited = np.zeros(N, dtype=bool)
    predecessors = np.full(N, -1, dtype=int)
    queue = deque([start_node])
    visited[start_node] = True
    
    while queue:
        current_node = queue.popleft()
        
        # 检查所有邻居
        for neighbor in range(N):
            if adj_matrix[current_node, neighbor] > 0 and not visited[neighbor]:
                visited[neighbor] = True
                predecessors[neighbor] = current_node
                queue.append(neighbor)
                
                # 如果到达目标节点，提前退出
                if neighbor == end_node:
                    break
        
        # 如果找到目标节点，退出外层循环
        if visited[end_node]:
            break
    
    # 重构路径
    if not visited[end_node]:
        return []  # 无路径
    
    path = []
    current = end_node
    while current != -1:
        path.append(current)
        current = predecessors[current]
    
    path.reverse()
    return path


def floyd_warshall(adj_matrix: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """
    使用Floyd-Warshall算法计算所有节点对之间的最短路径
    
    Args:
        adj_matrix: 邻接矩阵，形状为 (N, N)
        
    Returns:
        tuple: (距离矩阵, 下一跳矩阵)
    """
    N = adj_matrix.shape[0]
    
    # 初始化距离矩阵
    dist = np.full((N, N), np.inf)
    next_hop = np.full((N, N), -1, dtype=int)
    
    # 设置直接连接的距离
    for i in range(N):
        for j in range(N):
            if i == j:
                dist[i, j] = 0.0
            elif adj_matrix[i, j] > 0:
                dist[i, j] = adj_matrix[i, j]
                next_hop[i, j] = j
    
    # Floyd-Warshall主循环
    for k in range(N):
        for i in range(N):
            for j in range(N):
                if dist[i, k] + dist[k, j] < dist[i, j]:
                    dist[i, j] = dist[i, k] + dist[k, j]
                    next_hop[i, j] = next_hop[i, k]
    
    return dist, next_hop


def reconstruct_path_from_next_hop(next_hop: np.ndarray, start: int, end: int) -> List[int]:
    """
    从下一跳矩阵重构路径
    
    Args:
        next_hop: 下一跳矩阵
        start: 起始节点
        end: 目标节点
        
    Returns:
        路径节点列表
    """
    if next_hop[start, end] == -1:
        return []  # 无路径
    
    path = [start]
    current = start
    
    while current != end:
        current = next_hop[current, end]
        path.append(current)
    
    return path


def compute_network_connectivity(adj_matrix: np.ndarray) -> Dict[str, float]:
    """
    计算网络连通性指标
    
    Args:
        adj_matrix: 邻接矩阵
        
    Returns:
        连通性指标字典
    """
    N = adj_matrix.shape[0]
    
    if N == 0:
        return {'connectivity': 0.0, 'average_path_length': 0.0, 'diameter': 0.0}
    
    # 计算所有节点对的最短路径
    dist, _ = floyd_warshall(adj_matrix)
    
    # 统计连通的节点对
    connected_pairs = 0
    total_distance = 0.0
    max_distance = 0.0
    
    for i in range(N):
        for j in range(i + 1, N):
            if dist[i, j] < np.inf:
                connected_pairs += 1
                total_distance += dist[i, j]
                max_distance = max(max_distance, dist[i, j])
    
    total_pairs = N * (N - 1) // 2
    
    # 计算指标
    connectivity = connected_pairs / total_pairs if total_pairs > 0 else 0.0
    average_path_length = total_distance / connected_pairs if connected_pairs > 0 else 0.0
    diameter = max_distance
    
    return {
        'connectivity': connectivity,
        'average_path_length': average_path_length,
        'diameter': diameter,
        'connected_pairs': connected_pairs,
        'total_pairs': total_pairs
    }


def find_k_shortest_paths(adj_matrix: np.ndarray, start_node: int, end_node: int, k: int = 3) -> List[List[int]]:
    """
    寻找k条最短路径
    
    Args:
        adj_matrix: 邻接矩阵
        start_node: 起始节点
        end_node: 目标节点
        k: 路径数量
        
    Returns:
        k条最短路径列表
    """
    if k <= 0:
        return []
    
    # 首先找到最短路径
    shortest_path = dijkstra(adj_matrix, start_node, end_node)
    if not shortest_path:
        return []
    
    paths = [shortest_path]
    
    if k == 1:
        return paths
    
    # 使用Yen's算法的简化版本
    # 这里实现一个基础版本，实际应用中可能需要更复杂的算法
    
    candidates = []
    
    for i in range(1, k):
        # 对于每条已找到的路径，尝试找到替代路径
        for path_idx in range(len(paths)):
            current_path = paths[path_idx]
            
            # 尝试从路径的每个节点开始寻找替代路径
            for spur_idx in range(len(current_path) - 1):
                spur_node = current_path[spur_idx]
                root_path = current_path[:spur_idx + 1]
                
                # 临时移除已使用的边
                temp_adj = adj_matrix.copy()
                
                # 移除根路径中的边
                for j in range(len(root_path) - 1):
                    temp_adj[root_path[j], root_path[j + 1]] = 0
                
                # 移除与已找到路径冲突的边
                for existing_path in paths:
                    if (len(existing_path) > spur_idx and 
                        existing_path[:spur_idx + 1] == root_path):
                        if spur_idx + 1 < len(existing_path):
                            temp_adj[spur_node, existing_path[spur_idx + 1]] = 0
                
                # 寻找从spur_node到end_node的最短路径
                spur_path = dijkstra(temp_adj, spur_node, end_node)
                
                if spur_path:
                    # 组合根路径和spur路径
                    candidate_path = root_path[:-1] + spur_path
                    
                    # 检查是否已存在
                    if candidate_path not in candidates and candidate_path not in paths:
                        candidates.append(candidate_path)
        
        if not candidates:
            break
        
        # 选择最短的候选路径
        best_candidate = min(candidates, key=len)
        paths.append(best_candidate)
        candidates.remove(best_candidate)
    
    return paths


class RoutingTable:
    """
    路由表类
    维护网络的路由信息
    """
    
    def __init__(self, num_nodes: int):
        """
        初始化路由表
        
        Args:
            num_nodes: 节点数量
        """
        self.num_nodes = num_nodes
        self.next_hop_table = np.full((num_nodes, num_nodes), -1, dtype=int)
        self.distance_table = np.full((num_nodes, num_nodes), np.inf)
        
        # 初始化对角线
        for i in range(num_nodes):
            self.distance_table[i, i] = 0.0
            self.next_hop_table[i, i] = i
    
    def update_from_adjacency_matrix(self, adj_matrix: np.ndarray):
        """
        从邻接矩阵更新路由表
        
        Args:
            adj_matrix: 邻接矩阵
        """
        dist, next_hop = floyd_warshall(adj_matrix)
        self.distance_table = dist
        self.next_hop_table = next_hop
    
    def get_next_hop(self, source: int, destination: int) -> int:
        """
        获取下一跳节点
        
        Args:
            source: 源节点
            destination: 目标节点
            
        Returns:
            下一跳节点索引，-1表示无路径
        """
        if not (0 <= source < self.num_nodes and 0 <= destination < self.num_nodes):
            return -1
        
        return self.next_hop_table[source, destination]
    
    def get_path(self, source: int, destination: int) -> List[int]:
        """
        获取完整路径
        
        Args:
            source: 源节点
            destination: 目标节点
            
        Returns:
            路径节点列表
        """
        return reconstruct_path_from_next_hop(self.next_hop_table, source, destination)
    
    def get_distance(self, source: int, destination: int) -> float:
        """
        获取路径距离
        
        Args:
            source: 源节点
            destination: 目标节点
            
        Returns:
            路径距离
        """
        if not (0 <= source < self.num_nodes and 0 <= destination < self.num_nodes):
            return np.inf
        
        return self.distance_table[source, destination]
