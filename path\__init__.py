"""
SDT卫星数字孪生系统 - 路径控制模块

这个模块提供了完整的卫星路径控制功能，包括轨道计算、路径规划、
链路管理和路径优化，完全使用纯Python实现。

主要功能：
- 卫星轨道计算和预测
- 地面站与卫星通信路径规划
- 卫星间链路路径管理
- 动态路径优化
- 坐标系统转换
- 实时路径调整
"""

from .coordinate_system import CoordinateSystem, Vector3D, LatLonAlt
from .orbit_calculator import OrbitCalculator, SatelliteState, OrbitParameters
from .path_planner import PathPlanner, PathNode, PathResult
from .link_manager import LinkManager, LinkState, LinkQuality
from .optimization import PathOptimizer, OptimizationResult
from .path_predictor import PathPredictor, PredictionResult

__version__ = "1.0.0"
__author__ = "SDT Development Team"

__all__ = [
    # 坐标系统
    'CoordinateSystem',
    'Vector3D', 
    'LatLonAlt',
    
    # 轨道计算
    'OrbitCalculator',
    'SatelliteState',
    'OrbitParameters',
    
    # 路径规划
    'PathPlanner',
    'PathNode',
    'PathResult',
    
    # 链路管理
    'LinkManager',
    'LinkState',
    'LinkQuality',
    
    # 路径优化
    'PathOptimizer',
    'OptimizationResult',
    
    # 路径预测
    'PathPredictor',
    'PredictionResult'
] 