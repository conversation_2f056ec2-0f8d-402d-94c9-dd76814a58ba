"""
轨道计算器模块

提供卫星轨道计算和预测功能，包括：
- 开普勒轨道要素计算
- 卫星位置和速度预测
- 轨道传播
- 地面覆盖计算
- 可见性分析
"""

import math
from typing import List, Tuple, Optional
from dataclasses import dataclass
from .coordinate_system import Vector3D, LatLonAlt, CoordinateSystem


@dataclass
class OrbitParameters:
    """轨道参数类"""
    semi_major_axis: float      # 半长轴 (m)
    eccentricity: float         # 偏心率
    inclination: float          # 轨道倾角 (rad)
    raan: float                # 升交点赤经 (rad)
    argument_of_perigee: float  # 近地点幅角 (rad)
    true_anomaly: float         # 真近点角 (rad)
    epoch: float               # 历元时间 (儒略日)
    
    def __post_init__(self):
        """验证轨道参数的有效性"""
        if self.semi_major_axis <= 0:
            raise ValueError("半长轴必须大于0")
        if not (0 <= self.eccentricity < 1):
            raise ValueError("偏心率必须在[0,1)范围内")
        if not (0 <= self.inclination <= math.pi):
            raise ValueError("倾角必须在[0,π]范围内")
    
    @property
    def period(self) -> float:
        """计算轨道周期 (秒)"""
        mu = OrbitCalculator.EARTH_MU
        return 2 * math.pi * math.sqrt(self.semi_major_axis**3 / mu)
    
    @property
    def apogee_altitude(self) -> float:
        """计算远地点高度 (m)"""
        return self.semi_major_axis * (1 + self.eccentricity) - CoordinateSystem.EARTH_RADIUS
    
    @property
    def perigee_altitude(self) -> float:
        """计算近地点高度 (m)"""
        return self.semi_major_axis * (1 - self.eccentricity) - CoordinateSystem.EARTH_RADIUS


@dataclass
class SatelliteState:
    """卫星状态类"""
    position: Vector3D          # 位置向量 (m)
    velocity: Vector3D          # 速度向量 (m/s)
    time: float                # 时间 (儒略日)
    orbit_params: Optional[OrbitParameters] = None
    
    @property
    def altitude(self) -> float:
        """计算高度 (m)"""
        return self.position.magnitude() - CoordinateSystem.EARTH_RADIUS
    
    @property
    def speed(self) -> float:
        """计算速度大小 (m/s)"""
        return self.velocity.magnitude()
    
    def to_lla(self) -> LatLonAlt:
        """转换为地理坐标"""
        return CoordinateSystem.ecef_to_lla(self.position)


class OrbitCalculator:
    """轨道计算器"""
    
    # 地球引力参数
    EARTH_MU = 3.986004418e14  # m³/s²
    
    # 地球自转角速度
    EARTH_ROTATION_RATE = 7.2921159e-5  # rad/s
    
    def __init__(self):
        """初始化轨道计算器"""
        self.calculation_count = 0
        self.cache = {}
    
    def kepler_to_cartesian(self, orbit_params: OrbitParameters) -> SatelliteState:
        """
        开普勒轨道要素转笛卡尔坐标
        
        Args:
            orbit_params: 轨道参数
            
        Returns:
            卫星状态（位置和速度）
        """
        a = orbit_params.semi_major_axis
        e = orbit_params.eccentricity
        i = orbit_params.inclination
        raan = orbit_params.raan
        w = orbit_params.argument_of_perigee
        nu = orbit_params.true_anomaly
        
        # 计算轨道半径
        r = a * (1 - e**2) / (1 + e * math.cos(nu))
        
        # 轨道平面内的位置和速度
        cos_nu = math.cos(nu)
        sin_nu = math.sin(nu)
        
        # 位置（轨道平面）
        x_orb = r * cos_nu
        y_orb = r * sin_nu
        z_orb = 0.0
        
        # 速度（轨道平面）
        h = math.sqrt(self.EARTH_MU * a * (1 - e**2))  # 角动量
        vx_orb = -self.EARTH_MU / h * sin_nu
        vy_orb = self.EARTH_MU / h * (e + cos_nu)
        vz_orb = 0.0
        
        # 旋转矩阵系数
        cos_raan = math.cos(raan)
        sin_raan = math.sin(raan)
        cos_i = math.cos(i)
        sin_i = math.sin(i)
        cos_w = math.cos(w)
        sin_w = math.sin(w)
        
        # 旋转到ECI坐标系
        # 位置变换
        px = (cos_raan * cos_w - sin_raan * sin_w * cos_i) * x_orb + \
             (-cos_raan * sin_w - sin_raan * cos_w * cos_i) * y_orb
        py = (sin_raan * cos_w + cos_raan * sin_w * cos_i) * x_orb + \
             (-sin_raan * sin_w + cos_raan * cos_w * cos_i) * y_orb
        pz = (sin_w * sin_i) * x_orb + (cos_w * sin_i) * y_orb
        
        # 速度变换
        vx = (cos_raan * cos_w - sin_raan * sin_w * cos_i) * vx_orb + \
             (-cos_raan * sin_w - sin_raan * cos_w * cos_i) * vy_orb
        vy = (sin_raan * cos_w + cos_raan * sin_w * cos_i) * vx_orb + \
             (-sin_raan * sin_w + cos_raan * cos_w * cos_i) * vy_orb
        vz = (sin_w * sin_i) * vx_orb + (cos_w * sin_i) * vy_orb
        
        position = Vector3D(px, py, pz)
        velocity = Vector3D(vx, vy, vz)
        
        self.calculation_count += 1
        
        return SatelliteState(
            position=position,
            velocity=velocity,
            time=orbit_params.epoch,
            orbit_params=orbit_params
        )
    
    def cartesian_to_kepler(self, state: SatelliteState) -> OrbitParameters:
        """
        笛卡尔坐标转开普勒轨道要素
        
        Args:
            state: 卫星状态
            
        Returns:
            轨道参数
        """
        r_vec = state.position
        v_vec = state.velocity
        r = r_vec.magnitude()
        v = v_vec.magnitude()
        
        # 计算角动量向量
        h_vec = r_vec.cross(v_vec)
        h = h_vec.magnitude()
        
        # 计算偏心率向量
        rv_dot = r_vec.dot(v_vec)
        e_vec = ((v**2 - self.EARTH_MU/r) * r_vec - rv_dot * v_vec) * (1/self.EARTH_MU)
        e = e_vec.magnitude()
        
        # 计算半长轴
        energy = v**2/2 - self.EARTH_MU/r
        a = -self.EARTH_MU / (2 * energy)
        
        # 计算倾角
        i = math.acos(h_vec.z / h)
        
        # 计算升交点赤经
        n_vec = Vector3D(0, 0, 1).cross(h_vec)  # 节点向量
        n = n_vec.magnitude()
        
        if n > 1e-10:
            raan = math.acos(n_vec.x / n)
            if n_vec.y < 0:
                raan = 2 * math.pi - raan
        else:
            raan = 0.0
        
        # 计算近地点幅角
        if n > 1e-10:
            w = math.acos(n_vec.dot(e_vec) / (n * e))
            if e_vec.z < 0:
                w = 2 * math.pi - w
        else:
            w = math.atan2(e_vec.y, e_vec.x)
        
        # 计算真近点角
        nu = math.acos(e_vec.dot(r_vec) / (e * r))
        if rv_dot < 0:
            nu = 2 * math.pi - nu
        
        return OrbitParameters(
            semi_major_axis=a,
            eccentricity=e,
            inclination=i,
            raan=raan,
            argument_of_perigee=w,
            true_anomaly=nu,
            epoch=state.time
        )
    
    def propagate_orbit(self, initial_state: SatelliteState, target_time: float) -> SatelliteState:
        """
        轨道传播（使用二体问题简化模型）
        
        Args:
            initial_state: 初始卫星状态
            target_time: 目标时间 (儒略日)
            
        Returns:
            传播后的卫星状态
        """
        dt = (target_time - initial_state.time) * 86400  # 转换为秒
        
        if abs(dt) < 1e-6:  # 时间差很小，直接返回
            return initial_state
        
        # 获取初始轨道要素
        if initial_state.orbit_params:
            orbit_params = initial_state.orbit_params
        else:
            orbit_params = self.cartesian_to_kepler(initial_state)
        
        # 计算平均运动
        n = math.sqrt(self.EARTH_MU / orbit_params.semi_major_axis**3)
        
        # 计算偏近点角变化
        e = orbit_params.eccentricity
        nu0 = orbit_params.true_anomaly
        
        # 真近点角转偏近点角
        E0 = 2 * math.atan(math.sqrt((1-e)/(1+e)) * math.tan(nu0/2))
        
        # 偏近点角转平近点角
        M0 = E0 - e * math.sin(E0)
        
        # 传播平近点角
        M = M0 + n * dt
        M = M % (2 * math.pi)  # 归一化到[0, 2π]
        
        # 求解开普勒方程得到新的偏近点角
        E = self._solve_kepler_equation(M, e)
        
        # 偏近点角转真近点角
        nu = 2 * math.atan(math.sqrt((1+e)/(1-e)) * math.tan(E/2))
        
        # 更新轨道参数
        new_orbit_params = OrbitParameters(
            semi_major_axis=orbit_params.semi_major_axis,
            eccentricity=orbit_params.eccentricity,
            inclination=orbit_params.inclination,
            raan=orbit_params.raan,
            argument_of_perigee=orbit_params.argument_of_perigee,
            true_anomaly=nu,
            epoch=target_time
        )
        
        # 转换为笛卡尔坐标
        return self.kepler_to_cartesian(new_orbit_params)
    
    def _solve_kepler_equation(self, M: float, e: float, tolerance: float = 1e-12) -> float:
        """
        求解开普勒方程 M = E - e*sin(E)
        
        Args:
            M: 平近点角
            e: 偏心率
            tolerance: 收敛精度
            
        Returns:
            偏近点角E
        """
        # 初始猜测
        E = M if e < 0.8 else math.pi
        
        # 牛顿-拉夫逊迭代
        for _ in range(50):  # 最多迭代50次
            f = E - e * math.sin(E) - M
            df = 1 - e * math.cos(E)
            
            E_new = E - f / df
            
            if abs(E_new - E) < tolerance:
                return E_new
            
            E = E_new
        
        return E
    
    def calculate_ground_track(self, orbit_params: OrbitParameters, 
                             duration: float, time_step: float = 60.0) -> List[LatLonAlt]:
        """
        计算卫星地面轨迹
        
        Args:
            orbit_params: 轨道参数
            duration: 计算时长 (秒)
            time_step: 时间步长 (秒)
            
        Returns:
            地面轨迹点列表
        """
        ground_track = []
        current_time = orbit_params.epoch
        
        steps = int(duration / time_step)
        
        for i in range(steps + 1):
            # 计算当前时刻的卫星状态
            time_julian = current_time + i * time_step / 86400
            
            # 传播轨道
            state = self.propagate_orbit(
                self.kepler_to_cartesian(orbit_params), 
                time_julian
            )
            
            # 转换为地理坐标
            lla = state.to_lla()
            ground_track.append(lla)
        
        return ground_track
    
    def calculate_visibility(self, satellite_state: SatelliteState, 
                           ground_station: LatLonAlt, 
                           min_elevation: float = 10.0) -> Tuple[bool, float, float]:
        """
        计算卫星对地面站的可见性
        
        Args:
            satellite_state: 卫星状态
            ground_station: 地面站位置
            min_elevation: 最小仰角 (度)
            
        Returns:
            (is_visible, elevation, azimuth) 可见性、仰角、方位角
        """
        # 地面站ECEF坐标
        gs_ecef = CoordinateSystem.lla_to_ecef(ground_station)
        
        # 卫星相对地面站的位置向量
        rel_pos = satellite_state.position - gs_ecef
        
        # 转换到地面站局部坐标系（ENU）
        local_pos = CoordinateSystem.global_to_local(satellite_state.position, ground_station)
        
        # 计算方位角和仰角
        range_km, azimuth, elevation = CoordinateSystem.cartesian_to_spherical(local_pos)
        
        # 转换为度
        elevation_deg = math.degrees(elevation)
        azimuth_deg = math.degrees(azimuth)
        
        # 调整方位角范围到[0, 360)
        if azimuth_deg < 0:
            azimuth_deg += 360
        
        # 判断可见性
        is_visible = elevation_deg >= min_elevation
        
        return is_visible, elevation_deg, azimuth_deg
    
    def calculate_access_windows(self, orbit_params: OrbitParameters,
                               ground_station: LatLonAlt,
                               duration: float,
                               min_elevation: float = 10.0,
                               time_step: float = 60.0) -> List[Tuple[float, float]]:
        """
        计算卫星对地面站的可见时间窗口
        
        Args:
            orbit_params: 轨道参数
            ground_station: 地面站位置
            duration: 计算时长 (秒)
            min_elevation: 最小仰角 (度)
            time_step: 时间步长 (秒)
            
        Returns:
            可见时间窗口列表 [(start_time, end_time), ...]
        """
        access_windows = []
        current_time = orbit_params.epoch
        
        steps = int(duration / time_step)
        window_start = None
        
        for i in range(steps + 1):
            time_julian = current_time + i * time_step / 86400
            
            # 计算卫星状态
            state = self.propagate_orbit(
                self.kepler_to_cartesian(orbit_params),
                time_julian
            )
            
            # 检查可见性
            is_visible, elevation, azimuth = self.calculate_visibility(
                state, ground_station, min_elevation
            )
            
            if is_visible and window_start is None:
                # 可见窗口开始
                window_start = time_julian
            elif not is_visible and window_start is not None:
                # 可见窗口结束
                access_windows.append((window_start, time_julian))
                window_start = None
        
        # 处理在计算结束时仍然可见的情况
        if window_start is not None:
            access_windows.append((window_start, current_time + duration / 86400))
        
        return access_windows
    
    def get_calculation_statistics(self) -> dict:
        """获取计算统计信息"""
        return {
            'total_calculations': self.calculation_count,
            'cache_size': len(self.cache)
        } 