#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卫星模拟器
包含离散事件仿真引擎和网络仿真功能
"""

import heapq
import threading
import time
import queue
import socket
import struct
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import random
import json

from utils.logger import setup_logger


class EventType(Enum):
    """事件类型枚举"""
    PACKET_SEND = "packet_send"
    PACKET_RECEIVE = "packet_receive"
    LINK_UP = "link_up"
    LINK_DOWN = "link_down"
    TOPOLOGY_UPDATE = "topology_update"
    ROUTING_UPDATE = "routing_update"
    SIMULATION_END = "simulation_end"


@dataclass
class SimulationEvent:
    """仿真事件数据结构"""
    timestamp: float
    event_type: EventType
    node_id: int
    data: Dict[str, Any] = field(default_factory=dict)
    priority: int = 0
    
    def __lt__(self, other):
        if self.timestamp != other.timestamp:
            return self.timestamp < other.timestamp
        return self.priority < other.priority


@dataclass
class NetworkPacket:
    """网络数据包"""
    packet_id: str
    source: int
    destination: int
    payload: bytes
    size: int
    timestamp: float
    ttl: int = 64
    path: List[int] = field(default_factory=list)


@dataclass
class LinkMetrics:
    """链路指标"""
    bandwidth: float  # Mbps
    delay: float  # ms
    loss_rate: float  # 0-1
    jitter: float  # ms
    utilization: float = 0.0
    packets_sent: int = 0
    packets_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0


class DiscreteEventEngine:
    """离散事件仿真引擎"""
    
    def __init__(self):
        self.logger = setup_logger("DiscreteEventEngine")
        self.event_queue = []
        self.current_time = 0.0
        self.running = False
        self.event_handlers: Dict[EventType, List[Callable]] = {}
        self.statistics = {
            'events_processed': 0,
            'packets_transmitted': 0,
            'simulation_time': 0.0
        }
        
    def schedule_event(self, event: SimulationEvent):
        """调度事件"""
        heapq.heappush(self.event_queue, event)
        
    def register_handler(self, event_type: EventType, handler: Callable):
        """注册事件处理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        
    def run_simulation(self, duration: float = None, real_time: bool = False):
        """运行仿真"""
        self.running = True
        self.logger.info(f"开始仿真，持续时间: {duration}s, 实时模式: {real_time}")
        
        simulation_start = time.time()
        
        while self.running and self.event_queue:
            # 获取下一个事件
            event = heapq.heappop(self.event_queue)
            
            # 检查仿真时间限制
            if duration and event.timestamp > duration:
                break
            
            # 实时仿真模式下的时间同步
            if real_time:
                real_time_elapsed = time.time() - simulation_start
                sim_time_elapsed = event.timestamp
                
                if sim_time_elapsed > real_time_elapsed:
                    time.sleep(sim_time_elapsed - real_time_elapsed)
            
            # 更新当前仿真时间
            self.current_time = event.timestamp
            
            # 处理事件
            self._process_event(event)
            
            self.statistics['events_processed'] += 1
        
        self.statistics['simulation_time'] = self.current_time
        self.logger.info(f"仿真结束，处理了 {self.statistics['events_processed']} 个事件")
        
    def stop_simulation(self):
        """停止仿真"""
        self.running = False
        
    def _process_event(self, event: SimulationEvent):
        """处理单个事件"""
        try:
            handlers = self.event_handlers.get(event.event_type, [])
            for handler in handlers:
                handler(event)
        except Exception as e:
            self.logger.error(f"处理事件失败: {e}")
            
    def get_current_time(self) -> float:
        """获取当前仿真时间"""
        return self.current_time


class NetworkNode:
    """网络节点（卫星或地面站）"""
    
    def __init__(self, node_id: int, node_type: str = "satellite"):
        self.node_id = node_id
        self.node_type = node_type
        self.logger = setup_logger(f"Node-{node_id}")
        
        # 网络接口
        self.interfaces: Dict[int, LinkMetrics] = {}
        
        # 路由表
        self.routing_table: Dict[int, int] = {}  # destination -> next_hop
        
        # 数据包缓冲区
        self.packet_buffer = queue.Queue(maxsize=1000)
        
        # 统计信息
        self.stats = {
            'packets_sent': 0,
            'packets_received': 0,
            'packets_dropped': 0,
            'bytes_sent': 0,
            'bytes_received': 0
        }
        
    def add_interface(self, neighbor_id: int, metrics: LinkMetrics):
        """添加网络接口"""
        self.interfaces[neighbor_id] = metrics
        
    def remove_interface(self, neighbor_id: int):
        """删除网络接口"""
        if neighbor_id in self.interfaces:
            del self.interfaces[neighbor_id]
            
    def update_routing_table(self, routing_table: Dict[int, int]):
        """更新路由表"""
        self.routing_table = routing_table.copy()
        
    def send_packet(self, packet: NetworkPacket, engine: DiscreteEventEngine):
        """发送数据包"""
        try:
            # 查找下一跳
            next_hop = self.routing_table.get(packet.destination)
            
            if next_hop is None:
                self.stats['packets_dropped'] += 1
                self.logger.warning(f"无法路由到目标 {packet.destination}")
                return
            
            # 检查接口是否存在
            if next_hop not in self.interfaces:
                self.stats['packets_dropped'] += 1
                self.logger.warning(f"接口 {next_hop} 不存在")
                return
            
            link_metrics = self.interfaces[next_hop]
            
            # 计算传输延迟
            transmission_delay = (packet.size * 8) / (link_metrics.bandwidth * 1e6)  # 秒
            propagation_delay = link_metrics.delay / 1000  # 转换为秒
            total_delay = transmission_delay + propagation_delay
            
            # 模拟丢包
            if random.random() < link_metrics.loss_rate:
                self.stats['packets_dropped'] += 1
                return
            
            # 调度接收事件
            receive_time = engine.get_current_time() + total_delay
            receive_event = SimulationEvent(
                timestamp=receive_time,
                event_type=EventType.PACKET_RECEIVE,
                node_id=next_hop,
                data={'packet': packet, 'from_node': self.node_id}
            )
            engine.schedule_event(receive_event)
            
            # 更新统计
            self.stats['packets_sent'] += 1
            self.stats['bytes_sent'] += packet.size
            link_metrics.packets_sent += 1
            link_metrics.bytes_sent += packet.size
            
            # 添加到路径
            packet.path.append(self.node_id)
            
        except Exception as e:
            self.logger.error(f"发送数据包失败: {e}")
            
    def receive_packet(self, packet: NetworkPacket, engine: DiscreteEventEngine):
        """接收数据包"""
        try:
            self.stats['packets_received'] += 1
            self.stats['bytes_received'] += packet.size
            
            # 检查是否是目标节点
            if packet.destination == self.node_id:
                self.logger.debug(f"数据包 {packet.packet_id} 已到达目标")
                packet.path.append(self.node_id)
                return
            
            # 转发数据包
            packet.ttl -= 1
            if packet.ttl <= 0:
                self.stats['packets_dropped'] += 1
                self.logger.warning(f"数据包 {packet.packet_id} TTL过期")
                return
            
            # 转发
            self.send_packet(packet, engine)
            
        except Exception as e:
            self.logger.error(f"接收数据包失败: {e}")


class TrafficGenerator:
    """流量生成器"""
    
    def __init__(self, engine: DiscreteEventEngine):
        self.engine = engine
        self.logger = setup_logger("TrafficGenerator")
        self.packet_counter = 0
        
    def generate_traffic(self, source: int, destination: int, 
                        packet_size: int = 1500, interval: float = 0.1,
                        duration: float = 10.0):
        """生成流量"""
        start_time = self.engine.get_current_time()
        
        for i in range(int(duration / interval)):
            send_time = start_time + i * interval
            
            packet = NetworkPacket(
                packet_id=f"pkt_{self.packet_counter}",
                source=source,
                destination=destination,
                payload=b'x' * packet_size,
                size=packet_size,
                timestamp=send_time
            )
            
            send_event = SimulationEvent(
                timestamp=send_time,
                event_type=EventType.PACKET_SEND,
                node_id=source,
                data={'packet': packet}
            )
            
            self.engine.schedule_event(send_event)
            self.packet_counter += 1


class PacketConverter:
    """数据包转换器 - 用于半实物接入"""
    
    def __init__(self, tap_interface: str = "tap0"):
        self.tap_interface = tap_interface
        self.logger = setup_logger("PacketConverter")
        self.running = False
        self.socket = None
        self.receive_thread = None
        
    def start(self):
        """启动数据包转换器"""
        try:
            # 创建TAP设备连接（模拟实现）
            self.logger.info(f"启动数据包转换器，接口: {self.tap_interface}")
            self.running = True
            
            # 启动接收线程
            self.receive_thread = threading.Thread(target=self._receive_loop)
            self.receive_thread.daemon = True
            self.receive_thread.start()
            
        except Exception as e:
            self.logger.error(f"启动数据包转换器失败: {e}")
            
    def stop(self):
        """停止数据包转换器"""
        self.running = False
        if self.receive_thread:
            self.receive_thread.join()
        if self.socket:
            self.socket.close()
        self.logger.info("数据包转换器已停止")
        
    def _receive_loop(self):
        """接收循环"""
        while self.running:
            try:
                # 模拟从TAP接口接收数据包
                time.sleep(0.1)
                # 实际实现中会从TAP设备读取真实网络数据包
            except Exception as e:
                self.logger.error(f"接收数据包失败: {e}")
                
    def inject_packet(self, packet: NetworkPacket):
        """注入数据包到仿真网络"""
        self.logger.debug(f"注入数据包 {packet.packet_id} 到仿真网络")
        
    def extract_packet(self, packet: NetworkPacket):
        """从仿真网络提取数据包到真实网络"""
        self.logger.debug(f"提取数据包 {packet.packet_id} 到真实网络")


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.logger = setup_logger("PerformanceMonitor")
        self.metrics = {
            'throughput': [],
            'delay': [],
            'packet_loss': [],
            'jitter': [],
            'link_utilization': []
        }
        
    def record_packet_metrics(self, packet: NetworkPacket, delay: float):
        """记录数据包指标"""
        self.metrics['delay'].append(delay)
        
    def record_link_metrics(self, link_id: str, utilization: float):
        """记录链路指标"""
        self.metrics['link_utilization'].append(utilization)
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {}
        
        for metric_name, values in self.metrics.items():
            if values:
                stats[metric_name] = {
                    'mean': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'count': len(values)
                }
            else:
                stats[metric_name] = {'mean': 0, 'min': 0, 'max': 0, 'count': 0}
                
        return stats


class SatelliteSimulator:
    """卫星模拟器主类"""
    
    def __init__(self, config, topology_controller, path_calculator, communication_module):
        self.config = config
        self.topology_controller = topology_controller
        self.path_calculator = path_calculator
        self.communication_module = communication_module
        
        self.logger = setup_logger("SatelliteSimulator")
        
        # 核心组件
        self.event_engine = DiscreteEventEngine()
        self.traffic_generator = TrafficGenerator(self.event_engine)
        self.packet_converter = PacketConverter()
        self.performance_monitor = PerformanceMonitor()
        
        # 网络节点
        self.nodes: Dict[int, Any] = {}
        
        # 仿真控制
        self.simulation_thread = None
        self.running = False
        self.simulation_mode = "non_realtime"  # "realtime" or "non_realtime"
        
        # 注册事件处理器
        self._register_event_handlers()
        
        self.logger.info("卫星模拟器初始化完成")
        
    def _register_event_handlers(self):
        """注册事件处理器"""
        self.event_engine.register_handler(EventType.PACKET_SEND, self._handle_packet_send)
        self.event_engine.register_handler(EventType.PACKET_RECEIVE, self._handle_packet_receive)
        self.event_engine.register_handler(EventType.LINK_UP, self._handle_link_up)
        self.event_engine.register_handler(EventType.LINK_DOWN, self._handle_link_down)
        self.event_engine.register_handler(EventType.TOPOLOGY_UPDATE, self._handle_topology_update)
        
    def start(self):
        """启动模拟器"""
        if self.running:
            return
            
        self.running = True
        self.logger.info("启动卫星模拟器...")
        
        # 启动数据包转换器
        self.packet_converter.start()
        
        # 启动仿真线程
        self.simulation_thread = threading.Thread(target=self._simulation_loop)
        self.simulation_thread.daemon = True
        self.simulation_thread.start()
        
        self.logger.info("卫星模拟器已启动")
        
    def stop(self):
        """停止模拟器"""
        self.running = False
        self.event_engine.stop_simulation()
        
        if self.simulation_thread:
            self.simulation_thread.join()
            
        self.packet_converter.stop()
        
        self.logger.info("卫星模拟器已停止")
        
    def _simulation_loop(self):
        """仿真主循环"""
        while self.running:
            try:
                # 获取最新的网络拓扑
                topology = self.topology_controller.generate_network_topology()
                self._update_network_topology(topology)
                
                # 运行一个时间片的仿真
                self.event_engine.run_simulation(
                    duration=1.0,  # 1秒时间片
                    real_time=(self.simulation_mode == "realtime")
                )
                
                if self.simulation_mode == "non_realtime":
                    time.sleep(0.1)  # 非实时模式下的小延迟
                    
            except Exception as e:
                self.logger.error(f"仿真循环错误: {e}")
                
    def _update_network_topology(self, topology: Dict):
        """更新网络拓扑"""
        try:
            # 更新节点
            satellites = topology.get('satellites', [])
            for sat in satellites:
                node_id = sat['id']
                if node_id not in self.nodes:
                    self.nodes[node_id] = {'id': node_id, 'type': 'satellite'}
            
            # 更新链路
            inter_sat_links = topology.get('inter_satellite_links', [])
            for source, dest in inter_sat_links:
                if source in self.nodes and dest in self.nodes:
                    # 创建链路指标
                    metrics = LinkMetrics(
                        bandwidth=random.uniform(100, 1000),  # 100-1000 Mbps
                        delay=random.uniform(1, 50),  # 1-50 ms
                        loss_rate=random.uniform(0, 0.01),  # 0-1% 丢包率
                        jitter=random.uniform(0, 5)  # 0-5 ms抖动
                    )
                    
                    self.nodes[source]['interfaces'] = self.nodes[source].get('interfaces', {})
                    self.nodes[source]['interfaces'][dest] = metrics
                    self.nodes[dest]['interfaces'] = self.nodes[dest].get('interfaces', {})
                    self.nodes[dest]['interfaces'][source] = metrics
            
            # 更新路径计算器的拓扑
            self.path_calculator.update_network_topology(topology)
            
            # 更新所有节点的路由表
            self._update_routing_tables()
            
        except Exception as e:
            self.logger.error(f"更新网络拓扑失败: {e}")
            
    def _update_routing_tables(self):
        """更新所有节点的路由表"""
        for node_id, node in self.nodes.items():
            routing_table = {}
            
            # 为每个目标节点计算路由
            for dest_id in self.nodes.keys():
                if dest_id != node_id:
                    result = self.path_calculator.calculate_path(node_id, dest_id)
                    if result.path and len(result.path) > 1:
                        next_hop = result.path[1]  # 下一跳节点
                        routing_table[dest_id] = next_hop
            
            node['routing_table'] = routing_table
            
    def _handle_packet_send(self, event: SimulationEvent):
        """处理数据包发送事件"""
        packet = event.data.get('packet')
        node = self.nodes.get(event.node_id)
        
        if packet and node:
            node['interfaces'] = node.get('interfaces', {})
            for neighbor_id, metrics in node['interfaces'].items():
                self.nodes[neighbor_id]['interfaces'] = self.nodes[neighbor_id].get('interfaces', {})
                self.nodes[neighbor_id]['interfaces'][event.node_id] = metrics
            
            self.send_packet(packet, self.event_engine)
            
    def _handle_packet_receive(self, event: SimulationEvent):
        """处理数据包接收事件"""
        packet = event.data.get('packet')
        node = self.nodes.get(event.node_id)
        
        if packet and node:
            self.receive_packet(packet, self.event_engine)
            
    def _handle_link_up(self, event: SimulationEvent):
        """处理链路恢复事件"""
        self.logger.info(f"链路恢复: {event.data}")
        
    def _handle_link_down(self, event: SimulationEvent):
        """处理链路故障事件"""
        self.logger.info(f"链路故障: {event.data}")
        
    def _handle_topology_update(self, event: SimulationEvent):
        """处理拓扑更新事件"""
        self.logger.info("网络拓扑已更新")
        
    def generate_traffic(self, source: int, destination: int, 
                        duration: float = 10.0, packet_rate: float = 10.0):
        """生成测试流量"""
        if source not in self.nodes or destination not in self.nodes:
            self.logger.error(f"无效的源或目标节点: {source} -> {destination}")
            return
            
        interval = 1.0 / packet_rate
        self.traffic_generator.generate_traffic(
            source, destination, duration=duration, interval=interval
        )
        
        self.logger.info(f"已生成从 {source} 到 {destination} 的流量")
        
    def set_simulation_mode(self, mode: str):
        """设置仿真模式"""
        if mode in ["realtime", "non_realtime"]:
            self.simulation_mode = mode
            self.logger.info(f"仿真模式设置为: {mode}")
        
    def get_active_links(self) -> int:
        """获取活跃链路数"""
        total_links = 0
        for node in self.nodes.values():
            total_links += len(node.get('interfaces', {}))
        return total_links // 2  # 双向链路计为一条
        
    def get_simulation_time(self) -> float:
        """获取仿真时间"""
        return self.event_engine.get_current_time()
        
    def get_network_statistics(self) -> Dict[str, Any]:
        """获取网络统计信息"""
        total_stats = {
            'nodes': len(self.nodes),
            'total_packets_sent': 0,
            'total_packets_received': 0,
            'total_packets_dropped': 0,
            'total_bytes_sent': 0,
            'total_bytes_received': 0
        }
        
        for node in self.nodes.values():
            total_stats['total_packets_sent'] += node['stats']['packets_sent']
            total_stats['total_packets_received'] += node['stats']['packets_received']
            total_stats['total_packets_dropped'] += node['stats']['packets_dropped']
            total_stats['total_bytes_sent'] += node['stats']['bytes_sent']
            total_stats['total_bytes_received'] += node['stats']['bytes_received']
        
        # 添加性能监控数据
        perf_stats = self.performance_monitor.get_statistics()
        total_stats.update(perf_stats)
        
        # 添加仿真引擎统计
        total_stats.update(self.event_engine.statistics)
        
        return total_stats
        
    def export_simulation_results(self, filename: str = None) -> str:
        """导出仿真结果"""
        if filename is None:
            filename = f"simulation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            results = {
                'simulation_info': {
                    'timestamp': datetime.now().isoformat(),
                    'simulation_time': self.get_simulation_time(),
                    'mode': self.simulation_mode
                },
                'network_statistics': self.get_network_statistics(),
                'node_statistics': {
                    node_id: node['stats'] for node_id, node in self.nodes.items()
                }
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"仿真结果已导出到: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"导出仿真结果失败: {e}")
            return "" 