{"version": 3, "names": ["_index", "require", "isNodesEquivalent", "a", "b", "type", "fields", "Object", "keys", "NODE_FIELDS", "visitorKeys", "VISITOR_KEYS", "field", "val_a", "val_b", "Array", "isArray", "length", "i", "includes", "key"], "sources": ["../../src/validators/isNodesEquivalent.ts"], "sourcesContent": ["import { NODE_FIELDS, VISITOR_KEYS } from \"../definitions/index.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Check if two nodes are equivalent\n */\nexport default function isNodesEquivalent<T extends Partial<t.Node>>(\n  a: T,\n  b: any,\n): b is T {\n  if (\n    typeof a !== \"object\" ||\n    typeof b !== \"object\" ||\n    a == null ||\n    b == null\n  ) {\n    return a === b;\n  }\n\n  if (a.type !== b.type) {\n    return false;\n  }\n\n  const fields = Object.keys(NODE_FIELDS[a.type] || a.type);\n  const visitorKeys = VISITOR_KEYS[a.type];\n\n  for (const field of fields) {\n    const val_a =\n      // @ts-expect-error field must present in a\n      a[field];\n    const val_b = b[field];\n    if (typeof val_a !== typeof val_b) {\n      return false;\n    }\n    if (val_a == null && val_b == null) {\n      continue;\n    } else if (val_a == null || val_b == null) {\n      return false;\n    }\n\n    if (Array.isArray(val_a)) {\n      if (!Array.isArray(val_b)) {\n        return false;\n      }\n      if (val_a.length !== val_b.length) {\n        return false;\n      }\n\n      for (let i = 0; i < val_a.length; i++) {\n        if (!isNodesEquivalent(val_a[i], val_b[i])) {\n          return false;\n        }\n      }\n      continue;\n    }\n\n    if (typeof val_a === \"object\" && !visitorKeys?.includes(field)) {\n      for (const key of Object.keys(val_a)) {\n        if (val_a[key] !== val_b[key]) {\n          return false;\n        }\n      }\n      continue;\n    }\n\n    if (!isNodesEquivalent(val_a, val_b)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAMe,SAASC,iBAAiBA,CACvCC,CAAI,EACJC,CAAM,EACE;EACR,IACE,OAAOD,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,IACrBD,CAAC,IAAI,IAAI,IACTC,CAAC,IAAI,IAAI,EACT;IACA,OAAOD,CAAC,KAAKC,CAAC;EAChB;EAEA,IAAID,CAAC,CAACE,IAAI,KAAKD,CAAC,CAACC,IAAI,EAAE;IACrB,OAAO,KAAK;EACd;EAEA,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACC,kBAAW,CAACN,CAAC,CAACE,IAAI,CAAC,IAAIF,CAAC,CAACE,IAAI,CAAC;EACzD,MAAMK,WAAW,GAAGC,mBAAY,CAACR,CAAC,CAACE,IAAI,CAAC;EAExC,KAAK,MAAMO,KAAK,IAAIN,MAAM,EAAE;IAC1B,MAAMO,KAAK,GAETV,CAAC,CAACS,KAAK,CAAC;IACV,MAAME,KAAK,GAAGV,CAAC,CAACQ,KAAK,CAAC;IACtB,IAAI,OAAOC,KAAK,KAAK,OAAOC,KAAK,EAAE;MACjC,OAAO,KAAK;IACd;IACA,IAAID,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE;MAClC;IACF,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE;MACzC,OAAO,KAAK;IACd;IAEA,IAAIC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACxB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;QACzB,OAAO,KAAK;MACd;MACA,IAAID,KAAK,CAACI,MAAM,KAAKH,KAAK,CAACG,MAAM,EAAE;QACjC,OAAO,KAAK;MACd;MAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;QACrC,IAAI,CAAChB,iBAAiB,CAACW,KAAK,CAACK,CAAC,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC,CAAC,EAAE;UAC1C,OAAO,KAAK;QACd;MACF;MACA;IACF;IAEA,IAAI,OAAOL,KAAK,KAAK,QAAQ,IAAI,EAACH,WAAW,YAAXA,WAAW,CAAES,QAAQ,CAACP,KAAK,CAAC,GAAE;MAC9D,KAAK,MAAMQ,GAAG,IAAIb,MAAM,CAACC,IAAI,CAACK,KAAK,CAAC,EAAE;QACpC,IAAIA,KAAK,CAACO,GAAG,CAAC,KAAKN,KAAK,CAACM,GAAG,CAAC,EAAE;UAC7B,OAAO,KAAK;QACd;MACF;MACA;IACF;IAEA,IAAI,CAAClB,iBAAiB,CAACW,KAAK,EAAEC,KAAK,CAAC,EAAE;MACpC,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}