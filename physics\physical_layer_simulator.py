#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
物理层仿真模块
模拟卫星通信的物理层特性，包括信道建模、链路预算计算等
"""

import math
import random
import threading
import time
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import numpy as np

from utils.logger import setup_logger


@dataclass
class LinkBudget:
    """链路预算结果"""
    transmit_power: float  # 发射功率 (dBm)
    transmit_gain: float   # 发射天线增益 (dBi)
    path_loss: float       # 路径损耗 (dB)
    receive_gain: float    # 接收天线增益 (dBi)
    noise_power: float     # 噪声功率 (dBm)
    snr: float            # 信噪比 (dB)
    capacity: float       # 信道容量 (Mbps)
    link_margin: float    # 链路余量 (dB)


@dataclass
class ChannelState:
    """信道状态"""
    link_id: str
    frequency: float      # 频率 (Hz)
    bandwidth: float      # 带宽 (Hz)
    attenuation: float    # 衰减 (dB)
    doppler_shift: float  # 多普勒频移 (Hz)
    delay: float          # 传播延迟 (ms)
    is_blocked: bool      # 是否被遮挡
    quality_factor: float # 质量因子 (0-1)


class PropagationModel:
    """传播模型"""
    
    def __init__(self):
        self.logger = setup_logger("PropagationModel")
        # 物理常量
        self.light_speed = 2.998e8  # 光速 (m/s)
        self.earth_radius = 6371.0  # 地球半径 (km)
    
    def calculate_free_space_loss(self, distance: float, frequency: float) -> float:
        """
        计算自由空间传播损耗
        
        Args:
            distance: 距离 (km)
            frequency: 频率 (Hz)
        
        Returns:
            路径损耗 (dB)
        """
        if distance <= 0 or frequency <= 0:
            return float('inf')
        
        # 自由空间路径损耗公式: FSPL = 20*log10(d) + 20*log10(f) + 20*log10(4π/c)
        fspl = 20 * math.log10(distance * 1000) + 20 * math.log10(frequency) + \
               20 * math.log10(4 * math.pi / self.light_speed)
        
        return fspl
    
    def calculate_atmospheric_loss(self, elevation_angle: float, frequency: float) -> float:
        """
        计算大气损耗
        
        Args:
            elevation_angle: 仰角 (度)
            frequency: 频率 (Hz)
        
        Returns:
            大气损耗 (dB)
        """
        if elevation_angle <= 0:
            return 50.0  # 地平线以下，损耗很大
        
        # 简化的大气损耗模型
        zenith_angle = 90 - elevation_angle
        atmospheric_loss = 0.1 + 0.05 * (frequency / 1e9) * (zenith_angle / 90.0)
        
        return atmospheric_loss
    
    def calculate_rain_attenuation(self, rain_rate: float, frequency: float, distance: float) -> float:
        """
        计算雨衰
        
        Args:
            rain_rate: 降雨率 (mm/h)
            frequency: 频率 (Hz)
            distance: 距离 (km)
        
        Returns:
            雨衰 (dB)
        """
        if rain_rate <= 0:
            return 0.0
        
        # 简化的雨衰模型 (ITU-R P.838)
        freq_ghz = frequency / 1e9
        
        if freq_ghz < 1:
            return 0.0
        
        # 近似系数
        k = 0.0001 * (freq_ghz ** 2.3)
        alpha = 1.0
        
        specific_attenuation = k * (rain_rate ** alpha)
        total_attenuation = specific_attenuation * distance
        
        return total_attenuation
    
    def calculate_doppler_shift(self, relative_velocity: float, frequency: float) -> float:
        """
        计算多普勒频移
        
        Args:
            relative_velocity: 相对速度 (m/s)
            frequency: 频率 (Hz)
        
        Returns:
            多普勒频移 (Hz)
        """
        doppler_shift = frequency * relative_velocity / self.light_speed
        return doppler_shift
    
    def calculate_propagation_delay(self, distance: float) -> float:
        """
        计算传播延迟
        
        Args:
            distance: 距离 (km)
        
        Returns:
            传播延迟 (ms)
        """
        delay = (distance * 1000) / self.light_speed * 1000  # 转换为毫秒
        return delay


class AntennaModel:
    """天线模型"""
    
    def __init__(self):
        self.logger = setup_logger("AntennaModel")
    
    def calculate_antenna_gain(self, antenna_type: str, frequency: float, 
                             diameter: float = None, efficiency: float = 0.6) -> float:
        """
        计算天线增益
        
        Args:
            antenna_type: 天线类型 ("parabolic", "patch", "dipole")
            frequency: 频率 (Hz)
            diameter: 天线直径 (m)，抛物面天线需要
            efficiency: 天线效率
        
        Returns:
            天线增益 (dBi)
        """
        if antenna_type == "parabolic" and diameter:
            # 抛物面天线增益
            wavelength = self.light_speed / frequency
            gain_linear = efficiency * (math.pi * diameter / wavelength) ** 2
            gain_db = 10 * math.log10(gain_linear)
            return gain_db
        
        elif antenna_type == "patch":
            # 贴片天线典型增益
            return 6.0 + random.uniform(-1, 1)
        
        elif antenna_type == "dipole":
            # 偶极子天线增益
            return 2.15
        
        else:
            # 默认增益
            return 10.0
    
    def calculate_beam_pattern(self, antenna_type: str, azimuth: float, 
                             elevation: float) -> float:
        """
        计算天线方向图
        
        Args:
            antenna_type: 天线类型
            azimuth: 方位角 (度)
            elevation: 仰角 (度)
        
        Returns:
            相对增益 (dB)
        """
        if antenna_type == "parabolic":
            # 简化的抛物面天线方向图
            off_axis_angle = math.sqrt(azimuth**2 + elevation**2)
            if off_axis_angle < 1.0:
                return 0.0  # 主瓣
            elif off_axis_angle < 5.0:
                return -3.0 * off_axis_angle  # 快速下降
            else:
                return -20.0  # 旁瓣
        
        else:
            # 简化的全向天线
            return 0.0


class NoiseModel:
    """噪声模型"""
    
    def __init__(self):
        self.logger = setup_logger("NoiseModel")
        self.boltzmann_constant = 1.38e-23  # 玻尔兹曼常数
    
    def calculate_thermal_noise(self, temperature: float, bandwidth: float) -> float:
        """
        计算热噪声
        
        Args:
            temperature: 噪声温度 (K)
            bandwidth: 带宽 (Hz)
        
        Returns:
            噪声功率 (dBm)
        """
        noise_power_watts = self.boltzmann_constant * temperature * bandwidth
        noise_power_dbm = 10 * math.log10(noise_power_watts * 1000)
        return noise_power_dbm
    
    def calculate_system_noise_temperature(self, antenna_temp: float, 
                                         lna_temp: float, lna_gain: float) -> float:
        """
        计算系统噪声温度
        
        Args:
            antenna_temp: 天线噪声温度 (K)
            lna_temp: 低噪声放大器噪声温度 (K)
            lna_gain: 低噪声放大器增益 (线性)
        
        Returns:
            系统噪声温度 (K)
        """
        system_temp = antenna_temp + lna_temp / lna_gain
        return system_temp


class PhysicalLayerSimulator:
    """物理层仿真器主类"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("PhysicalLayerSimulator")
        
        # 模型组件
        self.propagation_model = PropagationModel()
        self.antenna_model = AntennaModel()
        self.noise_model = NoiseModel()
        
        # 仿真参数
        self.frequency = getattr(config.physics_config, 'frequency', 2.4e9)
        self.bandwidth = getattr(config.physics_config, 'bandwidth', 10e6)
        self.tx_power = getattr(config.physics_config, 'tx_power', 30.0)
        self.noise_figure = getattr(config.physics_config, 'noise_figure', 3.0)
        self.antenna_gain = getattr(config.physics_config, 'antenna_gain', 15.0)
        
        # 环境参数
        self.rain_rate = 0.0  # mm/h
        self.atmospheric_conditions = "clear"
        
        # 仿真状态
        self.active_links: Dict[str, ChannelState] = {}
        self.link_budgets: Dict[str, LinkBudget] = {}
        
        # 线程控制
        self.running = False
        self.update_thread = None
        
        self.logger.info("物理层仿真器初始化完成")
    
    def calculate_link_budget(self, source_pos: Tuple[float, float, float],
                            dest_pos: Tuple[float, float, float],
                            link_id: str = None) -> LinkBudget:
        """
        计算链路预算
        
        Args:
            source_pos: 发射端位置 (x, y, z) km
            dest_pos: 接收端位置 (x, y, z) km
            link_id: 链路标识
        
        Returns:
            LinkBudget: 链路预算结果
        """
        try:
            # 计算距离
            distance = math.sqrt(sum((a - b)**2 for a, b in zip(source_pos, dest_pos)))
            
            # 计算路径损耗
            free_space_loss = self.propagation_model.calculate_free_space_loss(
                distance, self.frequency
            )
            
            # 计算大气损耗（简化为固定值）
            atmospheric_loss = 2.0  # dB
            
            # 计算雨衰
            rain_loss = self.propagation_model.calculate_rain_attenuation(
                self.rain_rate, self.frequency, distance
            )
            
            # 总路径损耗
            total_path_loss = free_space_loss + atmospheric_loss + rain_loss
            
            # 天线增益
            tx_gain = self.antenna_gain
            rx_gain = self.antenna_gain
            
            # 噪声功率
            noise_temp = 290.0  # K
            noise_power = self.noise_model.calculate_thermal_noise(
                noise_temp, self.bandwidth
            ) + self.noise_figure
            
            # 接收功率
            rx_power = self.tx_power + tx_gain - total_path_loss + rx_gain
            
            # 信噪比
            snr = rx_power - noise_power
            
            # 信道容量 (Shannon公式)
            snr_linear = 10 ** (snr / 10)
            capacity = self.bandwidth * math.log2(1 + snr_linear) / 1e6  # Mbps
            
            # 链路余量
            required_snr = 10.0  # dB，所需最小信噪比
            link_margin = snr - required_snr
            
            budget = LinkBudget(
                transmit_power=self.tx_power,
                transmit_gain=tx_gain,
                path_loss=total_path_loss,
                receive_gain=rx_gain,
                noise_power=noise_power,
                snr=snr,
                capacity=capacity,
                link_margin=link_margin
            )
            
            if link_id:
                self.link_budgets[link_id] = budget
            
            return budget
            
        except Exception as e:
            self.logger.error(f"链路预算计算失败: {e}")
            return LinkBudget(0, 0, float('inf'), 0, 0, -100, 0, -100)
    
    def update_channel_state(self, link_id: str, source_pos: Tuple[float, float, float],
                           dest_pos: Tuple[float, float, float],
                           relative_velocity: float = 0.0) -> ChannelState:
        """
        更新信道状态
        
        Args:
            link_id: 链路标识
            source_pos: 发射端位置
            dest_pos: 接收端位置
            relative_velocity: 相对速度 (m/s)
        
        Returns:
            ChannelState: 信道状态
        """
        try:
            # 计算距离和延迟
            distance = math.sqrt(sum((a - b)**2 for a, b in zip(source_pos, dest_pos)))
            delay = self.propagation_model.calculate_propagation_delay(distance)
            
            # 计算多普勒频移
            doppler_shift = self.propagation_model.calculate_doppler_shift(
                relative_velocity, self.frequency
            )
            
            # 计算链路预算
            budget = self.calculate_link_budget(source_pos, dest_pos, link_id)
            
            # 判断链路质量
            quality_factor = min(1.0, max(0.0, (budget.snr + 10) / 30.0))
            is_blocked = budget.link_margin < -5.0  # 链路余量过低则认为阻断
            
            channel_state = ChannelState(
                link_id=link_id,
                frequency=self.frequency,
                bandwidth=self.bandwidth,
                attenuation=budget.path_loss,
                doppler_shift=doppler_shift,
                delay=delay,
                is_blocked=is_blocked,
                quality_factor=quality_factor
            )
            
            self.active_links[link_id] = channel_state
            
            return channel_state
            
        except Exception as e:
            self.logger.error(f"信道状态更新失败: {e}")
            return ChannelState(link_id, self.frequency, self.bandwidth, 
                              float('inf'), 0, 1000, True, 0.0)
    
    def get_channel_capacity(self, link_id: str) -> float:
        """获取信道容量"""
        budget = self.link_budgets.get(link_id)
        if budget:
            return budget.capacity
        return 0.0
    
    def get_link_quality(self, link_id: str) -> float:
        """获取链路质量"""
        channel = self.active_links.get(link_id)
        if channel:
            return channel.quality_factor
        return 0.0
    
    def set_environmental_conditions(self, rain_rate: float = 0.0, 
                                   atmospheric_condition: str = "clear"):
        """设置环境条件"""
        self.rain_rate = rain_rate
        self.atmospheric_conditions = atmospheric_condition
        self.logger.info(f"环境条件已更新: 降雨率={rain_rate}mm/h, 大气条件={atmospheric_condition}")
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_links = len(self.active_links)
        blocked_links = sum(1 for channel in self.active_links.values() if channel.is_blocked)
        avg_quality = np.mean([channel.quality_factor for channel in self.active_links.values()]) \
                     if self.active_links else 0.0
        
        total_capacity = sum(self.link_budgets[link_id].capacity 
                           for link_id in self.link_budgets 
                           if not self.active_links.get(link_id, ChannelState("", 0, 0, 0, 0, 0, True, 0)).is_blocked)
        
        return {
            'total_links': total_links,
            'active_links': total_links - blocked_links,
            'blocked_links': blocked_links,
            'average_quality': avg_quality,
            'total_capacity_mbps': total_capacity,
            'frequency_ghz': self.frequency / 1e9,
            'bandwidth_mhz': self.bandwidth / 1e6
        }
    
    def export_link_analysis(self, filename: str = None) -> str:
        """导出链路分析报告"""
        if filename is None:
            filename = f"link_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("SDT物理层链路分析报告\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"生成时间: {datetime.now()}\n")
                f.write(f"频率: {self.frequency/1e9:.2f} GHz\n")
                f.write(f"带宽: {self.bandwidth/1e6:.2f} MHz\n")
                f.write(f"发射功率: {self.tx_power} dBm\n\n")
                
                stats = self.get_statistics()
                f.write("总体统计:\n")
                f.write(f"  总链路数: {stats['total_links']}\n")
                f.write(f"  活跃链路数: {stats['active_links']}\n")
                f.write(f"  阻断链路数: {stats['blocked_links']}\n")
                f.write(f"  平均质量: {stats['average_quality']:.3f}\n")
                f.write(f"  总容量: {stats['total_capacity_mbps']:.2f} Mbps\n\n")
                
                f.write("详细链路信息:\n")
                for link_id, budget in self.link_budgets.items():
                    channel = self.active_links.get(link_id)
                    f.write(f"链路 {link_id}:\n")
                    f.write(f"  路径损耗: {budget.path_loss:.2f} dB\n")
                    f.write(f"  信噪比: {budget.snr:.2f} dB\n")
                    f.write(f"  容量: {budget.capacity:.2f} Mbps\n")
                    f.write(f"  余量: {budget.link_margin:.2f} dB\n")
                    if channel:
                        f.write(f"  延迟: {channel.delay:.2f} ms\n")
                        f.write(f"  质量: {channel.quality_factor:.3f}\n")
                    f.write("\n")
            
            self.logger.info(f"链路分析报告已导出到: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"导出链路分析报告失败: {e}")
            return "" 