#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SDT卫星数字孪生系统 v1.0 启动脚本
自动启动Flask API服务器和Node.js服务器
"""

import os
import sys
import time
import subprocess
import threading
import signal
from pathlib import Path


class SDTSystemLauncher:
    """SDT系统启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.server_dir = self.project_root / "server"
        self.flask_process = None
        self.node_process = None
        self.running = True
        
    def check_dependencies(self):
        """检查依赖是否安装"""
        print("检查系统依赖...")
        
        # 检查Python依赖
        try:
            import numpy
            import flask
            import flask_cors
            print("✓ Python依赖检查通过")
        except ImportError as e:
            print(f"✗ Python依赖缺失: {e}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        # 检查Node.js
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✓ Node.js版本: {result.stdout.strip()}")
            else:
                print("✗ Node.js未安装")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("✗ Node.js未安装或不在PATH中")
            return False
        
        # 检查npm依赖
        package_json = self.server_dir / "package.json"
        node_modules = self.server_dir / "node_modules"
        
        if package_json.exists() and not node_modules.exists():
            print("安装Node.js依赖...")
            try:
                subprocess.run(['npm', 'install'], 
                             cwd=self.server_dir, check=True, timeout=120)
                print("✓ Node.js依赖安装完成")
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                print("✗ Node.js依赖安装失败")
                return False
        else:
            print("✓ Node.js依赖检查通过")
        
        return True
    
    def start_flask_server(self):
        """启动Flask API服务器"""
        print("启动Flask API服务器...")
        
        flask_script = self.server_dir / "flask_app.py"
        if not flask_script.exists():
            print(f"✗ Flask应用文件不存在: {flask_script}")
            return False
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONPATH'] = str(self.project_root)
            env['FLASK_ENV'] = 'development'
            
            self.flask_process = subprocess.Popen(
                [sys.executable, str(flask_script)],
                cwd=self.project_root,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 启动输出监控线程
            threading.Thread(
                target=self._monitor_process_output,
                args=(self.flask_process, "Flask"),
                daemon=True
            ).start()
            
            print("✓ Flask服务器启动中... (端口: 5000)")
            return True
            
        except Exception as e:
            print(f"✗ Flask服务器启动失败: {e}")
            return False
    
    def start_node_server(self):
        """启动Node.js服务器"""
        print("启动Node.js服务器...")
        
        server_script = self.server_dir / "server.js"
        if not server_script.exists():
            print(f"✗ Node.js服务器文件不存在: {server_script}")
            return False
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env['NODE_ENV'] = 'development'
            env['FLASK_API_URL'] = 'http://localhost:5000'
            
            self.node_process = subprocess.Popen(
                ['node', 'server.js'],
                cwd=self.server_dir,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 启动输出监控线程
            threading.Thread(
                target=self._monitor_process_output,
                args=(self.node_process, "Node.js"),
                daemon=True
            ).start()
            
            print("✓ Node.js服务器启动中... (端口: 3000)")
            return True
            
        except Exception as e:
            print(f"✗ Node.js服务器启动失败: {e}")
            return False
    
    def _monitor_process_output(self, process, name):
        """监控进程输出"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line and self.running:
                    print(f"[{name}] {line.rstrip()}")
        except Exception as e:
            if self.running:
                print(f"[{name}] 输出监控错误: {e}")
    
    def wait_for_servers(self):
        """等待服务器启动完成"""
        print("等待服务器启动完成...")
        
        # 等待Flask服务器
        flask_ready = False
        for i in range(30):  # 最多等待30秒
            try:
                import requests
                response = requests.get('http://localhost:5000/api/v1/health', timeout=2)
                if response.status_code == 200:
                    flask_ready = True
                    print("✓ Flask API服务器就绪")
                    break
            except:
                pass
            time.sleep(1)
        
        if not flask_ready:
            print("⚠ Flask服务器启动超时，但可能仍在启动中")
        
        # 等待Node.js服务器
        node_ready = False
        for i in range(30):  # 最多等待30秒
            try:
                import requests
                response = requests.get('http://localhost:3000/health', timeout=2)
                if response.status_code == 200:
                    node_ready = True
                    print("✓ Node.js服务器就绪")
                    break
            except:
                pass
            time.sleep(1)
        
        if not node_ready:
            print("⚠ Node.js服务器启动超时，但可能仍在启动中")
        
        return flask_ready and node_ready
    
    def open_browser(self):
        """打开浏览器"""
        try:
            import webbrowser
            print("正在打开浏览器...")
            webbrowser.open('http://localhost:3000')
            print("✓ 浏览器已打开")
        except Exception as e:
            print(f"⚠ 无法自动打开浏览器: {e}")
            print("请手动访问: http://localhost:3000")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print(f"\n收到信号 {signum}，正在关闭系统...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """关闭系统"""
        print("正在关闭SDT系统...")
        self.running = False
        
        if self.flask_process:
            print("关闭Flask服务器...")
            self.flask_process.terminate()
            try:
                self.flask_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.flask_process.kill()
        
        if self.node_process:
            print("关闭Node.js服务器...")
            self.node_process.terminate()
            try:
                self.node_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.node_process.kill()
        
        print("✓ 系统已关闭")
    
    def run(self):
        """运行系统"""
        print("=" * 60)
        print("SDT卫星数字孪生系统 v1.0 启动器")
        print("=" * 60)
        
        # 检查依赖
        if not self.check_dependencies():
            print("依赖检查失败，请解决后重试")
            return False
        
        # 设置信号处理器
        self.setup_signal_handlers()
        
        # 启动服务器
        if not self.start_flask_server():
            return False
        
        time.sleep(2)  # 给Flask一点启动时间
        
        if not self.start_node_server():
            self.shutdown()
            return False
        
        # 等待服务器就绪
        self.wait_for_servers()
        
        # 打开浏览器
        self.open_browser()
        
        print("\n" + "=" * 60)
        print("SDT系统启动完成！")
        print("Flask API: http://localhost:5000")
        print("Web界面: http://localhost:3000")
        print("按 Ctrl+C 退出系统")
        print("=" * 60)
        
        # 保持运行
        try:
            while self.running:
                time.sleep(1)
                
                # 检查进程状态
                if self.flask_process and self.flask_process.poll() is not None:
                    print("⚠ Flask服务器意外退出")
                    break
                
                if self.node_process and self.node_process.poll() is not None:
                    print("⚠ Node.js服务器意外退出")
                    break
                    
        except KeyboardInterrupt:
            pass
        
        self.shutdown()
        return True


def main():
    """主函数"""
    launcher = SDTSystemLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
