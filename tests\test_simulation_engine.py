import pytest
import time
from engine.simulation_engine import SimulationEngine, SimulationState
from engine.event import Event, EventType

@pytest.fixture
def engine():
    """Pytest fixture for SimulationEngine."""
    return SimulationEngine()

def test_engine_initial_state(engine):
    """Test the initial state of the simulation engine."""
    assert engine.get_state() == SimulationState.IDLE
    assert engine.get_current_time() == 0.0
    assert engine.scheduler.get_queue_size() == 0

def test_run_empty_simulation(engine):
    """Test running a simulation with no events."""
    engine.run(end_time=100)
    assert engine.get_state() == SimulationState.STOPPED
    assert engine.get_current_time() == 0 # No events, time doesn't advance
    assert engine.simulation_stats['total_events_processed'] == 0

def test_run_simulation_with_events(engine):
    """Test a simple simulation run with a few events."""
    handler_calls = []
    def my_handler(event):
        handler_calls.append(event)

    engine.register_event_handler(EventType.TIMEOUT, my_handler)
    
    engine.schedule_event_at("e1", EventType.TIMEOUT, 5.0)
    engine.schedule_event_at("e2", EventType.TIMEOUT, 10.0)
    
    engine.run(end_time=20.0)
    
    assert engine.get_state() == SimulationState.STOPPED
    assert engine.get_current_time() == 10.0
    assert len(handler_calls) == 2
    assert handler_calls[0].event_id == "e1"
    assert handler_calls[1].event_id == "e2"
    assert engine.simulation_stats['total_events_processed'] == 2

def test_pause_and_resume(engine):
    """Test pausing and resuming the simulation."""
    # This is complex to test without a threaded run, 
    # so we'll just check state transitions.
    engine.state = SimulationState.RUNNING # Manually set state for test
    engine.pause()
    assert engine.get_state() == SimulationState.PAUSED
    engine.resume()
    assert engine.get_state() == SimulationState.RUNNING

def test_stop(engine):
    """Test stopping the simulation."""
    engine.state = SimulationState.RUNNING # Manually set state for test
    engine.stop()
    assert engine.get_state() == SimulationState.STOPPED 