"""
网络事件类型模块

定义各种网络相关的具体事件类型和工厂方法。
"""

from typing import Dict, Any, Optional
from .event import NetworkEvent, EventType, EventPriority


class PacketArrivalEvent(NetworkEvent):
    """数据包到达事件"""
    
    def __init__(self, 
                 event_id: str,
                 scheduled_time: float,
                 source_node: str,
                 destination_node: str,
                 packet_id: str,
                 packet_size: int,
                 protocol: str = "TCP",
                 priority: EventPriority = EventPriority.NORMAL,
                 data: Optional[Dict[str, Any]] = None):
        super().__init__(
            event_id=event_id,
            event_type=EventType.PACKET_ARRIVAL,
            scheduled_time=scheduled_time,
            source_node=source_node,
            destination_node=destination_node,
            packet_id=packet_id,
            packet_size=packet_size,
            protocol=protocol,
            priority=priority,
            data=data
        )


class PacketDepartureEvent(NetworkEvent):
    """数据包发送事件"""
    
    def __init__(self, 
                 event_id: str,
                 scheduled_time: float,
                 source_node: str,
                 destination_node: str,
                 packet_id: str,
                 packet_size: int,
                 protocol: str = "TCP",
                 priority: EventPriority = EventPriority.NORMAL,
                 data: Optional[Dict[str, Any]] = None):
        super().__init__(
            event_id=event_id,
            event_type=EventType.PACKET_DEPARTURE,
            scheduled_time=scheduled_time,
            source_node=source_node,
            destination_node=destination_node,
            packet_id=packet_id,
            packet_size=packet_size,
            protocol=protocol,
            priority=priority,
            data=data
        )


class LinkStateEvent(NetworkEvent):
    """链路状态事件（上线/下线）"""
    
    def __init__(self,
                 event_id: str,
                 scheduled_time: float,
                 link_id: str,
                 is_up: bool,
                 node_a: str,
                 node_b: str,
                 priority: EventPriority = EventPriority.HIGH,
                 data: Optional[Dict[str, Any]] = None):
        event_type = EventType.LINK_UP if is_up else EventType.LINK_DOWN
        
        super().__init__(
            event_id=event_id,
            event_type=event_type,
            scheduled_time=scheduled_time,
            source_node=node_a,
            destination_node=node_b,
            priority=priority,
            data=data or {}
        )
        
        self.link_id = link_id
        self.is_up = is_up
        self.set_data('link_id', link_id)
        self.set_data('is_up', is_up)


class RouteUpdateEvent(NetworkEvent):
    """路由更新事件"""
    
    def __init__(self,
                 event_id: str,
                 scheduled_time: float,
                 node_id: str,
                 destination: str,
                 next_hop: str,
                 metric: float = 1.0,
                 priority: EventPriority = EventPriority.NORMAL,
                 data: Optional[Dict[str, Any]] = None):
        super().__init__(
            event_id=event_id,
            event_type=EventType.ROUTE_UPDATE,
            scheduled_time=scheduled_time,
            source_node=node_id,
            destination_node=destination,
            priority=priority,
            data=data or {}
        )
        
        self.next_hop = next_hop
        self.metric = metric
        self.set_data('next_hop', next_hop)
        self.set_data('metric', metric)


# 事件工厂函数
def create_packet_arrival(event_id: str,
                         scheduled_time: float,
                         source: str,
                         destination: str,
                         packet_id: str,
                         packet_size: int,
                         protocol: str = "TCP") -> PacketArrivalEvent:
    """创建数据包到达事件"""
    return PacketArrivalEvent(
        event_id=event_id,
        scheduled_time=scheduled_time,
        source_node=source,
        destination_node=destination,
        packet_id=packet_id,
        packet_size=packet_size,
        protocol=protocol
    )


def create_packet_departure(event_id: str,
                          scheduled_time: float,
                          source: str,
                          destination: str,
                          packet_id: str,
                          packet_size: int,
                          protocol: str = "TCP") -> PacketDepartureEvent:
    """创建数据包发送事件"""
    return PacketDepartureEvent(
        event_id=event_id,
        scheduled_time=scheduled_time,
        source_node=source,
        destination_node=destination,
        packet_id=packet_id,
        packet_size=packet_size,
        protocol=protocol
    )


def create_link_up(event_id: str,
                  scheduled_time: float,
                  link_id: str,
                  node_a: str,
                  node_b: str) -> LinkStateEvent:
    """创建链路上线事件"""
    return LinkStateEvent(
        event_id=event_id,
        scheduled_time=scheduled_time,
        link_id=link_id,
        is_up=True,
        node_a=node_a,
        node_b=node_b
    )


def create_link_down(event_id: str,
                    scheduled_time: float,
                    link_id: str,
                    node_a: str,
                    node_b: str) -> LinkStateEvent:
    """创建链路下线事件"""
    return LinkStateEvent(
        event_id=event_id,
        scheduled_time=scheduled_time,
        link_id=link_id,
        is_up=False,
        node_a=node_a,
        node_b=node_b
    )


def create_route_update(event_id: str,
                       scheduled_time: float,
                       node_id: str,
                       destination: str,
                       next_hop: str,
                       metric: float = 1.0) -> RouteUpdateEvent:
    """创建路由更新事件"""
    return RouteUpdateEvent(
        event_id=event_id,
        scheduled_time=scheduled_time,
        node_id=node_id,
        destination=destination,
        next_hop=next_hop,
        metric=metric
    ) 