"""
链路管理器模块

管理网络链路的状态和质量，包括：
- 链路状态监控
- 链路质量评估
- 动态链路调整
- 链路故障检测
- 带宽管理
"""

import math
import time
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
from .coordinate_system import Vector3D, LatLonAlt, CoordinateSystem
from .orbit_calculator import SatelliteState


class LinkState(Enum):
    """链路状态枚举"""
    UP = "up"
    DOWN = "down"
    DEGRADED = "degraded"
    ESTABLISHING = "establishing"
    UNKNOWN = "unknown"


@dataclass
class LinkQuality:
    """链路质量指标"""
    signal_strength: float    # 信号强度 (dBm)
    snr: float               # 信噪比 (dB)
    bit_error_rate: float    # 误码率
    latency: float           # 延迟 (ms)
    jitter: float            # 抖动 (ms)
    packet_loss: float       # 丢包率 (%)
    bandwidth: float         # 可用带宽 (bps)
    
    @property
    def quality_score(self) -> float:
        """计算综合质量评分 (0-1)"""
        # 信号强度评分 (-50dBm为满分，-120dBm为0分)
        signal_score = max(0, min(1, (-50 - self.signal_strength) / (-50 - (-120))))
        
        # 信噪比评分 (30dB为满分，0dB为0分)
        snr_score = max(0, min(1, self.snr / 30))
        
        # 误码率评分 (1e-6为满分，1e-3为0分)
        ber_score = max(0, min(1, math.log10(1e-3) - math.log10(max(self.bit_error_rate, 1e-10)) / 
                              (math.log10(1e-3) - math.log10(1e-6))))
        
        # 延迟评分 (10ms为满分，1000ms为0分)
        latency_score = max(0, min(1, (1000 - self.latency) / (1000 - 10)))
        
        # 丢包率评分 (0%为满分，10%为0分)
        loss_score = max(0, min(1, (10 - self.packet_loss) / 10))
        
        # 加权平均
        return (signal_score * 0.2 + snr_score * 0.2 + ber_score * 0.2 + 
                latency_score * 0.2 + loss_score * 0.2)


@dataclass
class Link:
    """链路类"""
    link_id: str
    node1_id: str
    node2_id: str
    link_type: str           # 'satellite_to_satellite', 'satellite_to_ground', 'ground_to_ground'
    state: LinkState
    quality: LinkQuality
    established_time: float
    last_update_time: float
    properties: Dict = None
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}
    
    @property
    def is_active(self) -> bool:
        """链路是否活跃"""
        return self.state in [LinkState.UP, LinkState.DEGRADED]
    
    @property
    def age(self) -> float:
        """链路存活时间 (秒)"""
        return time.time() - self.established_time


class LinkManager:
    """链路管理器"""
    
    def __init__(self):
        """初始化链路管理器"""
        self.links = {}  # link_id -> Link
        self.node_links = {}  # node_id -> [link_ids]
        
        # 链路参数配置
        self.config = {
            'max_satellite_distance': 2000000,  # 最大卫星间距离 (m)
            'max_ground_distance': 1000000,     # 最大地面站距离 (m)
            'min_elevation_angle': 10.0,        # 最小仰角 (度)
            'signal_frequency': 2.4e9,          # 信号频率 (Hz)
            'transmit_power': 20.0,              # 发射功率 (dBm)
            'antenna_gain': 30.0,                # 天线增益 (dBi)
            'noise_temperature': 300.0,          # 噪声温度 (K)
            'bandwidth': 1e9,                    # 基础带宽 (Hz)
        }
        
        # 统计信息
        self.stats = {
            'total_links_created': 0,
            'total_links_destroyed': 0,
            'quality_updates': 0,
            'state_changes': 0
        }
    
    def create_link(self, node1_id: str, node2_id: str, link_type: str) -> Optional[str]:
        """
        创建链路
        
        Args:
            node1_id: 节点1 ID
            node2_id: 节点2 ID
            link_type: 链路类型
            
        Returns:
            链路ID或None（如果创建失败）
        """
        link_id = f"{node1_id}_{node2_id}"
        
        if link_id in self.links:
            return None  # 链路已存在
        
        # 创建初始质量（将在update中计算实际值）
        initial_quality = LinkQuality(
            signal_strength=-70.0,
            snr=15.0,
            bit_error_rate=1e-6,
            latency=50.0,
            jitter=5.0,
            packet_loss=0.1,
            bandwidth=self.config['bandwidth']
        )
        
        link = Link(
            link_id=link_id,
            node1_id=node1_id,
            node2_id=node2_id,
            link_type=link_type,
            state=LinkState.ESTABLISHING,
            quality=initial_quality,
            established_time=time.time(),
            last_update_time=time.time()
        )
        
        self.links[link_id] = link
        
        # 更新节点链路映射
        if node1_id not in self.node_links:
            self.node_links[node1_id] = []
        if node2_id not in self.node_links:
            self.node_links[node2_id] = []
        
        self.node_links[node1_id].append(link_id)
        self.node_links[node2_id].append(link_id)
        
        self.stats['total_links_created'] += 1
        
        return link_id
    
    def destroy_link(self, link_id: str) -> bool:
        """
        销毁链路
        
        Args:
            link_id: 链路ID
            
        Returns:
            是否成功销毁
        """
        if link_id not in self.links:
            return False
        
        link = self.links[link_id]
        
        # 从节点链路映射中移除
        if link.node1_id in self.node_links:
            self.node_links[link.node1_id].remove(link_id)
        if link.node2_id in self.node_links:
            self.node_links[link.node2_id].remove(link_id)
        
        del self.links[link_id]
        self.stats['total_links_destroyed'] += 1
        
        return True
    
    def update_link_quality(self, link_id: str, node1_pos: Vector3D, node2_pos: Vector3D,
                          node1_type: str = "satellite", node2_type: str = "satellite"):
        """
        更新链路质量
        
        Args:
            link_id: 链路ID
            node1_pos: 节点1位置
            node2_pos: 节点2位置
            node1_type: 节点1类型
            node2_type: 节点2类型
        """
        if link_id not in self.links:
            return
        
        link = self.links[link_id]
        distance = node1_pos.distance_to(node2_pos)
        
        # 计算信号强度（自由空间路径损耗模型）
        signal_strength = self._calculate_signal_strength(distance)
        
        # 计算信噪比
        snr = self._calculate_snr(signal_strength, distance)
        
        # 计算误码率
        bit_error_rate = self._calculate_ber(snr)
        
        # 计算延迟
        latency = self._calculate_latency(distance, link.link_type)
        
        # 计算抖动（基于距离变化率）
        jitter = self._calculate_jitter(distance, link.link_type)
        
        # 计算丢包率
        packet_loss = self._calculate_packet_loss(snr, distance)
        
        # 计算可用带宽
        bandwidth = self._calculate_bandwidth(snr, distance)
        
        # 更新链路质量
        link.quality = LinkQuality(
            signal_strength=signal_strength,
            snr=snr,
            bit_error_rate=bit_error_rate,
            latency=latency,
            jitter=jitter,
            packet_loss=packet_loss,
            bandwidth=bandwidth
        )
        
        # 根据质量更新链路状态
        self._update_link_state(link)
        
        link.last_update_time = time.time()
        self.stats['quality_updates'] += 1
    
    def _calculate_signal_strength(self, distance: float) -> float:
        """计算信号强度 (dBm)"""
        # 自由空间路径损耗: FSPL = 20*log10(d) + 20*log10(f) + 32.45
        # 其中d为距离(km), f为频率(MHz)
        frequency_mhz = self.config['signal_frequency'] / 1e6
        distance_km = distance / 1000
        
        fspl = 20 * math.log10(distance_km) + 20 * math.log10(frequency_mhz) + 32.45
        
        # 接收信号强度 = 发射功率 + 发射天线增益 + 接收天线增益 - 路径损耗
        received_power = (self.config['transmit_power'] + 
                         self.config['antenna_gain'] + 
                         self.config['antenna_gain'] - fspl)
        
        return received_power
    
    def _calculate_snr(self, signal_strength: float, distance: float) -> float:
        """计算信噪比 (dB)"""
        # 热噪声功率 = k * T * B (以dBm表示)
        k_boltzmann = 1.38e-23  # 玻尔兹曼常数
        noise_power_w = k_boltzmann * self.config['noise_temperature'] * self.config['bandwidth']
        noise_power_dbm = 10 * math.log10(noise_power_w * 1000)  # 转换为dBm
        
        snr = signal_strength - noise_power_dbm
        
        # 添加一些随机噪声和距离相关的衰减
        distance_penalty = distance / 1000000  # 每1000km降1dB
        return max(0, snr - distance_penalty)
    
    def _calculate_ber(self, snr: float) -> float:
        """计算误码率"""
        # 简化的QPSK误码率公式
        if snr <= 0:
            return 0.5  # 最差情况
        
        snr_linear = 10 ** (snr / 10)
        ber = 0.5 * math.exp(-snr_linear)
        return max(1e-10, min(0.5, ber))
    
    def _calculate_latency(self, distance: float, link_type: str) -> float:
        """计算延迟 (ms)"""
        # 传播延迟
        propagation_delay = distance / 299792458 * 1000  # 转换为ms
        
        # 处理延迟（根据链路类型）
        if link_type == "satellite_to_satellite":
            processing_delay = 5.0  # 5ms
        elif link_type == "satellite_to_ground":
            processing_delay = 10.0  # 10ms
        else:  # ground_to_ground
            processing_delay = 2.0   # 2ms
        
        return propagation_delay + processing_delay
    
    def _calculate_jitter(self, distance: float, link_type: str) -> float:
        """计算抖动 (ms)"""
        # 基础抖动
        base_jitter = 1.0 if link_type == "ground_to_ground" else 5.0
        
        # 距离相关的抖动
        distance_jitter = distance / 1000000  # 每1000km增加1ms抖动
        
        return base_jitter + distance_jitter
    
    def _calculate_packet_loss(self, snr: float, distance: float) -> float:
        """计算丢包率 (%)"""
        # 基于信噪比的丢包率
        if snr > 20:
            snr_loss = 0.01
        elif snr > 10:
            snr_loss = 0.1
        elif snr > 5:
            snr_loss = 1.0
        else:
            snr_loss = 5.0
        
        # 距离相关的丢包率
        distance_loss = (distance / 2000000) * 0.5  # 每2000km增加0.5%
        
        return min(50.0, snr_loss + distance_loss)
    
    def _calculate_bandwidth(self, snr: float, distance: float) -> float:
        """计算可用带宽 (bps)"""
        base_bandwidth = self.config['bandwidth']
        
        # Shannon容量公式的简化版本
        if snr > 0:
            capacity_factor = math.log2(1 + 10**(snr/10))
            capacity_factor = min(8, capacity_factor)  # 限制最大为8 bits/Hz
        else:
            capacity_factor = 0.1
        
        # 距离衰减
        distance_factor = max(0.1, 1.0 - distance / 3000000)  # 3000km外大幅衰减
        
        return base_bandwidth * capacity_factor * distance_factor / 8  # 理论容量的1/8
    
    def _update_link_state(self, link: Link):
        """根据质量更新链路状态"""
        old_state = link.state
        quality_score = link.quality.quality_score
        
        if quality_score > 0.7:
            new_state = LinkState.UP
        elif quality_score > 0.3:
            new_state = LinkState.DEGRADED
        else:
            new_state = LinkState.DOWN
        
        if old_state != new_state:
            link.state = new_state
            self.stats['state_changes'] += 1
    
    def get_node_links(self, node_id: str) -> List[Link]:
        """获取节点的所有链路"""
        link_ids = self.node_links.get(node_id, [])
        return [self.links[link_id] for link_id in link_ids if link_id in self.links]
    
    def get_active_links(self) -> List[Link]:
        """获取所有活跃链路"""
        return [link for link in self.links.values() if link.is_active]
    
    def get_link_between_nodes(self, node1_id: str, node2_id: str) -> Optional[Link]:
        """获取两个节点间的链路"""
        link_id1 = f"{node1_id}_{node2_id}"
        link_id2 = f"{node2_id}_{node1_id}"
        
        return self.links.get(link_id1) or self.links.get(link_id2)
    
    def cleanup_old_links(self, max_age: float = 3600.0):
        """清理过期链路"""
        current_time = time.time()
        to_remove = []
        
        for link_id, link in self.links.items():
            if current_time - link.last_update_time > max_age:
                to_remove.append(link_id)
        
        for link_id in to_remove:
            self.destroy_link(link_id)
    
    def get_statistics(self) -> Dict:
        """获取链路管理统计信息"""
        active_links = len(self.get_active_links())
        total_links = len(self.links)
        
        # 计算平均质量
        if total_links > 0:
            avg_quality = sum(link.quality.quality_score for link in self.links.values()) / total_links
        else:
            avg_quality = 0.0
        
        # 按状态统计
        state_counts = {}
        for state in LinkState:
            state_counts[state.value] = sum(1 for link in self.links.values() if link.state == state)
        
        self.stats.update({
            'current_total_links': total_links,
            'current_active_links': active_links,
            'average_quality': avg_quality,
            'state_distribution': state_counts
        })
        
        return self.stats.copy() 