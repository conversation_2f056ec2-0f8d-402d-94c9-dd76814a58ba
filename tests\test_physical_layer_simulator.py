import pytest
import math
from physics.physical_layer_simulator import PropagationModel, AntennaModel, NoiseModel, PhysicalLayerSimulator

@pytest.fixture
def prop_model():
    return PropagationModel()

def test_free_space_path_loss(prop_model):
    """Test the free space path loss calculation."""
    # Example values:
    # Distance: 1000 km = 1e6 m
    # Frequency: 12 GHz = 12e9 Hz
    distance_km = 1000.0
    frequency_hz = 12e9
    
    # FSPL(dB) = 20*log10(d) + 20*log10(f) + 20*log10(4*pi/c)
    # 20*log10(1e6) = 120
    # 20*log10(12e9) = 20 * (log10(12) + 9) = 20 * (1.079 + 9) = 201.58
    # 20*log10(4*pi/3e8) = 20*log10(4.188e-8) = 20*(-7.378) = -147.56
    # Expected FSPL = 120 + 201.58 - 147.56 = 174.02 dB
    
    loss = prop_model.calculate_free_space_loss(distance_km, frequency_hz)
    assert loss == pytest.approx(174.02, rel=1e-2)

def test_doppler_shift(prop_model):
    """Test Doppler shift calculation."""
    # Relative velocity of 1 km/s towards the receiver
    relative_velocity = -1000 # m/s (negative for approaching)
    frequency_hz = 10e9 # 10 GHz
    
    # doppler = f * v / c
    # expected_shift = 10e9 * (-1000) / 3e8 = -33333.33 Hz
    shift = prop_model.calculate_doppler_shift(relative_velocity, frequency_hz)
    assert shift == pytest.approx(-33333.33, rel=1e-2)

def test_noise_model():
    """Test the thermal noise calculation."""
    noise_model = NoiseModel()
    # Typical values
    temperature_k = 290 # Kelvin
    bandwidth_hz = 20e6 # 20 MHz
    
    # Noise(dBm) = 10*log10(k * T * B * 1000)
    # k = 1.38e-23
    # noise_watts = 1.38e-23 * 290 * 20e6 = 8.004e-14 W
    # noise_dbm = 10 * log10(8.004e-14 * 1000) = 10 * log10(8.004e-11)
    #           = 10 * (-10.096) = -100.96 dBm
    noise = noise_model.calculate_thermal_noise(temperature_k, bandwidth_hz)
    assert noise == pytest.approx(-100.96, rel=1e-2)

def test_placeholder_for_link_budget():
    """
    Placeholder test for the full link budget calculation.
    """
    assert True 