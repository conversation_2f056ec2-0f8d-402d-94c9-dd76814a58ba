#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卫星拓扑控制器
负责卫星轨迹计算和拓扑生成
"""

import math
import numpy as np
import threading
import time
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

from utils.logger import setup_logger


@dataclass
class SatelliteInfo:
    """卫星信息数据结构"""
    id: int
    name: str
    position: Tuple[float, float, float]  # (x, y, z) in km
    velocity: Tuple[float, float, float]  # (vx, vy, vz) in km/s
    orbital_elements: Dict  # 轨道要素
    beam_status: Dict  # 波束状态
    timestamp: datetime


@dataclass
class GroundStation:
    """地面站信息"""
    id: int
    name: str
    location: Tuple[float, float, float]  # (lat, lon, alt)
    visible_satellites: List[int]


class OrbitCalculator:
    """轨道计算器 - 使用SGP4模型"""
    
    def __init__(self, config: Dict = None):
        self.logger = setup_logger("OrbitCalculator")
        # 【配置修改1】增强物理参数配置，提高计算精度
        self.config = config or {}
        self.mu = self.config.get('earth_gravitational_parameter', 3.986004418e14)  # m³/s²
        self.earth_radius = self.config.get('earth_radius', 6371.008)  # km (更精确值)
        self.calculation_precision = self.config.get('calculation_precision', 1e-10)  # 提高精度
        self.max_iterations = self.config.get('max_kepler_iterations', 150)  # 增加迭代次数
        
    def calculate_satellite_position(self, orbital_elements: Dict, timestamp: datetime) -> Tuple[Tuple[float, float, float], Tuple[float, float, float]]:
        """
        使用SGP4模型计算卫星位置和速度
        
        Args:
            orbital_elements: 轨道要素
            timestamp: 计算时间
        
        Returns:
            position, velocity: 位置和速度元组
        """
        try:
            # 提取轨道要素
            a = orbital_elements.get('semi_major_axis', 7000.0)  # 半长轴 (km)
            e = orbital_elements.get('eccentricity', 0.001)  # 偏心率
            i = math.radians(orbital_elements.get('inclination', 53.0))  # 倾角 (度转弧度)
            omega = math.radians(orbital_elements.get('argument_of_perigee', 0.0))  # 近地点幅角
            Omega = math.radians(orbital_elements.get('raan', 0.0))  # 升交点赤经
            M0 = math.radians(orbital_elements.get('mean_anomaly', 0.0))  # 平近点角
            epoch = orbital_elements.get('epoch', datetime.now())
            
            # 计算时间差（秒）
            dt = (timestamp - epoch).total_seconds()
            
            # 平均角速度
            n = math.sqrt(self.mu / (a * 1000)**3) * 1000  # rad/s
            
            # 当前平近点角
            M = M0 + n * dt
            
            # 开普勒方程求解偏近点角E
            E = self._solve_kepler_equation(M, e, self.calculation_precision)
            
            # 真近点角
            nu = 2 * math.atan2(
                math.sqrt(1 + e) * math.sin(E/2),
                math.sqrt(1 - e) * math.cos(E/2)
            )
            
            # 轨道半径
            r = a * (1 - e * math.cos(E))
            
            # 轨道平面坐标
            x_orb = r * math.cos(nu)
            y_orb = r * math.sin(nu)
            z_orb = 0.0
            
            # 轨道平面速度
            p = a * (1 - e**2)
            h = math.sqrt(self.mu * p * 1000) / 1000  # km²/s
            vx_orb = -(h/r) * math.sin(nu)
            vy_orb = (h/r) * (e + math.cos(nu))
            vz_orb = 0.0
            
            # 转换到地心惯性坐标系
            cos_omega = math.cos(omega)
            sin_omega = math.sin(omega)
            cos_Omega = math.cos(Omega)
            sin_Omega = math.sin(Omega)
            cos_i = math.cos(i)
            sin_i = math.sin(i)
            
            # 旋转矩阵
            x = (cos_omega * cos_Omega - sin_omega * cos_i * sin_Omega) * x_orb + \
                (-sin_omega * cos_Omega - cos_omega * cos_i * sin_Omega) * y_orb
            
            y = (cos_omega * sin_Omega + sin_omega * cos_i * cos_Omega) * x_orb + \
                (-sin_omega * sin_Omega + cos_omega * cos_i * cos_Omega) * y_orb
            
            z = (sin_omega * sin_i) * x_orb + (cos_omega * sin_i) * y_orb
            
            vx = (cos_omega * cos_Omega - sin_omega * cos_i * sin_Omega) * vx_orb + \
                 (-sin_omega * cos_Omega - cos_omega * cos_i * sin_Omega) * vy_orb
            
            vy = (cos_omega * sin_Omega + sin_omega * cos_i * cos_Omega) * vx_orb + \
                 (-sin_omega * sin_Omega + cos_omega * cos_i * cos_Omega) * vy_orb
            
            vz = (sin_omega * sin_i) * vx_orb + (cos_omega * sin_i) * vy_orb
            
            return (x, y, z), (vx, vy, vz)
            
        except Exception as e:
            self.logger.error(f"轨道计算错误: {e}")
            return (0.0, 0.0, 0.0), (0.0, 0.0, 0.0)
    
    def _solve_kepler_equation(self, M: float, e: float, tolerance: float = 1e-8) -> float:
        """求解开普勒方程"""
        E = M  # 初始猜测
        for _ in range(self.max_iterations):  # 使用配置的最大迭代次数
            E_new = M + e * math.sin(E)
            if abs(E_new - E) < tolerance:
                return E_new
            E = E_new
        return E


class TopologyGenerator:
    """拓扑生成器"""
    
    def __init__(self, config: Dict = None):
        self.logger = setup_logger("TopologyGenerator")
        # 【配置修改2】增强链路配置参数，提供更灵活的控制
        self.config = config or {}
        self.link_types = ["同轨道星间链路", "异轨道星间链路", "极地星间链路", "交叉轨道链路"]  # 增加链路类型
        self.selection_algorithms = ["最小距离", "最优信号强度", "负载均衡", "最小延时", "最高带宽"]  # 增加选择算法
        
        # 【配置修改3】可配置的链路参数
        self.max_inter_sat_distance = self.config.get('max_inter_satellite_distance', 8000)  # 增加到8000km
        self.min_inter_sat_distance = self.config.get('min_inter_satellite_distance', 800)   # 减少到800km
        self.min_elevation_angle = self.config.get('min_elevation_angle', 10.0)  # 最小仰角10度
        self.ground_visibility_altitude = self.config.get('ground_visibility_altitude', 250)  # 降低到250km
        self.max_links_per_satellite = self.config.get('max_links_per_satellite', 8)  # 每颗卫星最大链路数
        
    def generate_inter_satellite_links(self, satellites: List[SatelliteInfo]) -> List[Tuple[int, int]]:
        """生成星间链路"""
        links = []
        sat_link_count = {}  # 记录每颗卫星的链路数
        
        for i, sat1 in enumerate(satellites):
            sat_link_count[sat1.id] = 0
            for j, sat2 in enumerate(satellites[i+1:], i+1):
                # 检查链路数限制
                if (sat_link_count.get(sat1.id, 0) >= self.max_links_per_satellite or 
                    sat_link_count.get(sat2.id, 0) >= self.max_links_per_satellite):
                    continue
                    
                distance = self._calculate_distance(sat1.position, sat2.position)
                
                # 使用新的距离阈值判断是否建立链路
                if self.min_inter_sat_distance < distance < self.max_inter_sat_distance:
                    # 检查卫星之间的角度和其他可行性条件
                    if self._check_link_feasibility(sat1, sat2):
                        links.append((sat1.id, sat2.id))
                        sat_link_count[sat1.id] = sat_link_count.get(sat1.id, 0) + 1
                        sat_link_count[sat2.id] = sat_link_count.get(sat2.id, 0) + 1
        
        return links
    
    def generate_satellite_ground_links(self, satellites: List[SatelliteInfo], 
                                      ground_stations: List[GroundStation]) -> Dict[int, List[int]]:
        """生成星地链路"""
        sat_ground_links = {}
        
        for station in ground_stations:
            visible_sats = []
            for satellite in satellites:
                if self._is_satellite_visible(satellite, station):
                    visible_sats.append(satellite.id)
            
            # 【配置修改4】支持多卫星选择
            max_sats_per_station = self.config.get('max_satellites_per_ground_station', 3)
            selected_sats = self._select_best_satellites(visible_sats, satellites, station, max_sats_per_station)
            if selected_sats:
                sat_ground_links[station.id] = selected_sats
            
        return sat_ground_links
    
    def _calculate_distance(self, pos1: Tuple[float, float, float], 
                          pos2: Tuple[float, float, float]) -> float:
        """计算两点间距离"""
        return math.sqrt(sum((a - b)**2 for a, b in zip(pos1, pos2)))
    
    def _check_link_feasibility(self, sat1: SatelliteInfo, sat2: SatelliteInfo) -> bool:
        """检查链路可行性"""
        distance = self._calculate_distance(sat1.position, sat2.position)
        # 使用新的距离范围
        return self.min_inter_sat_distance < distance < self.max_inter_sat_distance
    
    def _is_satellite_visible(self, satellite: SatelliteInfo, station: GroundStation) -> bool:
        """判断卫星对地面站是否可见"""
        sat_x, sat_y, sat_z = satellite.position
        lat, lon, alt = station.location
        
        # 改进的可见性计算
        earth_center_to_sat = math.sqrt(sat_x**2 + sat_y**2 + sat_z**2)
        return earth_center_to_sat > 6371 + self.ground_visibility_altitude
    
    def _select_best_satellites(self, visible_sats: List[int], 
                              satellites: List[SatelliteInfo], 
                              station: GroundStation,
                              max_count: int = 3) -> List[int]:
        """选择最佳卫星（支持多颗）"""
        if not visible_sats:
            return []
        
        # 计算所有可见卫星的距离
        sat_distances = []
        for sat_id in visible_sats:
            satellite = next((s for s in satellites if s.id == sat_id), None)
            if satellite:
                distance = self._calculate_sat_ground_distance(satellite, station)
                sat_distances.append((sat_id, distance))
        
        # 按距离排序，选择最近的几颗
        sat_distances.sort(key=lambda x: x[1])
        return [sat_id for sat_id, _ in sat_distances[:max_count]]
    
    def _select_best_satellite(self, visible_sats: List[int], 
                             satellites: List[SatelliteInfo], 
                             station: GroundStation) -> Optional[int]:
        """选择最佳卫星（保持向后兼容）"""
        selected = self._select_best_satellites(visible_sats, satellites, station, 1)
        return selected[0] if selected else None
    
    def _calculate_sat_ground_distance(self, satellite: SatelliteInfo, 
                                     station: GroundStation) -> float:
        """计算卫星到地面站的距离"""
        # 简化计算
        sat_x, sat_y, sat_z = satellite.position
        return math.sqrt(sat_x**2 + sat_y**2 + sat_z**2) - 6371


class SatelliteTopologyController:
    """卫星拓扑控制器主类"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("SatelliteTopologyController")
        
        # 组件初始化，传递配置
        self.orbit_calculator = OrbitCalculator(config.get('orbit_config', {}))
        self.topology_generator = TopologyGenerator(config.get('topology_config', {}))
        
        # 数据存储
        self.satellites: List[SatelliteInfo] = []
        self.ground_stations: List[GroundStation] = []
        self.constellation_configs = []
        
        # 【配置修改5】可配置的运行参数
        self.default_update_interval = config.get('default_update_interval', 0.5)  # 提高更新频率
        self.running = False
        self.update_thread = None
        
        self.logger.info("卫星拓扑控制器初始化完成")
    
    def load_constellation_config(self, config_file: str = None):
        """加载星座配置"""
        # 【配置修改6】增强和新增星座配置
        self.constellation_configs = [
            {
                'name': 'Starlink-Enhanced',  # 修改名称
                'orbit_altitude': 575,  # 轨道高度从550提高到575km
                'inclination': 53.2,  # 倾角微调到53.2度
                'planes': 80,  # 轨道面从72增加到80
                'sats_per_plane': 25,  # 每轨道面卫星从22增加到25
                'total_satellites': 2000  # 总数从1584增加到2000
            },
            {
                'name': 'OneWeb-Optimized',  # 修改名称
                'orbit_altitude': 1250,  # 轨道高度从1200提高到1250km
                'inclination': 88.0,  # 倾角从87.9调整到88.0度
                'planes': 20,  # 轨道面从18增加到20
                'sats_per_plane': 40,  # 每轨道面卫星从36增加到40
                'total_satellites': 800  # 总数从648增加到800
            },
            {
                'name': 'Custom-LEO-Advanced',  # 修改名称
                'orbit_altitude': 850,  # 轨道高度从800提高到850km
                'inclination': 97.8,  # 倾角从98.0调整到97.8度
                'planes': 12,  # 轨道面从10增加到12
                'sats_per_plane': 24,  # 每轨道面卫星从20增加到24
                'total_satellites': 288  # 总数从200增加到288
            },
            {
                'name': 'GEO-Constellation',  # 新增地球同步轨道星座
                'orbit_altitude': 35786,  # 地球同步轨道高度
                'inclination': 0.0,  # 赤道轨道
                'planes': 3,  # 3个轨道面
                'sats_per_plane': 8,  # 每轨道面8颗卫星
                'total_satellites': 24
            },
            {
                'name': 'Polar-Constellation',  # 新增极地轨道星座
                'orbit_altitude': 780,
                'inclination': 99.5,  # 太阳同步轨道
                'planes': 6,
                'sats_per_plane': 15,
                'total_satellites': 90
            }
        ]
        
        self.logger.info(f"加载了 {len(self.constellation_configs)} 种星座配置")
    
    def generate_constellation(self, constellation_name: str = "Custom-LEO-Advanced"):  # 修改默认配置
        """生成星座"""
        config = next((c for c in self.constellation_configs if c['name'] == constellation_name), 
                     self.constellation_configs[0])
        
        self.logger.info(f"开始生成 {constellation_name} 星座...")
        
        self.satellites.clear()
        satellite_id = 1
        
        for plane in range(config['planes']):
            for sat_in_plane in range(config['sats_per_plane']):
                # 计算轨道要素
                orbital_elements = {
                    'semi_major_axis': 6371 + config['orbit_altitude'],
                    'eccentricity': 0.001,
                    'inclination': config['inclination'],
                    'argument_of_perigee': 0.0,
                    'raan': plane * (360.0 / config['planes']),
                    'mean_anomaly': sat_in_plane * (360.0 / config['sats_per_plane']),
                    'epoch': datetime.now()
                }
                
                # 计算初始位置
                position, velocity = self.orbit_calculator.calculate_satellite_position(
                    orbital_elements, datetime.now()
                )
                
                satellite = SatelliteInfo(
                    id=satellite_id,
                    name=f"SAT-{satellite_id:04d}",
                    position=position,
                    velocity=velocity,
                    orbital_elements=orbital_elements,
                    beam_status={'active': True, 'power': 120, 'frequency': 'Ka'},  # 增强波束配置
                    timestamp=datetime.now()
                )
                
                self.satellites.append(satellite)
                satellite_id += 1
        
        self.logger.info(f"成功生成 {len(self.satellites)} 颗卫星")
    
    def update_satellite_positions(self, timestamp: datetime = None):
        """更新所有卫星位置"""
        if timestamp is None:
            timestamp = datetime.now()
        
        for satellite in self.satellites:
            position, velocity = self.orbit_calculator.calculate_satellite_position(
                satellite.orbital_elements, timestamp
            )
            satellite.position = position
            satellite.velocity = velocity
            satellite.timestamp = timestamp
    
    def generate_network_topology(self) -> Dict:
        """生成网络拓扑"""
        # 生成星间链路
        inter_sat_links = self.topology_generator.generate_inter_satellite_links(self.satellites)
        
        # 生成星地链路
        sat_ground_links = self.topology_generator.generate_satellite_ground_links(
            self.satellites, self.ground_stations
        )
        
        topology = {
            'timestamp': datetime.now(),
            'satellites': [
                {
                    'id': sat.id,
                    'name': sat.name,
                    'position': sat.position,
                    'velocity': sat.velocity
                } for sat in self.satellites
            ],
            'inter_satellite_links': inter_sat_links,
            'satellite_ground_links': sat_ground_links,
            'total_satellites': len(self.satellites),
            'total_links': len(inter_sat_links) + sum(len(links) for links in sat_ground_links.values())
        }
        
        return topology
    
    def start_position_updates(self, update_interval: float = None):
        """启动位置更新线程"""
        if self.running:
            return
        
        # 【配置修改7】使用配置的默认更新间隔
        if update_interval is None:
            update_interval = self.default_update_interval
        
        self.running = True
        self.update_thread = threading.Thread(target=self._position_update_loop, args=(update_interval,))
        self.update_thread.daemon = True
        self.update_thread.start()
        
        self.logger.info(f"位置更新线程已启动，更新间隔: {update_interval}秒")
    
    def stop_position_updates(self):
        """停止位置更新"""
        self.running = False
        if self.update_thread:
            self.update_thread.join()
        self.logger.info("位置更新线程已停止")
    
    def _position_update_loop(self, update_interval: float):
        """位置更新循环"""
        while self.running:
            try:
                self.update_satellite_positions()
                time.sleep(update_interval)
            except Exception as e:
                self.logger.error(f"位置更新错误: {e}")
    
    def get_satellite_count(self) -> int:
        """获取卫星数量"""
        return len(self.satellites)
    
    def get_satellite_info(self, satellite_id: int) -> Optional[SatelliteInfo]:
        """获取指定卫星信息"""
        return next((sat for sat in self.satellites if sat.id == satellite_id), None)
    
    def export_ephemeris_data(self, filename: str = None) -> str:
        """导出星历数据"""
        if filename is None:
            filename = f"ephemeris_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        # 生成TLE格式的星历数据
        ephemeris_data = []
        for satellite in self.satellites:
            line1 = f"1 {satellite.id:05d}U 24001A   24001.50000000  .00000000  00000-0  00000-0 0  9990"
            line2 = f"2 {satellite.id:05d} {satellite.orbital_elements.get('inclination', 53.0):8.4f} " + \
                   f"{satellite.orbital_elements.get('raan', 0.0):8.4f} 0000000 " + \
                   f"{satellite.orbital_elements.get('argument_of_perigee', 0.0):8.4f} " + \
                   f"{satellite.orbital_elements.get('mean_anomaly', 0.0):8.4f} " + \
                   f"15.50000000{satellite.id:5d}"
            
            ephemeris_data.append(satellite.name)
            ephemeris_data.append(line1)
            ephemeris_data.append(line2)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(ephemeris_data))
            self.logger.info(f"星历数据已导出到: {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"导出星历数据失败: {e}")
            return ""
    
    def import_ephemeris_data(self, filename: str) -> bool:
        """导入星历数据"""
        try:
            # 简化的TLE读取实现
            self.logger.info(f"从 {filename} 导入星历数据")
            return True
        except Exception as e:
            self.logger.error(f"导入星历数据失败: {e}")
            return False 