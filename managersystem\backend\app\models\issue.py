"""
问题模型
处理缺陷问题管理相关的数据操作
"""

from datetime import datetime
from typing import Dict, List, Optional, Any

from app.utils.redis_client import redis_client, RedisKeys


class Issue:
    """问题模型类"""
    
    # 问题类型定义
    TYPES = {
        'bug': '缺陷',
        'feature': '功能需求',
        'task': '任务',
        'improvement': '改进',
        'question': '疑问'
    }
    
    # 优先级定义
    PRIORITIES = {
        'low': '低',
        'medium': '中',
        'high': '高',
        'critical': '紧急'
    }
    
    # 状态定义
    STATUSES = {
        'open': '待处理',
        'in_progress': '处理中',
        'resolved': '已解决',
        'closed': '已关闭',
        'rejected': '已拒绝'
    }
    
    def __init__(self, issue_data: Dict[str, Any] = None):
        """初始化问题对象"""
        if issue_data:
            self.id = issue_data.get('id')
            self.title = issue_data.get('title')
            self.description = issue_data.get('description')
            self.type = issue_data.get('type', 'bug')
            self.priority = issue_data.get('priority', 'medium')
            self.status = issue_data.get('status', 'open')
            self.assignee = issue_data.get('assignee')
            self.reporter = issue_data.get('reporter')
            self.created_at = issue_data.get('created_at')
            self.updated_at = issue_data.get('updated_at')
            self.resolved_at = issue_data.get('resolved_at')
            self.tags = issue_data.get('tags', [])
            self.attachments = issue_data.get('attachments', [])
            self.estimated_hours = issue_data.get('estimated_hours', 0)
            self.actual_hours = issue_data.get('actual_hours', 0)
        else:
            self.id = None
            self.title = None
            self.description = None
            self.type = 'bug'
            self.priority = 'medium'
            self.status = 'open'
            self.assignee = None
            self.reporter = None
            self.created_at = None
            self.updated_at = None
            self.resolved_at = None
            self.tags = []
            self.attachments = []
            self.estimated_hours = 0
            self.actual_hours = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'type': self.type,
            'type_name': self.TYPES.get(self.type, self.type),
            'priority': self.priority,
            'priority_name': self.PRIORITIES.get(self.priority, self.priority),
            'status': self.status,
            'status_name': self.STATUSES.get(self.status, self.status),
            'assignee': self.assignee,
            'reporter': self.reporter,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'resolved_at': self.resolved_at,
            'tags': self.tags,
            'attachments': self.attachments,
            'estimated_hours': self.estimated_hours,
            'actual_hours': self.actual_hours
        }
    
    def save(self) -> bool:
        """保存问题到Redis"""
        try:
            if not self.id:
                self.id = redis_client.generate_id('issue_')
                self.created_at = redis_client.get_timestamp()
            
            self.updated_at = redis_client.get_timestamp()
            
            # 如果状态变为已解决，记录解决时间
            if self.status == 'resolved' and not self.resolved_at:
                self.resolved_at = redis_client.get_timestamp()
            
            # 保存问题数据
            issue_key = RedisKeys.ISSUE.format(issue_id=self.id)
            issue_data = self.to_dict()
            
            if not redis_client.set(issue_key, issue_data):
                return False
            
            # 添加到主索引
            index_key = RedisKeys.ISSUE_INDEX
            if not redis_client.add_to_index(index_key, self.id):
                return False
            
            # 添加到状态索引
            status_key = RedisKeys.ISSUE_BY_STATUS.format(status=self.status)
            redis_client.add_to_index(status_key, self.id)
            
            # 添加到分配人索引
            if self.assignee:
                assignee_key = RedisKeys.ISSUE_BY_ASSIGNEE.format(user_id=self.assignee)
                redis_client.add_to_index(assignee_key, self.id)
            
            return True
            
        except Exception as e:
            print(f"Error saving issue: {e}")
            return False
    
    def delete(self) -> bool:
        """删除问题"""
        try:
            if not self.id:
                return False
            
            # 删除问题数据
            issue_key = RedisKeys.ISSUE.format(issue_id=self.id)
            redis_client.delete(issue_key)
            
            # 从主索引中移除
            index_key = RedisKeys.ISSUE_INDEX
            redis_client.remove_from_index(index_key, self.id)
            
            # 从状态索引中移除
            status_key = RedisKeys.ISSUE_BY_STATUS.format(status=self.status)
            redis_client.remove_from_index(status_key, self.id)
            
            # 从分配人索引中移除
            if self.assignee:
                assignee_key = RedisKeys.ISSUE_BY_ASSIGNEE.format(user_id=self.assignee)
                redis_client.remove_from_index(assignee_key, self.id)
            
            # 删除评论
            comments_key = RedisKeys.ISSUE_COMMENTS.format(issue_id=self.id)
            redis_client.delete(comments_key)
            
            return True
            
        except Exception as e:
            print(f"Error deleting issue: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, issue_id: str) -> Optional['Issue']:
        """根据ID获取问题"""
        try:
            issue_key = RedisKeys.ISSUE.format(issue_id=issue_id)
            issue_data = redis_client.get(issue_key)
            
            if issue_data:
                return cls(issue_data)
            return None
            
        except Exception as e:
            print(f"Error getting issue by id: {e}")
            return None
    
    @classmethod
    def get_list(cls, page: int = 1, size: int = 20, **filters) -> Dict[str, Any]:
        """获取问题列表"""
        try:
            # 根据过滤条件选择索引
            if filters.get('status'):
                index_key = RedisKeys.ISSUE_BY_STATUS.format(status=filters['status'])
            elif filters.get('assignee'):
                index_key = RedisKeys.ISSUE_BY_ASSIGNEE.format(user_id=filters['assignee'])
            else:
                index_key = RedisKeys.ISSUE_INDEX
            
            # 获取所有问题ID
            issue_ids = redis_client.get_from_index(index_key, reverse=True)
            
            # 应用其他过滤条件
            filtered_ids = []
            for issue_id in issue_ids:
                issue = cls.get_by_id(issue_id)
                if issue and cls._match_filters(issue, filters):
                    filtered_ids.append(issue_id)
            
            # 分页
            total = len(filtered_ids)
            start = (page - 1) * size
            end = start + size
            page_ids = filtered_ids[start:end]
            
            # 获取问题数据
            issues = []
            for issue_id in page_ids:
                issue = cls.get_by_id(issue_id)
                if issue:
                    issues.append(issue.to_dict())
            
            return {
                'issues': issues,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total,
                    'pages': (total + size - 1) // size
                }
            }
            
        except Exception as e:
            print(f"Error getting issue list: {e}")
            return {
                'issues': [],
                'pagination': {'page': 1, 'size': size, 'total': 0, 'pages': 0}
            }
    
    @classmethod
    def _match_filters(cls, issue: 'Issue', filters: Dict[str, Any]) -> bool:
        """检查问题是否匹配过滤条件"""
        if filters.get('type') and issue.type != filters['type']:
            return False
        
        if filters.get('priority') and issue.priority != filters['priority']:
            return False
        
        if filters.get('reporter') and issue.reporter != filters['reporter']:
            return False
        
        if filters.get('tags'):
            filter_tags = filters['tags'] if isinstance(filters['tags'], list) else [filters['tags']]
            if not any(tag in issue.tags for tag in filter_tags):
                return False
        
        if filters.get('keyword'):
            keyword = filters['keyword'].lower()
            if (keyword not in issue.title.lower() and 
                keyword not in issue.description.lower()):
                return False
        
        return True
    
    def add_comment(self, content: str, author: str) -> bool:
        """添加评论"""
        try:
            comment = {
                'id': redis_client.generate_id('comment_'),
                'content': content,
                'author': author,
                'created_at': redis_client.get_timestamp()
            }
            
            comments_key = RedisKeys.ISSUE_COMMENTS.format(issue_id=self.id)
            return redis_client.lpush(comments_key, comment) > 0
            
        except Exception as e:
            print(f"Error adding comment: {e}")
            return False
    
    def get_comments(self) -> List[Dict[str, Any]]:
        """获取评论列表"""
        try:
            comments_key = RedisKeys.ISSUE_COMMENTS.format(issue_id=self.id)
            return redis_client.lrange(comments_key)
        except Exception as e:
            print(f"Error getting comments: {e}")
            return []
    
    @classmethod
    def get_stats(cls) -> Dict[str, Any]:
        """获取问题统计信息"""
        try:
            index_key = RedisKeys.ISSUE_INDEX
            issue_ids = redis_client.get_from_index(index_key)
            
            stats = {
                'total': len(issue_ids),
                'by_type': {},
                'by_priority': {},
                'by_status': {},
                'by_assignee': {},
                'resolved_this_month': 0
            }
            
            current_month = datetime.now().strftime('%Y-%m')
            
            for issue_id in issue_ids:
                issue = cls.get_by_id(issue_id)
                if issue:
                    # 按类型统计
                    type_name = cls.TYPES.get(issue.type, issue.type)
                    stats['by_type'][type_name] = stats['by_type'].get(type_name, 0) + 1
                    
                    # 按优先级统计
                    priority_name = cls.PRIORITIES.get(issue.priority, issue.priority)
                    stats['by_priority'][priority_name] = stats['by_priority'].get(priority_name, 0) + 1
                    
                    # 按状态统计
                    status_name = cls.STATUSES.get(issue.status, issue.status)
                    stats['by_status'][status_name] = stats['by_status'].get(status_name, 0) + 1
                    
                    # 按分配人统计
                    if issue.assignee:
                        stats['by_assignee'][issue.assignee] = stats['by_assignee'].get(issue.assignee, 0) + 1
                    
                    # 本月解决的问题
                    if (issue.resolved_at and 
                        issue.resolved_at.startswith(current_month)):
                        stats['resolved_this_month'] += 1
            
            return stats
            
        except Exception as e:
            print(f"Error getting issue stats: {e}")
            return {
                'total': 0, 'by_type': {}, 'by_priority': {}, 
                'by_status': {}, 'by_assignee': {}, 'resolved_this_month': 0
            }
