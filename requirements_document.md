# 需求文档 - 卫星数字孪生系统 (SDT)

## 1. 引言

### 1.1. 项目目标
本项目旨在开发一个功能全面的卫星数字孪生系统（SDT），用于对大规模近地轨道（LEO）卫星星座的动态行为进行高精度仿真。系统的核心目标是提供一个集轨道动力学分析、通信网络模拟、物理层信道建模和实时可视化于一体的集成平台，以支持卫星网络协议的研发、星座设计的优化以及网络性能的评估。

### 1.2. 项目范围
本系统的范围涵盖了从卫星星座的定义、轨道位置的计算、动态网络拓扑的生成，到网络路由算法的执行、数据包传输的模拟、物理链路特性的分析，最终到仿真过程和结果的实时可视化。系统应支持不同星座配置和多种路由算法，并提供灵活的配置和扩展能力。

### 1.3. 读者对象
本文档的读者对象包括：
- **项目经理**：用于了解项目目标、范围和整体规划。
- **系统设计师与开发人员**：用于理解系统的功能与非功能需求，作为架构设计和模块开发的依据。
- **测试工程师**：用于制定测试计划和测试用例，确保系统满足所有需求。
- **最终用户/研究人员**：用于了解系统的能力和使用场景。

---

## 2. 总体描述

### 2.1. 产品愿景
成为一个高保真、高性能、高可扩展性的卫星网络仿真平台，为学术界和工业界的研究人员提供一个强大的工具，以加速下一代天基网络技术的创新和发展。通过数字孪生的方式，在纯软件环境中复现复杂卫星网络的动态特性，从而降低研究成本，提高研发效率。

### 2.2. 产品功能
系统将提供以下核心功能：
- **星座仿真**：支持多种LEO星座配置，精确模拟卫星在空间中的运动。
- **网络仿真**：模拟星间、星地链路的动态变化，并仿真数据在网络中的路由和传输。
- **物理层仿真**：对通信链路的物理特性（如信号衰减、信噪比）进行建模。
- **可视化**：以2D/3D图形方式实时展示卫星星座、网络链路和仿真数据。
- **配置与控制**：允许用户通过配置文件和GUI灵活地控制仿真过程。
- **数据与统计**：记录详细的仿真日志，并提供关键性能指标（KPI）的统计。

### 2.3. 用户特征
本系统的主要用户为卫星通信网络领域的研究人员、工程师和学生，他们具备通信网络和轨道动力学相关的专业知识，需要使用本系统进行网络协议验证、星座设计评估等研究工作。

---

## 3. 系统功能需求

### 3.1. 核心仿真功能

#### 3.1.1. 卫星星座建模与管理
- **[FR-CORE-001]** 系统应支持通过配置文件定义和加载卫星星座，配置参数应至少包括轨道高度、轨道倾角、轨道平面数量、每平面卫星数量。
- **[FR-CORE-002]** 系统应支持管理多个预定义的星座配置（例如，类似Starlink或OneWeb的星座）。
- **[FR-CORE-003]** 系统应能根据配置参数生成星座中每颗卫星的初始轨道根数（TLE）。

#### 3.1.2. 轨道动力学仿真
- **[FR-CORE-004]** 系统应使用SGP4（Simplified General Perturbations 4）模型来精确计算和预测卫星的轨道位置和速度。
- **[FR-CORE-005]** 系统应支持以可配置的时间步长，周期性地更新所有卫星的实时位置。
- **[FR--CORE-006]** 系统应支持将卫星的星历数据导出为标准格式（如TLE）。

#### 3.1.3. 网络拓扑动态生成
- **[FR-CORE-007]** 系统应能基于卫星的实时位置和预定义的链路策略，动态生成星间链路（ISL）和星地链路（GSL）。
- **[FR-CORE-008]** 系统应能将生成的网络拓扑（节点和链路）提供给路径计算模块和可视化模块使用。

### 3.2. 通信网络仿真

#### 3.2.1. 路由与路径计算
- **[FR-NET-001]** 系统应支持多种最短路径算法，至少包括Dijkstra、BFS和Floyd-Warshall。
- **[FR-NET-002]** 系统应允许用户在仿真开始前选择使用的路径计算算法。
- **[FR-NET-003]** 系统应实现路径计算缓存机制（如LRU），以提高在拓扑相对稳定时的计算效率。
- **[FR-NET-004]** 系统应能将计算出的路由表导出为文本文件。

#### 3.2.2. 网络流量生成
- **[FR-NET-005]** 系统应能模拟在指定的源节点和目标节点之间产生网络数据流。
- **[FR-NET-006]** 用户应能配置流量的参数，如持续时间、数据包速率等。

#### 3.2.3. 数据传输仿真
- **[FR-NET-007]** 系统应基于离散事件仿真引擎，模拟数据包在网络中的逐跳传输。
- **[FR-NET-008]** 仿真引擎应考虑由物理层模块计算出的传播延迟。
- **[FR-NET-009]** 系统应能统计网络性能数据，包括数据包的发送、接收、丢弃数量，以及端到端延迟和吞吐量。

### 3.3. 物理层仿真

#### 3.3.1. 链路预算分析
- **[FR-PHY-001]** 系统应能为任意两个节点间的通信链路计算详细的链路预算。
- **[FR-PHY-002]** 链路预算应至少考虑频率、发射功率、天线增益和自由空间路径损耗。
- **[FR-PHY-003]** 系统应能基于链路预算计算出信噪比（SNR）和理论信道容量（根据香农定理）。

#### 3.3.2. 信道特性建模
- **[FR-PHY-004]** 系统应能计算信号在星间和星地链路中的传播延迟。
- **[FR-PHY-005]** 系统应能根据卫星的相对速度计算多普勒频移。

#### 3.3.3. 环境影响仿真
- **[FR-PHY-006]** 系统应支持模拟不同环境条件对信号传播的影响，至少应包括大气损耗和雨衰模型。
- **[FR-PHY-007]** 用户应能配置相关的环境参数（如降雨率）。

### 3.4. 可视化与交互

#### 3.4.1. 实时状态显示
- **[FR-GUI-001]** 系统应提供一个图形用户界面（GUI），用于实时可视化仿真过程。
- **[FR-GUI-002]** 可视化界面应支持3D和2D两种模式展示卫星星座的动态。
- **[FR-GUI-003]** 界面上应能清晰地展示卫星节点、活动的星间和星地链路。

#### 3.4.2. 仿真过程控制
- **[FR-GUI-004]** 用户应能通过GUI启动、暂停、继续和停止仿真。
- **[FR-GUI-005]** 用户应能通过GUI调整仿真速度（例如，实时模式或加速模式）。

#### 3.4.3. 数据统计与展示
- **[FR-GUI-006]** GUI界面应实时显示关键的系统状态和性能指标，如仿真时间、卫星总数、活跃链路数、网络吞吐量等。

### 3.5. 系统管理

#### 3.5.1. 配置管理
- **[FR-SYS-001]** 系统所有的关键参数都应通过一个外部JSON配置文件进行管理。
- **[FR-SYS-002]** 系统启动时应加载该配置文件，如果文件不存在，则使用默认值创建。
- **[FR-SYS-003]** 系统应支持在运行时将当前的配置状态保存回文件。

#### 3.5.2. 日志管理
- **[FR-SYS-004]** 系统应提供一个统一的日志系统，记录运行状态、警告和错误信息。
- **[FR-SYS-005]** 日志应支持多级别（DEBUG, INFO, WARNING, ERROR）。
- **[FR-SYS-006]** 日志应能同时输出到控制台和日志文件。
- **[FR-SYS-007]** 日志文件应能按日期或大小自动进行轮转。

#### 3.5.3. 外部API接口
- **[FR-SYS-008]** 系统应提供一个基于TCP的接口，允许外部客户端进行交互，执行如查询系统状态、生成网络流量等操作。

---

## 4. 非功能性需求

### 4.1. 性能需求
- **[NFR-PERF-001]** 系统应能支持至少2000颗卫星的大规模星座仿真。
- **[NFR-PERF-002]** 对于中等规模网络（约500节点），单次路径计算的响应时间应在毫秒级别。
- **[NFR-PERF-003]** 在典型负载下，可视化界面的刷新率应不低于30 FPS，以保证动画的流畅性。

### 4.2. 易用性需求
- **[NFR-USAB-001]** 系统的安装和启动过程应足够简单，提供明确的指引文档。
- **[NFR-USAB-002]** 配置文件应有清晰的结构和注释。
- **[NFR-USAB-003]** GUI界面应直观，关键功能易于查找和操作。

### 4.3. 可靠性需求
- **[NFR-RELI-001]** 系统应具备异常处理和容错机制，避免因单个模块的错误导致整个系统崩溃。
- **[NFR-RELI-002]** 系统应能安全地停止和清理所有后台线程和资源。

### 4.4. 可扩展性需求
- **[NFR-EXT-001]** 系统应采用模块化设计，核心功能模块（如路径算法、星座生成器、物理模型）应易于替换和扩展。
- **[NFR-EXT-002]** 添加新的路径计算算法或星座模型，不应需要对系统核心框架进行大规模修改。

### 4.5. 兼容性需求
- **[NFR-COMP-001]** 系统应能在Python 3.8及以上版本环境中运行。

---

## 5. 系统架构

### 5.1. 软件架构
系统采用分层模块化架构，如下图所示：

```
SDT系统架构
├── 主程序层 (sdt_main.py)
├── 核心功能层 (core/)
│   ├── 卫星拓扑控制器 (satellite_topology_controller.py)
│   ├── 路径计算控制器 (satellite_path_calculator.py) 
│   ├── 卫星模拟器 (satellite_simulator.py)
│   └── 通信模块 (communication_module.py)
├── 物理层仿真 (physics/)
│   └── 物理层仿真器 (physical_layer_simulator.py)
├── 可视化界面 (gui/)
│   └── 可视化模块 (visualization_module.py)
└── 支撑模块 (utils/)
    ├── 配置管理器 (config_manager.py)
    └── 日志系统 (logger.py)
```

### 5.2. 模块说明
- **主程序层**：系统的入口，负责组装和协调所有模块。
- **核心功能层**：实现了卫星数字孪生的主要逻辑，包括轨道计算、拓扑生成、路径计算和事件仿真。
- **物理层仿真**：对通信链路的物理特性进行建模。
- **可视化界面**：提供人机交互界面，用于控制和监控仿真。
- **支撑模块**：提供配置、日志等通用基础服务。

---

## 6. 附录

### 6.1. 术语表

| 术语 | 全称 | 解释 |
| --- | --- | --- |
| SDT | Satellite Digital Twin | 卫星数字孪生 |
| LEO | Low Earth Orbit | 近地轨道 |
| TLE | Two-Line Element | 两行轨道根数，描述卫星轨道状态的标准格式 |
| SGP4 | Simplified General Perturbations 4 | 一种用于计算地球轨道卫星位置的算法模型 |
| ISL | Inter-Satellite Link | 星间链路 |
| GSL | Ground-to-Satellite Link | 星地链路 |
| SNR | Signal-to-Noise Ratio | 信噪比 |
| KPI | Key Performance Indicator | 关键性能指标 |
| GUI | Graphical User Interface | 图形用户界面 |
| API | Application Programming Interface | 应用程序编程接口 | 