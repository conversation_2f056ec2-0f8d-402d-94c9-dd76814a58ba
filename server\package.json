{"name": "sdt-satellite-server", "version": "2.0.0", "description": "SDT卫星数字孪生系统Node.js服务器", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["satellite", "digital-twin", "orbit", "topology", "simulation"], "author": "SDT Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "axios": "^1.5.0", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}