// 全局变量
let canvas, ctx;
let satellites = [];
let selectedConstellation = 'kuiper_630';
let animationId;
let simulationRunning = false;
let earthRotation = 0;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initCanvas();
    initEventListeners();
    generateSatellites();
    startAnimation();
});

// 初始化画布
function initCanvas() {
    canvas = document.getElementById('earthCanvas');
    ctx = canvas.getContext('2d');
    
    // 设置画布尺寸适应容器
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
}

function resizeCanvas() {
    const centerView = document.querySelector('.center-view');
    const rect = centerView.getBoundingClientRect();
    canvas.width = rect.width - 40;
    canvas.height = rect.height - 40;
}

// 初始化事件监听
function initEventListeners() {
    // 星座选择
    const constellationSelect = document.querySelector('.constellation-select');
    constellationSelect.addEventListener('change', function() {
        selectedConstellation = this.value;
        generateSatellites();
    });

    // 按钮事件
    document.querySelector('.btn-primary').addEventListener('click', selectConstellation);
    document.querySelectorAll('.btn-secondary')[0].addEventListener('click', resetView);
    document.querySelectorAll('.btn-secondary')[1].addEventListener('click', addConstellation);
    
    // 路径计算按钮
    const computeBtn = document.querySelector('.control-group:nth-child(2) .btn-primary');
    computeBtn.addEventListener('click', computePath);
    const clearPathBtn = document.querySelector('.control-group:nth-child(2) .btn-secondary');
    clearPathBtn.addEventListener('click', clearPath);

    // 运行按钮
    const runBtn = document.querySelector('.btn-run');
    runBtn.addEventListener('click', toggleSimulation);

    // Tab按钮
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// 生成卫星数据
function generateSatellites() {
    satellites = [];
    const earthCenterX = canvas.width / 2;
    const earthCenterY = canvas.height / 2;
    const earthRadius = Math.min(canvas.width, canvas.height) * 0.15;
    
    let satelliteCount;
    switch(selectedConstellation) {
        case 'kuiper_630':
            satelliteCount = 630;
            break;
        case 'starlink':
            satelliteCount = 400;
            break;
        case 'oneweb':
            satelliteCount = 300;
            break;
        default:
            satelliteCount = 630;
    }

    // 生成多层轨道
    const orbitLayers = 8;
    const satellitesPerLayer = Math.floor(satelliteCount / orbitLayers);
    
    for (let layer = 0; layer < orbitLayers; layer++) {
        const orbitRadius = earthRadius + 80 + (layer * 25);
        const inclination = (layer * 20) - 80; // 轨道倾角
        
        for (let i = 0; i < satellitesPerLayer; i++) {
            const angle = (i / satellitesPerLayer) * Math.PI * 2;
            const phaseOffset = layer * 0.5; // 不同层的相位偏移
            
            satellites.push({
                orbitRadius: orbitRadius,
                angle: angle + phaseOffset,
                inclination: inclination,
                speed: 0.01 + Math.random() * 0.02,
                layer: layer,
                active: true
            });
        }
    }
}

// 绘制地球
function drawEarth() {
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(canvas.width, canvas.height) * 0.15;

    // 地球外观
    const gradient = ctx.createRadialGradient(
        centerX - radius * 0.3, centerY - radius * 0.3, radius * 0.1,
        centerX, centerY, radius
    );
    gradient.addColorStop(0, '#4a9eff');
    gradient.addColorStop(0.3, '#2b8cff');
    gradient.addColorStop(0.7, '#1a5cb8');
    gradient.addColorStop(1, '#0d2c5c');

    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    ctx.fill();

    // 大陆轮廓 (简化版)
    ctx.fillStyle = '#2d5a2d';
    ctx.save();
    ctx.translate(centerX, centerY);
    ctx.rotate(earthRotation);
    
    // 绘制简化的大陆
    drawContinents(radius);
    
    ctx.restore();

    // 地球边框
    ctx.strokeStyle = '#6698ff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    ctx.stroke();
}

// 绘制简化的大陆
function drawContinents(radius) {
    ctx.fillStyle = '#3d6a3d';
    
    // 非洲/欧洲
    ctx.beginPath();
    ctx.ellipse(0, -radius * 0.2, radius * 0.3, radius * 0.6, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 亚洲
    ctx.beginPath();
    ctx.ellipse(radius * 0.4, -radius * 0.1, radius * 0.4, radius * 0.5, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 北美
    ctx.beginPath();
    ctx.ellipse(-radius * 0.5, -radius * 0.3, radius * 0.3, radius * 0.4, 0, 0, Math.PI * 2);
    ctx.fill();
    
    // 南美
    ctx.beginPath();
    ctx.ellipse(-radius * 0.4, radius * 0.4, radius * 0.2, radius * 0.3, 0, 0, Math.PI * 2);
    ctx.fill();
}

// 绘制卫星
function drawSatellites() {
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    satellites.forEach(satellite => {
        if (!satellite.active) return;
        
        // 计算3D位置
        const x = centerX + Math.cos(satellite.angle) * satellite.orbitRadius;
        const y = centerY + Math.sin(satellite.angle) * satellite.orbitRadius * Math.cos(satellite.inclination * Math.PI / 180);
        const z = Math.sin(satellite.angle) * satellite.orbitRadius * Math.sin(satellite.inclination * Math.PI / 180);
        
        // Z轴影响大小和透明度
        const scale = 1 + z / 1000;
        const alpha = 0.6 + (z / 1000) * 0.4;
        
        // 绘制卫星
        ctx.save();
        ctx.globalAlpha = Math.max(0.3, alpha);
        
        // 卫星本体
        ctx.fillStyle = '#ffaa00';
        ctx.beginPath();
        ctx.arc(x, y, 2 * scale, 0, Math.PI * 2);
        ctx.fill();
        
        // 太阳能板
        ctx.fillStyle = '#4444ff';
        ctx.fillRect(x - 4 * scale, y - 1, 8 * scale, 2);
        
        ctx.restore();
    });
}

// 绘制轨道
function drawOrbits() {
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    ctx.strokeStyle = 'rgba(100, 150, 255, 0.2)';
    ctx.lineWidth = 1;
    
    // 绘制轨道圈
    for (let layer = 0; layer < 8; layer++) {
        const radius = Math.min(canvas.width, canvas.height) * 0.15 + 80 + (layer * 25);
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.stroke();
    }
}

// 主渲染函数
function render() {
    // 清空画布
    ctx.fillStyle = 'rgba(0, 0, 20, 0.1)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 星空背景
    drawStars();
    
    // 绘制轨道
    drawOrbits();
    
    // 绘制地球
    drawEarth();
    
    // 绘制卫星
    drawSatellites();
    
    // 更新动画
    if (simulationRunning) {
        updateAnimation();
    }
}

// 绘制星空背景
function drawStars() {
    ctx.fillStyle = '#ffffff';
    for (let i = 0; i < 100; i++) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        const size = Math.random() * 1.5;
        
        ctx.globalAlpha = Math.random() * 0.8 + 0.2;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();
    }
    ctx.globalAlpha = 1;
}

// 更新动画
function updateAnimation() {
    earthRotation += 0.005;
    
    satellites.forEach(satellite => {
        satellite.angle += satellite.speed;
        if (satellite.angle > Math.PI * 2) {
            satellite.angle -= Math.PI * 2;
        }
    });
}

// 开始动画循环
function startAnimation() {
    function animate() {
        render();
        animationId = requestAnimationFrame(animate);
    }
    animate();
}

// 按钮功能实现
function selectConstellation() {
    const select = document.querySelector('.constellation-select');
    selectedConstellation = select.value;
    generateSatellites();
    
    // 更新卫星信息显示
    const infoContent = document.querySelector('.info-content');
    infoContent.innerHTML = `
        <div style="color: #00ff00; font-size: 11px;">
            星座: ${selectedConstellation}<br>
            卫星数量: ${satellites.length}<br>
            轨道层数: 8<br>
            状态: 活跃
        </div>
    `;
}

function resetView() {
    earthRotation = 0;
    satellites.forEach(satellite => {
        satellite.angle = Math.random() * Math.PI * 2;
    });
}

function addConstellation() {
    alert('添加星座功能开发中...');
}

function computePath() {
    const sourceSelect = document.querySelector('.dropdown-row .path-select:first-child');
    const destSelect = document.querySelector('.dropdown-row .path-select:last-child');
    
    if (sourceSelect.value && destSelect.value) {
        alert(`计算从 ${sourceSelect.value} 到 ${destSelect.value} 的路径...`);
        // 这里可以添加路径计算和显示逻辑
    } else {
        alert('请选择源和目标终端');
    }
}

function clearPath() {
    const selects = document.querySelectorAll('.dropdown-row .path-select');
    selects.forEach(select => select.value = '');
    alert('路径已清除');
}

function toggleSimulation() {
    const runBtn = document.querySelector('.btn-run');
    const statusValue = document.querySelector('.status-value');
    
    simulationRunning = !simulationRunning;
    
    if (simulationRunning) {
        runBtn.textContent = 'Stop';
        runBtn.style.backgroundColor = '#cc0000';
        statusValue.textContent = 'Running';
        statusValue.style.color = '#00ff00';
    } else {
        runBtn.textContent = 'Run';
        runBtn.style.backgroundColor = '#00aa00';
        statusValue.textContent = 'Stopped';
        statusValue.style.color = '#ff6666';
    }
}

// 更新时间轴
function updateTimeline() {
    const progressBar = document.querySelector('.progress-bar');
    const timeMarkers = document.querySelectorAll('.time-markers span');
    
    setInterval(() => {
        const now = new Date();
        const currentWidth = parseFloat(progressBar.style.width) || 30;
        const newWidth = (currentWidth + 0.5) % 100;
        progressBar.style.width = newWidth + '%';
        
        // 更新时间标记 (示例)
        timeMarkers.forEach((marker, index) => {
            const time = new Date(now.getTime() + index * 2 * 60 * 1000);
            marker.textContent = time.toISOString().replace('T', ' ').substring(0, 19) + ' UTC';
        });
    }, 1000);
}

// 启动时间轴更新
setTimeout(updateTimeline, 1000); 