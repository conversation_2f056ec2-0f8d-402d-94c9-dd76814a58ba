import pytest
import time
from engine.event import EventType
from engine.network_events import (
    PacketArrivalEvent,
    LinkStateEvent,
    RouteUpdateEvent,
    create_packet_arrival,
    create_link_up,
    create_link_down,
    create_route_update,
)

def test_packet_arrival_event():
    """Test PacketArrivalEvent creation."""
    event = PacketArrivalEvent(
        "pa1", 10.0, "src", "dst", "pkt1", 1500
    )
    assert event.event_type == EventType.PACKET_ARRIVAL
    assert event.packet_id == "pkt1"

def test_link_state_event():
    """Test LinkStateEvent for both link up and down."""
    t = time.time()
    link_up = LinkStateEvent("lu1", t, "link1", True, "A", "B")
    assert link_up.event_type == EventType.LINK_UP
    assert link_up.is_up
    assert link_up.get_data('link_id') == "link1"

    link_down = LinkStateEvent("ld1", t, "link2", False, "C", "D")
    assert link_down.event_type == EventType.LINK_DOWN
    assert not link_down.is_up

def test_route_update_event():
    """Test RouteUpdateEvent creation."""
    event = RouteUpdateEvent("ru1", 20.0, "nodeA", "nodeB", "nodeC", 2.5)
    assert event.event_type == EventType.ROUTE_UPDATE
    assert event.next_hop == "nodeC"
    assert event.metric == 2.5

def test_event_factories():
    """Test the event factory functions."""
    t = time.time()
    arrival = create_packet_arrival("f_pa1", t, "s", "d", "p1", 1024)
    assert isinstance(arrival, PacketArrivalEvent)
    assert arrival.packet_size == 1024
    
    link_up = create_link_up("f_lu1", t, "l1", "a", "b")
    assert link_up.is_up
    
    link_down = create_link_down("f_ld1", t, "l2", "c", "d")
    assert not link_down.is_up
    
    route_update = create_route_update("f_ru1", t, "n1", "n2", "n3", 5.0)
    assert route_update.metric == 5.0 