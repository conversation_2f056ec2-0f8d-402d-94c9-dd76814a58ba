version: '3.8'

services:
  redis:
    image: redis:6-alpine
    container_name: sdt-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  backend:
    build: ./backend
    container_name: sdt-backend
    ports:
      - "5000:5000"
    depends_on:
      - redis
    environment:
      - FLASK_ENV=production
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs

  frontend:
    build: ./frontend
    container_name: sdt-frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  redis_data:
