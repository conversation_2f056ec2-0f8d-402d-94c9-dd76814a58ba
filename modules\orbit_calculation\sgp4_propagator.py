#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SGP4轨道传播器
实现SGP4/SDP4算法进行轨道传播
"""

import math
import numpy as np
from typing import Tuple, Union
from .tle import TLE


# 定义儒略日数据类型
JulianDateDType = np.float64


class SGP4Error(Exception):
    """SGP4计算错误"""
    pass


class SGP4Propagator:
    """
    SGP4轨道传播器
    按照设计文档要求实现SGP4/SDP4算法
    """
    
    # WGS72/WGS84物理常数
    EARTH_MU = 3.986004418e14  # m³/s² 地球引力参数
    EARTH_RADIUS = 6378.137    # km 地球半径
    J2 = 1.08262998905e-3      # J2项系数
    J3 = -2.53265648e-6        # J3项系数  
    J4 = -1.61098761e-6        # J4项系数
    KE = 0.07436685316         # ke常数
    
    # 单位转换常数
    DEG_TO_RAD = math.pi / 180.0
    RAD_TO_DEG = 180.0 / math.pi
    MIN_PER_DAY = 1440.0
    SEC_PER_DAY = 86400.0
    
    def __init__(self):
        """初始化SGP4传播器"""
        self.initialized = False
        self.tle_data = None
        self.sgp4_params = {}
    
    def propagate(self, tle: TLE, jd_utc: JulianDateDType) -> Tuple[np.ndarray, np.ndarray, int]:
        """
        使用SGP4算法传播轨道
        
        Args:
            tle: TLE对象
            jd_utc: 儒略日UTC时间
            
        Returns:
            tuple: (位置向量r(km), 速度向量v(km/s), 错误码)
            
        Raises:
            SGP4Error: 传播计算错误
        """
        try:
            # 初始化SGP4参数（如果需要）
            if not self.initialized or self.tle_data != tle:
                self._initialize_sgp4(tle)
            
            # 计算时间差（分钟）
            epoch_jd = self._tle_epoch_to_jd(tle)
            tsince = (jd_utc - epoch_jd) * self.MIN_PER_DAY
            
            # 选择模型（SGP4 vs SDP4）
            if self.sgp4_params['is_deep_space']:
                return self._sdp4_propagate(tsince)
            else:
                return self._sgp4_propagate(tsince)
                
        except Exception as e:
            raise SGP4Error(f"SGP4传播失败: {str(e)}")
    
    def _initialize_sgp4(self, tle: TLE):
        """
        初始化SGP4参数
        
        Args:
            tle: TLE对象
        """
        # 转换轨道根数为SGP4内部单位
        n0 = tle.mean_motion * 2.0 * math.pi / self.MIN_PER_DAY  # rad/min
        e0 = tle.eccentricity
        i0 = tle.inclination * self.DEG_TO_RAD  # rad
        omega0 = tle.arg_of_perigee * self.DEG_TO_RAD  # rad
        Omega0 = tle.raan * self.DEG_TO_RAD  # rad
        M0 = tle.mean_anomaly * self.DEG_TO_RAD  # rad
        
        # 计算轨道周期
        period = 2.0 * math.pi / n0  # 分钟
        
        # 判断是否为深空轨道（周期 >= 225分钟）
        is_deep_space = period >= 225.0
        
        # 存储参数
        self.sgp4_params = {
            'n0': n0,
            'e0': e0,
            'i0': i0,
            'omega0': omega0,
            'Omega0': Omega0,
            'M0': M0,
            'bstar': tle.bstar_drag,
            'ndot': tle.mean_motion_dot * math.pi / (self.MIN_PER_DAY * self.MIN_PER_DAY),
            'nddot': tle.mean_motion_ddot * math.pi / (self.MIN_PER_DAY * self.MIN_PER_DAY * self.MIN_PER_DAY),
            'is_deep_space': is_deep_space,
            'period': period
        }
        
        # 计算常用参数
        self._compute_common_parameters()
        
        self.tle_data = tle
        self.initialized = True
    
    def _compute_common_parameters(self):
        """计算常用参数"""
        params = self.sgp4_params
        
        # 半长轴
        a1 = (self.KE / params['n0']) ** (2.0/3.0)
        
        # J2修正
        cosio = math.cos(params['i0'])
        theta2 = cosio * cosio
        x3thm1 = 3.0 * theta2 - 1.0
        eosq = params['e0'] * params['e0']
        betao2 = 1.0 - eosq
        betao = math.sqrt(betao2)
        
        del1 = 1.5 * self.J2 * x3thm1 / (a1 * a1 * betao * betao2)
        ao = a1 * (1.0 - del1 * (0.5 * (2.0/3.0) + del1 * (1.0 + 134.0/81.0 * del1)))
        delo = 1.5 * self.J2 * x3thm1 / (ao * ao * betao * betao2)
        
        # 更新参数
        params.update({
            'a1': a1,
            'ao': ao,
            'delo': delo,
            'cosio': cosio,
            'sinio': math.sin(params['i0']),
            'betao': betao,
            'betao2': betao2,
            'theta2': theta2,
            'x3thm1': x3thm1
        })
    
    def _sgp4_propagate(self, tsince: float) -> Tuple[np.ndarray, np.ndarray, int]:
        """
        SGP4传播（近地轨道）
        
        Args:
            tsince: 时间差（分钟）
            
        Returns:
            tuple: (位置向量, 速度向量, 错误码)
        """
        params = self.sgp4_params
        
        # 长期项摄动修正
        omega = params['omega0']
        Omega = params['Omega0'] 
        M = params['M0'] + params['n0'] * tsince
        
        # 应用大气阻力效应
        if abs(params['bstar']) > 1e-9:
            # 简化的大气阻力修正
            drag_factor = 1.0 - params['bstar'] * tsince
            if drag_factor <= 0:
                return np.zeros(3), np.zeros(3), 1  # 轨道衰减错误
        
        # 求解开普勒方程
        E = self._solve_kepler_equation(M, params['e0'])
        
        # 计算真近点角
        beta = math.sqrt(1.0 - params['e0'] * params['e0'])
        nu = 2.0 * math.atan2(
            math.sqrt(1.0 + params['e0']) * math.sin(E/2.0),
            math.sqrt(1.0 - params['e0']) * math.cos(E/2.0)
        )
        
        # 计算轨道半径
        r = params['ao'] * (1.0 - params['e0'] * math.cos(E))
        
        # 轨道平面内的位置和速度
        cos_nu = math.cos(nu)
        sin_nu = math.sin(nu)
        
        x_orb = r * cos_nu
        y_orb = r * sin_nu
        z_orb = 0.0
        
        # 轨道平面内的速度
        n = math.sqrt(self.EARTH_MU / (params['ao'] * 1000)**3) * 1000  # km/s
        vx_orb = -n * params['ao'] * math.sin(E) / (1.0 - params['e0'] * math.cos(E))
        vy_orb = n * params['ao'] * beta * math.cos(E) / (1.0 - params['e0'] * math.cos(E))
        vz_orb = 0.0
        
        # 转换到TEME坐标系
        position, velocity = self._orbital_to_teme(
            np.array([x_orb, y_orb, z_orb]),
            np.array([vx_orb, vy_orb, vz_orb]),
            params['i0'], omega, Omega
        )
        
        return position, velocity, 0
    
    def _sdp4_propagate(self, tsince: float) -> Tuple[np.ndarray, np.ndarray, int]:
        """
        SDP4传播（深空轨道）
        
        Args:
            tsince: 时间差（分钟）
            
        Returns:
            tuple: (位置向量, 速度向量, 错误码)
        """
        # 简化的SDP4实现，实际应用中需要完整的深空摄动修正
        # 这里先使用SGP4逻辑，后续可以扩展
        return self._sgp4_propagate(tsince)
    
    def _solve_kepler_equation(self, M: float, e: float, tolerance: float = 1e-12, max_iter: int = 50) -> float:
        """
        求解开普勒方程 M = E - e*sin(E)
        
        Args:
            M: 平近点角
            e: 偏心率
            tolerance: 收敛精度
            max_iter: 最大迭代次数
            
        Returns:
            偏近点角E
        """
        # 初始猜测
        E = M if e < 0.8 else math.pi
        
        # 牛顿迭代法
        for _ in range(max_iter):
            f = E - e * math.sin(E) - M
            df = 1.0 - e * math.cos(E)
            
            if abs(df) < 1e-15:
                break
                
            dE = -f / df
            E += dE
            
            if abs(dE) < tolerance:
                break
        
        return E
    
    def _orbital_to_teme(self, r_orb: np.ndarray, v_orb: np.ndarray, 
                        i: float, omega: float, Omega: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        将轨道平面坐标转换为TEME坐标系
        
        Args:
            r_orb: 轨道平面位置向量
            v_orb: 轨道平面速度向量
            i: 轨道倾角
            omega: 近地点幅角
            Omega: 升交点赤经
            
        Returns:
            tuple: (TEME位置向量, TEME速度向量)
        """
        # 旋转矩阵
        cos_omega = math.cos(omega)
        sin_omega = math.sin(omega)
        cos_i = math.cos(i)
        sin_i = math.sin(i)
        cos_Omega = math.cos(Omega)
        sin_Omega = math.sin(Omega)
        
        # 组合旋转矩阵
        R11 = cos_Omega * cos_omega - sin_Omega * sin_omega * cos_i
        R12 = -cos_Omega * sin_omega - sin_Omega * cos_omega * cos_i
        R13 = sin_Omega * sin_i
        
        R21 = sin_Omega * cos_omega + cos_Omega * sin_omega * cos_i
        R22 = -sin_Omega * sin_omega + cos_Omega * cos_omega * cos_i
        R23 = -cos_Omega * sin_i
        
        R31 = sin_omega * sin_i
        R32 = cos_omega * sin_i
        R33 = cos_i
        
        # 应用旋转
        r_teme = np.array([
            R11 * r_orb[0] + R12 * r_orb[1] + R13 * r_orb[2],
            R21 * r_orb[0] + R22 * r_orb[1] + R23 * r_orb[2],
            R31 * r_orb[0] + R32 * r_orb[1] + R33 * r_orb[2]
        ])
        
        v_teme = np.array([
            R11 * v_orb[0] + R12 * v_orb[1] + R13 * v_orb[2],
            R21 * v_orb[0] + R22 * v_orb[1] + R23 * v_orb[2],
            R31 * v_orb[0] + R32 * v_orb[1] + R33 * v_orb[2]
        ])
        
        return r_teme, v_teme
    
    def _tle_epoch_to_jd(self, tle: TLE) -> float:
        """
        将TLE历元转换为儒略日
        
        Args:
            tle: TLE对象
            
        Returns:
            儒略日
        """
        year = tle.epoch_year
        day_of_year = tle.epoch_day
        
        # 计算年初的儒略日
        if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0):
            # 闰年
            jd_year_start = 367 * year - int(7 * (year + int((1 + 9) / 12)) / 4) + int(275 * 1 / 9) + 1 + 1721013.5
        else:
            # 平年
            jd_year_start = 367 * year - int(7 * (year + int((1 + 9) / 12)) / 4) + int(275 * 1 / 9) + 1 + 1721013.5
        
        # 简化计算：使用标准公式
        a = int((14 - 1) / 12)
        y = year - a
        m = 1 + 12 * a - 1
        
        jd_jan1 = int(365.25 * (y + 4716)) + int(30.6001 * (m + 1)) + 1 - int(y/100) + int(y/400) - 1524.5
        
        return jd_jan1 + day_of_year - 1.0
