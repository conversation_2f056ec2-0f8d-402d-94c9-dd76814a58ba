<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDT卫星数字孪生系统 v1.0</title>
    <link rel="stylesheet" href="styles_v2.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>SDT卫星数字孪生系统 v1.0</h1>
            <p>Satellite Digital Twin System - 重构版本</p>
            <div class="system-status">
                <span id="connection-status" class="status-indicator">连接中...</span>
                <span id="api-status" class="status-indicator">API检查中...</span>
            </div>
        </header>

        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="orbit">轨道计算</button>
            <button class="tab-btn" data-tab="twin">数字孪生</button>
            <button class="tab-btn" data-tab="topology">拓扑仿真</button>
            <button class="tab-btn" data-tab="realtime">实时监控</button>
        </nav>

        <!-- 轨道计算模块 -->
        <div id="orbit" class="tab-content active">
            <div class="module-header">
                <h2>轨道计算模块 (Module I)</h2>
                <p>基于TLE数据进行SGP4轨道传播计算</p>
            </div>
            
            <div class="input-section">
                <h3>TLE数据输入</h3>
                <div class="tle-input-container">
                    <textarea id="tle-input" placeholder="请输入TLE数据...
例如:
ISS (ZARYA)
1 25544U 98067A   23001.00000000  .00002182  00000-0  40768-4 0  9990
2 25544  51.6461 339.7939 0001220  92.8340 267.3124 15.49309620368473"></textarea>
                    <button id="parse-tle" class="btn-secondary">解析TLE</button>
                </div>
                
                <div id="tle-info" class="tle-info hidden"></div>
                
                <div class="time-inputs">
                    <label>
                        开始时间:
                        <input type="datetime-local" id="start-time" value="2024-01-01T00:00">
                    </label>
                    <label>
                        结束时间:
                        <input type="datetime-local" id="end-time" value="2024-01-01T01:00">
                    </label>
                    <label>
                        时间步长(秒):
                        <input type="number" id="time-step" value="60" min="1" max="3600">
                    </label>
                </div>
                
                <button id="calculate-orbit" class="btn-primary" disabled>计算轨道</button>
            </div>
            
            <div class="results-section">
                <h3>计算结果</h3>
                <div id="orbit-results"></div>
                <div class="chart-container">
                    <canvas id="orbit-chart" width="800" height="400"></canvas>
                </div>
            </div>
        </div>

        <!-- 数字孪生模块 -->
        <div id="twin" class="tab-content">
            <div class="module-header">
                <h2>数字孪生模型构建模块 (Module II)</h2>
                <p>高保真物理引擎和数值积分</p>
            </div>
            
            <div class="input-section">
                <h3>初始状态设置</h3>
                <div class="state-inputs">
                    <div class="input-group">
                        <label>位置 (km):</label>
                        <input type="number" id="pos-x" placeholder="X" value="6678">
                        <input type="number" id="pos-y" placeholder="Y" value="0">
                        <input type="number" id="pos-z" placeholder="Z" value="0">
                    </div>
                    <div class="input-group">
                        <label>速度 (km/s):</label>
                        <input type="number" id="vel-x" placeholder="VX" value="0" step="0.001">
                        <input type="number" id="vel-y" placeholder="VY" value="7.727" step="0.001">
                        <input type="number" id="vel-z" placeholder="VZ" value="0" step="0.001">
                    </div>
                </div>
                
                <h3>卫星属性</h3>
                <div class="satellite-inputs">
                    <div class="input-group">
                        <label>质量 (kg):</label>
                        <input type="number" id="sat-mass" value="1000" min="1">
                    </div>
                    <div class="input-group">
                        <label>阻力面积 (m²):</label>
                        <input type="number" id="sat-drag-area" value="10" min="0" step="0.1">
                    </div>
                    <div class="input-group">
                        <label>阻力系数:</label>
                        <input type="number" id="sat-drag-coeff" value="2.2" min="0" step="0.1">
                    </div>
                </div>
                
                <h3>摄动设置</h3>
                <div class="perturbation-settings">
                    <label><input type="checkbox" id="include-j2" checked> J2摄动</label>
                    <label><input type="checkbox" id="include-drag" checked> 大气阻力</label>
                    <label><input type="checkbox" id="include-srp" checked> 太阳辐射压</label>
                </div>
                
                <div class="time-inputs">
                    <label>
                        开始时间:
                        <input type="datetime-local" id="twin-start-time" value="2024-01-01T00:00">
                    </label>
                    <label>
                        结束时间:
                        <input type="datetime-local" id="twin-end-time" value="2024-01-01T01:00">
                    </label>
                    <label>
                        时间步长(秒):
                        <input type="number" id="twin-time-step" value="60" min="1" max="3600">
                    </label>
                </div>
                
                <button id="simulate-orbit" class="btn-primary">开始仿真</button>
            </div>
            
            <div class="results-section">
                <h3>仿真结果</h3>
                <div id="twin-results"></div>
                <div class="chart-container">
                    <canvas id="twin-chart" width="800" height="400"></canvas>
                </div>
            </div>
        </div>

        <!-- 拓扑仿真模块 -->
        <div id="topology" class="tab-content">
            <div class="module-header">
                <h2>卫星拓扑模拟模块 (Module III)</h2>
                <p>网络拓扑分析和路由算法</p>
            </div>
            
            <div class="input-section">
                <h3>拓扑参数</h3>
                <div class="topology-inputs">
                    <div class="input-group">
                        <label>最大链路距离 (km):</label>
                        <input type="number" id="max-link-distance" value="5000" min="100">
                    </div>
                    <div class="input-group">
                        <label>地球半径 (km):</label>
                        <input type="number" id="earth-radius" value="6378.137" step="0.001">
                    </div>
                </div>
                
                <h3>星历数据</h3>
                <div class="ephemeris-section">
                    <p>请先在轨道计算或数字孪生模块中生成星历数据</p>
                    <div id="ephemeris-status" class="status-info">无可用星历数据</div>
                </div>
                
                <button id="analyze-topology" class="btn-primary" disabled>分析拓扑</button>
            </div>
            
            <div class="results-section">
                <h3>拓扑分析结果</h3>
                <div id="topology-results"></div>
                <div class="chart-container">
                    <canvas id="topology-chart" width="800" height="400"></canvas>
                </div>
            </div>
        </div>

        <!-- 实时监控模块 -->
        <div id="realtime" class="tab-content">
            <div class="module-header">
                <h2>实时监控</h2>
                <p>WebSocket实时数据监控</p>
            </div>
            
            <div class="monitoring-section">
                <div class="connection-info">
                    <h3>连接状态</h3>
                    <div id="ws-status" class="status-info">未连接</div>
                    <button id="connect-ws" class="btn-secondary">连接WebSocket</button>
                </div>
                
                <div class="real-time-data">
                    <h3>实时数据</h3>
                    <div id="realtime-log" class="log-container"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading" class="loading hidden">
        <div class="spinner"></div>
        <p>处理中...</p>
    </div>

    <!-- 错误提示 -->
    <div id="error-toast" class="toast error hidden">
        <span id="error-message"></span>
        <button class="toast-close">&times;</button>
    </div>

    <!-- 成功提示 -->
    <div id="success-toast" class="toast success hidden">
        <span id="success-message"></span>
        <button class="toast-close">&times;</button>
    </div>

    <script src="app_v2.js"></script>
</body>
</html>
