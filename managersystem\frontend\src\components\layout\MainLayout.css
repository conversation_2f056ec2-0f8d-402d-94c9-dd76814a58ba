.main-layout {
  height: 100vh;
  overflow: hidden;
}

/* 侧边栏样式 */
.main-sider {
  background: #001529;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.sider-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #1f1f1f;
  margin-bottom: 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #fff;
}

.logo-icon {
  font-size: 24px;
  color: #1890ff;
}

.logo-text {
  color: #fff !important;
  margin: 0 !important;
  font-size: 18px;
  font-weight: 600;
}

.main-menu {
  border-right: none;
}

.main-menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

.main-menu .ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.main-menu .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.main-menu .ant-menu-item-selected::after {
  display: none;
}

/* 主内容区样式 */
.main-content-layout {
  background: #f0f2f5;
}

/* 顶部导航栏样式 */
.main-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 99;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-trigger {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s;
}

.sidebar-trigger:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.header-right {
  display: flex;
  align-items: center;
}

.header-action {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 16px;
  transition: all 0.3s;
}

.header-action:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.user-avatar {
  background-color: #1890ff;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  line-height: 1.2;
}

/* 内容区域样式 */
.main-content {
  margin: 0;
  padding: 0;
  background: #f0f2f5;
  overflow-y: auto;
  height: calc(100vh - 64px);
}

.content-wrapper {
  padding: 24px;
  min-height: calc(100vh - 64px - 48px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-header {
    padding: 0 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .user-details {
    display: none;
  }
  
  .user-info {
    padding: 8px;
  }
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.main-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.main-sider {
  transition: all 0.2s;
}

.main-menu .ant-menu-item {
  transition: all 0.3s;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .main-header {
    background: #141414;
    border-bottom-color: #303030;
  }
  
  .main-content {
    background: #000;
  }
  
  .content-wrapper {
    background: #000;
  }
  
  .user-name {
    color: rgba(255, 255, 255, 0.85);
  }
  
  .user-role {
    color: rgba(255, 255, 255, 0.45);
  }
}

/* 加载状态 */
.content-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 面包屑样式 */
.content-breadcrumb {
  margin-bottom: 16px;
}

.content-breadcrumb .ant-breadcrumb {
  font-size: 14px;
}

/* 页面标题样式 */
.page-header {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.page-header .ant-page-header {
  padding: 0;
}

/* 卡片样式 */
.content-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}

.content-card .ant-card-body {
  padding: 24px;
}

/* 表格样式 */
.content-table {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.content-table .ant-table {
  border-radius: 8px;
}

/* 表单样式 */
.content-form {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

/* 统计卡片样式 */
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
}

.stats-card .ant-card-body {
  padding: 20px;
}

.stats-card .ant-statistic-title {
  color: rgba(255, 255, 255, 0.8);
}

.stats-card .ant-statistic-content {
  color: #fff;
}
