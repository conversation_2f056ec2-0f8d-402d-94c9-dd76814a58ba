#!/usr/bin/env python3
"""
快速测试脚本 - 验证网络离散事件引擎基本功能
"""

def test_basic_functionality():
    print("=" * 50)
    print("网络离散事件引擎 - 快速功能测试")
    print("=" * 50)
    
    try:
        # 导入模块
        from .simulation_engine import SimulationEngine
        from .event import EventType
        from .network_events import create_packet_arrival, create_link_up
        
        print("✅ 模块导入成功")
        
        # 创建仿真引擎
        engine = SimulationEngine(max_simulation_time=5.0)
        print("✅ 仿真引擎创建成功")
        
        # 调度一些测试事件
        events_scheduled = 0
        
        # 数据包事件
        for i in range(3):
            event = create_packet_arrival(
                event_id=f'test_packet_{i}',
                scheduled_time=float(i + 1),
                source='node_A',
                destination='node_B', 
                packet_id=f'packet_{i:03d}',
                packet_size=1024,
                protocol='TCP'
            )
            if engine.schedule_event(event):
                events_scheduled += 1
        
        # 链路事件
        link_event = create_link_up(
            event_id='link_test',
            scheduled_time=0.5,
            link_id='link_AB',
            node_a='node_A',
            node_b='node_B'
        )
        if engine.schedule_event(link_event):
            events_scheduled += 1
        
        print(f"✅ 成功调度 {events_scheduled} 个事件")
        
        # 运行仿真
        print("🚀 开始仿真...")
        success = engine.run()
        
        if success:
            stats = engine.get_statistics()
            print("\n📊 仿真结果:")
            print(f"   状态: {stats['current_state']}")
            print(f"   仿真时间: {engine.get_current_time():.2f}")
            print(f"   处理事件: {stats['simulation']['total_events_processed']}")
            print(f"   调度事件: {stats['simulation']['total_events_scheduled']}")
            print(f"   队列大小: {stats['queue_size']}")
            
            # 验证结果
            if stats['simulation']['total_events_processed'] == events_scheduled:
                print("\n🎉 所有功能测试通过！")
                print("   网络离散事件引擎工作正常。")
                return True
            else:
                print("\n⚠️  事件处理数量不符合预期")
                return False
        else:
            print("\n❌ 仿真运行失败")
            return False
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_event_types():
    """测试不同的事件类型"""
    print("\n" + "=" * 30)
    print("测试网络事件类型")
    print("=" * 30)
    
    try:
        from .simulation_engine import SimulationEngine
        from .network_events import (create_packet_arrival, create_packet_departure, 
                                   create_link_up, create_link_down, create_route_update)
        
        engine = SimulationEngine(max_simulation_time=10.0)
        
        # 测试各种事件类型
        events = [
            create_packet_arrival('pkt_arr', 1.0, 'A', 'B', 'p1', 1024),
            create_packet_departure('pkt_dep', 2.0, 'B', 'C', 'p1', 1024),
            create_link_up('link_up', 3.0, 'link1', 'A', 'B'),
            create_link_down('link_down', 4.0, 'link1', 'A', 'B'),
            create_route_update('route_up', 5.0, 'A', 'C', 'B', 1.5)
        ]
        
        scheduled_count = 0
        for event in events:
            if engine.schedule_event(event):
                scheduled_count += 1
        
        print(f"✅ 调度了 {scheduled_count} 个不同类型的事件")
        
        # 运行仿真
        success = engine.run()
        
        if success:
            stats = engine.get_statistics()
            print(f"✅ 成功处理 {stats['simulation']['total_events_processed']} 个事件")
            
            # 显示事件类型统计
            if 'events_by_type' in stats['scheduler']:
                print("📈 事件类型统计:")
                for event_type, count in stats['scheduler']['events_by_type'].items():
                    print(f"   {event_type}: {count}")
            
            return True
        else:
            print("❌ 事件类型测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 事件类型测试出错: {e}")
        return False


if __name__ == "__main__":
    success1 = test_basic_functionality()
    success2 = test_event_types()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 所有测试通过！网络离散事件引擎已就绪。")
        print("\n📚 使用说明:")
        print("   - 查看 README.md 了解详细文档")
        print("   - 运行 examples.py 查看完整示例")
        print("   - 运行 test_engine.py 进行全面测试")
    else:
        print("❌ 部分测试失败，请检查代码")
    print("=" * 50) 