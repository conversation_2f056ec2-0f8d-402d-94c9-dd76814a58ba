"""
网络离散事件引擎模块

这个模块提供了一个完整的离散事件仿真引擎，专门用于网络相关的仿真。
包含事件调度、时间管理、网络事件处理等核心功能。
"""

from .event import Event, NetworkEvent
from .scheduler import EventScheduler
from .time_manager import TimeManager
from .network_events import *
from .simulation_engine import SimulationEngine
from .event_handler import EventHandler

__version__ = "1.0.0"
__author__ = "SDT Development Team"

__all__ = [
    'Event',
    'NetworkEvent', 
    'EventScheduler',
    'TimeManager',
    'SimulationEngine',
    'EventHandler'
] 