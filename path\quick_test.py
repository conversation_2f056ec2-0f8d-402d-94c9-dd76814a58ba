"""
路径控制模块快速功能验证脚本

快速验证路径控制模块的基本功能是否正常工作
"""

import math
import time


def quick_test():
    """快速功能测试"""
    print("SDT路径控制模块 - 快速功能验证")
    print("=" * 40)
    
    # 测试1: 模块导入
    print("1. 测试模块导入...")
    try:
        from . import (
            CoordinateSystem, Vector3D, LatLonAlt,
            OrbitCalculator, OrbitParameters,
            PathPlanner, LinkManager,
            PathOptimizer, PathPredictor
        )
        print("✓ 所有模块导入成功")
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False
    
    # 测试2: 基本坐标转换
    print("\n2. 测试坐标转换...")
    try:
        beijing = LatLonAlt(39.9042, 116.4074, 50.0)
        ecef = CoordinateSystem.lla_to_ecef(beijing)
        back = CoordinateSystem.ecef_to_lla(ecef)
        
        lat_diff = abs(beijing.latitude - back.latitude)
        lon_diff = abs(beijing.longitude - back.longitude)
        
        if lat_diff < 1e-6 and lon_diff < 1e-6:
            print("✓ 坐标转换精度正常")
        else:
            print(f"✗ 坐标转换精度异常: lat_diff={lat_diff}, lon_diff={lon_diff}")
            return False
    except Exception as e:
        print(f"✗ 坐标转换测试失败: {e}")
        return False
    
    # 测试3: 轨道计算
    print("\n3. 测试轨道计算...")
    try:
        calc = OrbitCalculator()
        orbit = OrbitParameters(
            semi_major_axis=6778137.0,
            eccentricity=0.001,
            inclination=math.radians(98.7),
            raan=0.0,
            argument_of_perigee=0.0,
            true_anomaly=0.0,
            epoch=2460000.0
        )
        
        state = calc.kepler_to_cartesian(orbit)
        altitude = state.altitude
        
        if 390000 < altitude < 410000:  # 400km ± 10km
            print(f"✓ 轨道计算正常，高度: {altitude/1000:.1f} km")
        else:
            print(f"✗ 轨道计算异常，高度: {altitude/1000:.1f} km")
            return False
    except Exception as e:
        print(f"✗ 轨道计算测试失败: {e}")
        return False
    
    # 测试4: 路径规划
    print("\n4. 测试路径规划...")
    try:
        planner = PathPlanner()
        
        # 创建简单的3节点网络
        from .path_planner import PathNode
        
        node1 = PathNode("node1", Vector3D(6778137, 0, 0), "satellite")
        node2 = PathNode("node2", Vector3D(6778137, 1000000, 0), "satellite")
        node3 = PathNode("node3", Vector3D(6778137, 2000000, 0), "satellite")
        
        planner.add_node(node1)
        planner.add_node(node2)
        planner.add_node(node3)
        
        planner.add_connection("node1", "node2")
        planner.add_connection("node2", "node3")
        
        path = planner.plan_path("node1", "node3", "astar")
        
        if path.success and path.hop_count == 2:
            print("✓ 路径规划正常")
        else:
            print(f"✗ 路径规划异常: success={path.success}, hops={path.hop_count}")
            return False
    except Exception as e:
        print(f"✗ 路径规划测试失败: {e}")
        return False
    
    # 测试5: 链路管理
    print("\n5. 测试链路管理...")
    try:
        link_mgr = LinkManager()
        
        link_id = link_mgr.create_link("sat1", "sat2", "satellite_to_satellite")
        if not link_id:
            print("✗ 链路创建失败")
            return False
        
        pos1 = Vector3D(6778137, 0, 0)
        pos2 = Vector3D(6778137 + 1000000, 0, 0)
        
        link_mgr.update_link_quality(link_id, pos1, pos2)
        
        link = link_mgr.links[link_id]
        if -120 < link.quality.signal_strength < 0:
            print("✓ 链路管理正常")
        else:
            print(f"✗ 链路质量异常: {link.quality.signal_strength}")
            return False
    except Exception as e:
        print(f"✗ 链路管理测试失败: {e}")
        return False
    
    # 测试6: 路径优化
    print("\n6. 测试路径优化...")
    try:
        from .optimization import OptimizationCriteria
        
        optimizer = PathOptimizer()
        criteria = OptimizationCriteria()
        
        # 使用之前创建的路径
        result = optimizer.optimize_path(path, criteria)
        
        if hasattr(result, 'success'):
            print("✓ 路径优化接口正常")
        else:
            print("✗ 路径优化接口异常")
            return False
    except Exception as e:
        print(f"✗ 路径优化测试失败: {e}")
        return False
    
    # 测试7: 路径预测
    print("\n7. 测试路径预测...")
    try:
        predictor = PathPredictor()
        
        # 使用之前创建的卫星状态
        future_time = orbit.epoch + 0.1
        predicted = predictor.predict_satellite_positions([state], future_time)
        
        if len(predicted) == 1 and predicted[0].time == future_time:
            print("✓ 路径预测正常")
        else:
            print("✗ 路径预测异常")
            return False
    except Exception as e:
        print(f"✗ 路径预测测试失败: {e}")
        return False
    
    # 测试8: 性能基准
    print("\n8. 性能基准测试...")
    try:
        start_time = time.time()
        
        # 执行100次坐标转换
        for i in range(100):
            lat = (i % 180) - 90
            lon = (i % 360) - 180
            lla = LatLonAlt(lat, lon, 0)
            ecef = CoordinateSystem.lla_to_ecef(lla)
            back = CoordinateSystem.ecef_to_lla(ecef)
        
        coord_time = time.time() - start_time
        
        start_time = time.time()
        
        # 执行10次轨道传播
        for i in range(10):
            future_time = orbit.epoch + i * 0.01
            future_state = calc.propagate_orbit(state, future_time)
        
        orbit_time = time.time() - start_time
        
        print(f"✓ 性能正常: 坐标转换 {coord_time:.3f}s, 轨道传播 {orbit_time:.3f}s")
        
        if coord_time > 1.0 or orbit_time > 1.0:
            print("⚠ 性能警告: 计算时间较长")
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 所有快速测试通过！路径控制模块工作正常。")
    return True


def test_module_attributes():
    """测试模块属性和版本信息"""
    print("\n模块信息:")
    try:
        from . import __version__, __author__, __all__
        print(f"版本: {__version__}")
        print(f"作者: {__author__}")
        print(f"导出组件数量: {len(__all__)}")
        
        print("\n导出的组件:")
        for component in __all__:
            print(f"  - {component}")
            
    except ImportError as e:
        print(f"模块属性获取失败: {e}")


def main():
    """主函数"""
    try:
        success = quick_test()
        test_module_attributes()
        
        if success:
            print("\n✅ 模块验证完成，可以正常使用！")
        else:
            print("\n❌ 模块验证失败，请检查错误信息！")
            
    except Exception as e:
        print(f"\n💥 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 