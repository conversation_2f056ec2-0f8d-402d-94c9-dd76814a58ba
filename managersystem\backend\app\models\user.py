"""
用户模型
处理用户相关的数据操作
"""

import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any
from werkzeug.security import generate_password_hash, check_password_hash

from app.utils.redis_client import redis_client, RedisKeys


class User:
    """用户模型类"""
    
    # 用户角色定义
    ROLES = {
        'super_admin': '超级管理员',
        'admin': '管理员', 
        'developer': '开发者',
        'tester': '测试员',
        'guest': '访客'
    }
    
    # 用户状态定义
    STATUSES = {
        'active': '激活',
        'inactive': '未激活',
        'disabled': '禁用'
    }
    
    # 权限定义
    PERMISSIONS = {
        'super_admin': [
            'user:create', 'user:read', 'user:update', 'user:delete',
            'issue:create', 'issue:read', 'issue:update', 'issue:delete',
            'document:create', 'document:read', 'document:update', 'document:delete',
            'product:create', 'product:read', 'product:update', 'product:delete',
            'system:config'
        ],
        'admin': [
            'user:create', 'user:read', 'user:update', 'user:delete',
            'issue:create', 'issue:read', 'issue:update', 'issue:delete',
            'document:create', 'document:read', 'document:update', 'document:delete',
            'product:create', 'product:read', 'product:update', 'product:delete'
        ],
        'developer': [
            'issue:create', 'issue:read', 'issue:update',
            'document:create', 'document:read', 'document:update',
            'product:create', 'product:read', 'product:update'
        ],
        'tester': [
            'issue:create', 'issue:read',
            'document:read',
            'product:read'
        ],
        'guest': [
            'document:read',
            'product:read'
        ]
    }
    
    def __init__(self, user_data: Dict[str, Any] = None):
        """初始化用户对象"""
        if user_data:
            self.id = user_data.get('id')
            self.username = user_data.get('username')
            self.email = user_data.get('email')
            self.password_hash = user_data.get('password_hash')
            self.role = user_data.get('role', 'guest')
            self.status = user_data.get('status', 'active')
            self.created_at = user_data.get('created_at')
            self.updated_at = user_data.get('updated_at')
            self.last_login = user_data.get('last_login')
            self.profile = user_data.get('profile', {})
        else:
            self.id = None
            self.username = None
            self.email = None
            self.password_hash = None
            self.role = 'guest'
            self.status = 'active'
            self.created_at = None
            self.updated_at = None
            self.last_login = None
            self.profile = {}
    
    def to_dict(self, include_password: bool = False) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'status': self.status,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'last_login': self.last_login,
            'profile': self.profile,
            'permissions': self.get_permissions()
        }
        
        if include_password:
            data['password_hash'] = self.password_hash
            
        return data
    
    def set_password(self, password: str) -> None:
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """验证密码"""
        if not self.password_hash:
            return False
        return check_password_hash(self.password_hash, password)
    
    def get_permissions(self) -> List[str]:
        """获取用户权限列表"""
        return self.PERMISSIONS.get(self.role, [])
    
    def has_permission(self, permission: str) -> bool:
        """检查是否有指定权限"""
        return permission in self.get_permissions()
    
    def save(self) -> bool:
        """保存用户到Redis"""
        try:
            if not self.id:
                self.id = redis_client.generate_id('user_')
                self.created_at = redis_client.get_timestamp()
            
            self.updated_at = redis_client.get_timestamp()
            
            # 保存用户数据
            user_key = RedisKeys.USER.format(user_id=self.id)
            user_data = self.to_dict(include_password=True)
            
            if not redis_client.set(user_key, user_data):
                return False
            
            # 添加到索引
            index_key = RedisKeys.USER_INDEX
            if not redis_client.add_to_index(index_key, self.id):
                return False
            
            # 创建用户名索引
            username_key = RedisKeys.USER_BY_USERNAME.format(username=self.username)
            if not redis_client.set(username_key, self.id):
                return False
            
            return True
            
        except Exception as e:
            print(f"Error saving user: {e}")
            return False
    
    def delete(self) -> bool:
        """删除用户"""
        try:
            if not self.id:
                return False
            
            # 删除用户数据
            user_key = RedisKeys.USER.format(user_id=self.id)
            redis_client.delete(user_key)
            
            # 从索引中移除
            index_key = RedisKeys.USER_INDEX
            redis_client.remove_from_index(index_key, self.id)
            
            # 删除用户名索引
            if self.username:
                username_key = RedisKeys.USER_BY_USERNAME.format(username=self.username)
                redis_client.delete(username_key)
            
            return True
            
        except Exception as e:
            print(f"Error deleting user: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, user_id: str) -> Optional['User']:
        """根据ID获取用户"""
        try:
            user_key = RedisKeys.USER.format(user_id=user_id)
            user_data = redis_client.get(user_key)
            
            if user_data:
                return cls(user_data)
            return None
            
        except Exception as e:
            print(f"Error getting user by id: {e}")
            return None
    
    @classmethod
    def get_by_username(cls, username: str) -> Optional['User']:
        """根据用户名获取用户"""
        try:
            username_key = RedisKeys.USER_BY_USERNAME.format(username=username)
            user_id = redis_client.get(username_key)
            
            if user_id:
                return cls.get_by_id(user_id)
            return None
            
        except Exception as e:
            print(f"Error getting user by username: {e}")
            return None
    
    @classmethod
    def get_list(cls, page: int = 1, size: int = 20, role: str = None, status: str = None) -> Dict[str, Any]:
        """获取用户列表"""
        try:
            index_key = RedisKeys.USER_INDEX
            
            # 获取所有用户ID
            user_ids = redis_client.get_from_index(index_key, reverse=True)
            
            # 过滤条件
            filtered_ids = []
            for user_id in user_ids:
                user = cls.get_by_id(user_id)
                if user:
                    if role and user.role != role:
                        continue
                    if status and user.status != status:
                        continue
                    filtered_ids.append(user_id)
            
            # 分页
            total = len(filtered_ids)
            start = (page - 1) * size
            end = start + size
            page_ids = filtered_ids[start:end]
            
            # 获取用户数据
            users = []
            for user_id in page_ids:
                user = cls.get_by_id(user_id)
                if user:
                    users.append(user.to_dict())
            
            return {
                'users': users,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total,
                    'pages': (total + size - 1) // size
                }
            }
            
        except Exception as e:
            print(f"Error getting user list: {e}")
            return {
                'users': [],
                'pagination': {'page': 1, 'size': size, 'total': 0, 'pages': 0}
            }
    
    @classmethod
    def create_default_admin(cls) -> Optional['User']:
        """创建默认管理员账户"""
        try:
            # 检查是否已存在管理员
            admin_user = cls.get_by_username('admin')
            if admin_user:
                return admin_user
            
            # 创建默认管理员
            admin = cls()
            admin.username = 'admin'
            admin.email = '<EMAIL>'
            admin.set_password('admin123')
            admin.role = 'super_admin'
            admin.status = 'active'
            admin.profile = {
                'name': '系统管理员',
                'department': '技术部',
                'phone': ''
            }
            
            if admin.save():
                return admin
            return None
            
        except Exception as e:
            print(f"Error creating default admin: {e}")
            return None
    
    @classmethod
    def authenticate(cls, username: str, password: str) -> Optional['User']:
        """用户认证"""
        try:
            user = cls.get_by_username(username)
            if user and user.status == 'active' and user.check_password(password):
                # 更新最后登录时间
                user.last_login = redis_client.get_timestamp()
                user.save()
                return user
            return None
            
        except Exception as e:
            print(f"Error authenticating user: {e}")
            return None
    
    @classmethod
    def get_stats(cls) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            index_key = RedisKeys.USER_INDEX
            user_ids = redis_client.get_from_index(index_key)
            
            stats = {
                'total': len(user_ids),
                'by_role': {},
                'by_status': {},
                'active_today': 0
            }
            
            today = datetime.now().date()
            
            for user_id in user_ids:
                user = cls.get_by_id(user_id)
                if user:
                    # 按角色统计
                    role_name = cls.ROLES.get(user.role, user.role)
                    stats['by_role'][role_name] = stats['by_role'].get(role_name, 0) + 1
                    
                    # 按状态统计
                    status_name = cls.STATUSES.get(user.status, user.status)
                    stats['by_status'][status_name] = stats['by_status'].get(status_name, 0) + 1
                    
                    # 今日活跃用户
                    if user.last_login:
                        try:
                            last_login_date = datetime.fromisoformat(user.last_login).date()
                            if last_login_date == today:
                                stats['active_today'] += 1
                        except:
                            pass
            
            return stats
            
        except Exception as e:
            print(f"Error getting user stats: {e}")
            return {'total': 0, 'by_role': {}, 'by_status': {}, 'active_today': 0}
