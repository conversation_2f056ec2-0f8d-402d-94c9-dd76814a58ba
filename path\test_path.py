"""
路径控制模块测试脚本

对路径控制模块的各个组件进行单元测试，包括：
1. 坐标系统转换测试
2. 轨道计算测试
3. 路径规划测试
4. 链路管理测试
5. 路径优化测试
6. 路径预测测试
"""

import math
import time


def test_coordinate_system():
    """测试坐标系统转换"""
    print("测试坐标系统转换...")
    
    from .coordinate_system import Vector3D, LatLonAlt, CoordinateSystem
    
    # 测试向量运算
    v1 = Vector3D(1, 2, 3)
    v2 = Vector3D(4, 5, 6)
    
    assert abs(v1.magnitude() - math.sqrt(14)) < 1e-10, "向量模长计算错误"
    assert abs(v1.dot(v2) - 32) < 1e-10, "向量点积计算错误"
    
    v3 = v1.cross(v2)
    expected_cross = Vector3D(-3, 6, -3)
    assert abs(v3.x - expected_cross.x) < 1e-10, "向量叉积计算错误"
    
    # 测试坐标转换
    test_lla = LatLonAlt(0.0, 0.0, 0.0)  # 赤道，经度0
    test_ecef = CoordinateSystem.lla_to_ecef(test_lla)
    
    # 应该在X轴上，距离等于地球半径
    expected_x = CoordinateSystem.EARTH_RADIUS
    assert abs(test_ecef.x - expected_x) < 1.0, "地理坐标转ECEF错误"
    assert abs(test_ecef.y) < 1.0, "地理坐标转ECEF错误"
    assert abs(test_ecef.z) < 1.0, "地理坐标转ECEF错误"
    
    # 反向转换测试
    back_lla = CoordinateSystem.ecef_to_lla(test_ecef)
    assert abs(back_lla.latitude - test_lla.latitude) < 1e-6, "ECEF转地理坐标错误"
    assert abs(back_lla.longitude - test_lla.longitude) < 1e-6, "ECEF转地理坐标错误"
    
    print("✓ 坐标系统转换测试通过")


def test_orbit_calculation():
    """测试轨道计算"""
    print("测试轨道计算...")
    
    from .orbit_calculator import OrbitCalculator, OrbitParameters
    
    calc = OrbitCalculator()
    
    # 创建圆形轨道
    orbit = OrbitParameters(
        semi_major_axis=6778137.0,  # 400km高度
        eccentricity=0.0,
        inclination=0.0,
        raan=0.0,
        argument_of_perigee=0.0,
        true_anomaly=0.0,
        epoch=2460000.0
    )
    
    # 测试轨道周期计算
    expected_period = 2 * math.pi * math.sqrt(orbit.semi_major_axis**3 / calc.EARTH_MU)
    assert abs(orbit.period - expected_period) < 1.0, "轨道周期计算错误"
    
    # 测试开普勒转笛卡尔
    state = calc.kepler_to_cartesian(orbit)
    
    # 圆形轨道，true_anomaly=0，应该在X轴上
    assert abs(state.position.x - orbit.semi_major_axis) < 1.0, "开普勒转笛卡尔位置错误"
    assert abs(state.position.y) < 1.0, "开普勒转笛卡尔位置错误"
    assert abs(state.position.z) < 1.0, "开普勒转笛卡尔位置错误"
    
    # 测试轨道传播
    future_time = orbit.epoch + 0.25 * orbit.period / 86400  # 1/4周期后
    future_state = calc.propagate_orbit(state, future_time)
    
    # 1/4周期后应该在Y轴附近
    assert abs(future_state.position.y) > orbit.semi_major_axis * 0.9, "轨道传播错误"
    
    # 测试笛卡尔转开普勒
    recovered_orbit = calc.cartesian_to_kepler(state)
    assert abs(recovered_orbit.semi_major_axis - orbit.semi_major_axis) < 1000, "笛卡尔转开普勒错误"
    
    print("✓ 轨道计算测试通过")


def test_path_planning():
    """测试路径规划"""
    print("测试路径规划...")
    
    from .coordinate_system import Vector3D
    from .path_planner import PathPlanner, PathNode
    from .orbit_calculator import SatelliteState
    
    planner = PathPlanner()
    
    # 创建测试节点
    nodes = []
    for i in range(5):
        pos = Vector3D(6778137 + i*500000, 0, 0)  # 线性排列
        node = PathNode(f"sat_{i}", pos, "satellite")
        planner.add_node(node)
        nodes.append(node)
    
    # 建立连接
    for i in range(4):
        planner.add_connection(f"sat_{i}", f"sat_{i+1}")
    
    # 测试路径规划
    path = planner.plan_path("sat_0", "sat_4", "astar")
    
    assert path.success, "路径规划应该成功"
    assert len(path.nodes) == 5, "路径节点数量错误"
    assert path.hop_count == 4, "路径跳数错误"
    
    # 测试不存在的路径
    no_path = planner.plan_path("sat_0", "nonexistent", "astar")
    assert not no_path.success, "不存在的路径应该失败"
    
    print("✓ 路径规划测试通过")


def test_link_management():
    """测试链路管理"""
    print("测试链路管理...")
    
    from .coordinate_system import Vector3D
    from .link_manager import LinkManager, LinkState
    
    manager = LinkManager()
    
    # 创建链路
    link_id = manager.create_link("node1", "node2", "satellite_to_satellite")
    assert link_id is not None, "链路创建失败"
    assert link_id in manager.links, "链路未正确存储"
    
    # 测试链路质量更新
    pos1 = Vector3D(6778137, 0, 0)
    pos2 = Vector3D(6778137 + 1000000, 0, 0)  # 1000km外
    
    manager.update_link_quality(link_id, pos1, pos2)
    
    link = manager.links[link_id]
    assert link.quality.signal_strength < 0, "信号强度应该为负值"
    assert link.quality.latency > 0, "延迟应该为正值"
    assert 0 <= link.quality.quality_score <= 1, "质量评分应该在0-1之间"
    
    # 测试不同距离的质量变化
    pos3 = Vector3D(6778137 + 2000000, 0, 0)  # 2000km外
    manager.update_link_quality(link_id, pos1, pos3)
    
    link_far = manager.links[link_id]
    assert link_far.quality.signal_strength < link.quality.signal_strength, "远距离信号强度应该更低"
    
    # 测试链路销毁
    success = manager.destroy_link(link_id)
    assert success, "链路销毁失败"
    assert link_id not in manager.links, "链路未正确删除"
    
    print("✓ 链路管理测试通过")


def test_path_optimization():
    """测试路径优化"""
    print("测试路径优化...")
    
    from .coordinate_system import Vector3D
    from .path_planner import PathNode, PathResult, PathSegment
    from .optimization import PathOptimizer, OptimizationCriteria
    
    optimizer = PathOptimizer()
    
    # 创建测试路径
    nodes = []
    for i in range(4):
        pos = Vector3D(6778137 + i*300000, 0, 0)
        node = PathNode(f"node_{i}", pos, "satellite")
        nodes.append(node)
    
    # 创建路径段
    segments = []
    total_distance = 0
    total_delay = 0
    total_cost = 0
    
    for i in range(3):
        node1, node2 = nodes[i], nodes[i+1]
        distance = node1.distance_to(node2)
        delay = distance / 299792458
        quality = 0.8
        bandwidth = 1e9
        
        segment = PathSegment(node1, node2, distance, delay, quality, bandwidth)
        segments.append(segment)
        total_distance += distance
        total_delay += delay
        total_cost += distance / 1000
    
    original_path = PathResult(nodes, segments, total_distance, total_delay, total_cost, True)
    
    # 测试优化
    criteria = OptimizationCriteria()
    result = optimizer.optimize_path(original_path, criteria)
    
    assert result.original_path == original_path, "原始路径不匹配"
    assert result.optimized_path.success, "优化路径应该成功"
    
    # 测试评分函数
    score = optimizer._calculate_path_score(original_path, criteria)
    assert score > 0, "路径评分应该为正值"
    
    print("✓ 路径优化测试通过")


def test_path_prediction():
    """测试路径预测"""
    print("测试路径预测...")
    
    from .coordinate_system import Vector3D, LatLonAlt
    from .orbit_calculator import SatelliteState, OrbitParameters, OrbitCalculator
    from .path_predictor import PathPredictor
    
    predictor = PathPredictor()
    orbit_calc = OrbitCalculator()
    
    # 创建测试卫星状态
    orbit = OrbitParameters(
        semi_major_axis=6778137.0,
        eccentricity=0.0,
        inclination=0.0,
        raan=0.0,
        argument_of_perigee=0.0,
        true_anomaly=0.0,
        epoch=2460000.0
    )
    
    sat_state = orbit_calc.kepler_to_cartesian(orbit)
    
    # 测试位置预测
    future_time = orbit.epoch + 0.1  # 0.1天后
    predicted_states = predictor.predict_satellite_positions([sat_state], future_time)
    
    assert len(predicted_states) == 1, "预测状态数量错误"
    assert predicted_states[0].time == future_time, "预测时间错误"
    
    # 位置应该发生变化
    original_pos = sat_state.position
    predicted_pos = predicted_states[0].position
    distance_moved = original_pos.distance_to(predicted_pos)
    assert distance_moved > 1000, "卫星位置应该发生显著变化"
    
    # 测试链路质量预测
    quality = predictor.predict_link_quality(1000000, "satellite_to_satellite")
    assert quality.signal_strength < 0, "信号强度应该为负值"
    assert quality.bandwidth > 0, "带宽应该为正值"
    
    # 测试拓扑变化预测
    ground_stations = [LatLonAlt(0, 0, 0)]
    changes = predictor.predict_topology_changes(
        [sat_state], ground_stations, future_time
    )
    
    assert isinstance(changes, list), "拓扑变化应该返回列表"
    
    print("✓ 路径预测测试通过")


def test_integration():
    """集成测试"""
    print("测试模块集成...")
    
    from .coordinate_system import Vector3D, LatLonAlt
    from .orbit_calculator import OrbitCalculator, OrbitParameters
    from .path_planner import PathPlanner
    from .link_manager import LinkManager
    from .optimization import PathOptimizer, OptimizationCriteria
    from .path_predictor import PathPredictor
    
    # 创建所有模块实例
    orbit_calc = OrbitCalculator()
    planner = PathPlanner()
    link_mgr = LinkManager()
    optimizer = PathOptimizer()
    predictor = PathPredictor()
    
    # 创建简单的卫星系统
    orbit = OrbitParameters(
        semi_major_axis=6778137.0,
        eccentricity=0.0,
        inclination=0.0,
        raan=0.0,
        argument_of_perigee=0.0,
        true_anomaly=0.0,
        epoch=2460000.0
    )
    
    # 创建两颗卫星
    sat1_state = orbit_calc.kepler_to_cartesian(orbit)
    
    orbit2 = OrbitParameters(
        semi_major_axis=6778137.0,
        eccentricity=0.0,
        inclination=0.0,
        raan=0.0,
        argument_of_perigee=0.0,
        true_anomaly=math.radians(90),  # 90度位相差
        epoch=2460000.0
    )
    sat2_state = orbit_calc.kepler_to_cartesian(orbit2)
    
    satellite_states = [sat1_state, sat2_state]
    ground_stations = [LatLonAlt(0, 0, 0)]
    
    # 建立网络拓扑
    planner.update_topology(satellite_states, ground_stations)
    
    # 创建链路
    distance = sat1_state.position.distance_to(sat2_state.position)
    if distance <= 2000000:  # 如果在连接范围内
        link_id = link_mgr.create_link("sat_0", "sat_1", "satellite_to_satellite")
        if link_id:
            link_mgr.update_link_quality(link_id, sat1_state.position, sat2_state.position)
    
    # 尝试路径规划
    if "sat_0" in planner.nodes and "gs_0" in planner.nodes:
        path = planner.plan_path("sat_0", "gs_0", "astar")
        
        if path.success:
            # 路径优化
            criteria = OptimizationCriteria()
            opt_result = optimizer.optimize_path(path, criteria)
            
            # 位置预测
            future_time = 2460000.0 + 1/24  # 1小时后
            predicted_sats = predictor.predict_satellite_positions(satellite_states, future_time)
            
            assert len(predicted_sats) == 2, "预测卫星数量错误"
    
    print("✓ 模块集成测试通过")


def run_performance_test():
    """性能测试"""
    print("执行性能测试...")
    
    from .coordinate_system import Vector3D, CoordinateSystem, LatLonAlt
    from .orbit_calculator import OrbitCalculator, OrbitParameters
    
    # 测试大量坐标转换的性能
    start_time = time.time()
    
    for i in range(1000):
        lat = (i % 180) - 90
        lon = (i % 360) - 180
        lla = LatLonAlt(lat, lon, 0)
        ecef = CoordinateSystem.lla_to_ecef(lla)
        back_lla = CoordinateSystem.ecef_to_lla(ecef)
    
    coord_time = time.time() - start_time
    print(f"1000次坐标转换耗时: {coord_time:.3f}秒")
    
    # 测试轨道计算性能
    start_time = time.time()
    calc = OrbitCalculator()
    
    orbit = OrbitParameters(
        semi_major_axis=6778137.0,
        eccentricity=0.001,
        inclination=math.radians(98.7),
        raan=0.0,
        argument_of_perigee=0.0,
        true_anomaly=0.0,
        epoch=2460000.0
    )
    
    for i in range(100):
        state = calc.kepler_to_cartesian(orbit)
        future_time = orbit.epoch + i * 0.01
        future_state = calc.propagate_orbit(state, future_time)
    
    orbit_time = time.time() - start_time
    print(f"100次轨道传播耗时: {orbit_time:.3f}秒")
    
    print("✓ 性能测试完成")


def run_all_tests():
    """运行所有测试"""
    print("SDT卫星数字孪生系统 - 路径控制模块测试")
    print("=" * 50)
    
    tests = [
        test_coordinate_system,
        test_orbit_calculation,
        test_path_planning,
        test_link_management,
        test_path_optimization,
        test_path_prediction,
        test_integration,
        run_performance_test
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"✗ {test_func.__name__} 失败: {e}")
            import traceback
            traceback.print_exc()
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过!")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    return failed == 0


if __name__ == "__main__":
    run_all_tests() 