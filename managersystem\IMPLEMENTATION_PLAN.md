# SDT管理系统实现计划

## 1. 技术栈详细说明

### 1.1 后端技术栈
- **Flask 2.3+**: 轻量级Web框架
- **Flask-JWT-Extended**: JWT认证
- **Flask-CORS**: 跨域支持
- **Redis-py**: Redis客户端
- **Werkzeug**: 文件上传处理
- **APScheduler**: 定时任务
- **Marshmallow**: 数据序列化

### 1.2 前端技术栈
- **React 18**: 前端框架
- **Ant Design 5**: UI组件库
- **React Router 6**: 路由管理
- **Axios**: HTTP客户端
- **Redux Toolkit**: 状态管理
- **React Query**: 数据获取和缓存
- **Dayjs**: 日期处理

### 1.3 开发工具
- **Webpack**: 前端构建工具
- **ESLint + Prettier**: 代码规范
- **Jest**: 单元测试
- **Docker**: 容器化部署

## 2. 项目初始化步骤

### 2.1 后端项目结构创建
```bash
managersystem/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── issue.py
│   │   │   ├── document.py
│   │   │   └── product.py
│   │   ├── api/
│   │   │   ├── __init__.py
│   │   │   ├── auth.py
│   │   │   ├── users.py
│   │   │   ├── issues.py
│   │   │   ├── documents.py
│   │   │   └── products.py
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── auth_service.py
│   │   │   ├── user_service.py
│   │   │   ├── issue_service.py
│   │   │   ├── document_service.py
│   │   │   └── product_service.py
│   │   ├── utils/
│   │   │   ├── __init__.py
│   │   │   ├── redis_client.py
│   │   │   ├── file_handler.py
│   │   │   ├── validators.py
│   │   │   └── decorators.py
│   │   └── auth/
│   │       ├── __init__.py
│   │       └── jwt_handler.py
│   ├── config.py
│   ├── requirements.txt
│   └── run.py
```

### 2.2 前端项目结构创建
```bash
├── frontend/
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/
│   │   │   ├── common/
│   │   │   ├── layout/
│   │   │   └── forms/
│   │   ├── pages/
│   │   │   ├── Login/
│   │   │   ├── Dashboard/
│   │   │   ├── Issues/
│   │   │   ├── Documents/
│   │   │   ├── Products/
│   │   │   └── Users/
│   │   ├── services/
│   │   │   ├── api.js
│   │   │   ├── auth.js
│   │   │   └── storage.js
│   │   ├── store/
│   │   │   ├── index.js
│   │   │   ├── authSlice.js
│   │   │   └── appSlice.js
│   │   ├── utils/
│   │   │   ├── constants.js
│   │   │   ├── helpers.js
│   │   │   └── validators.js
│   │   ├── App.js
│   │   └── index.js
│   ├── package.json
│   └── webpack.config.js
```

## 3. 核心功能实现要点

### 3.1 Redis数据存储策略

#### 数据键命名规范
```python
# 用户相关
USER_KEY = "user:{user_id}"
USER_INDEX = "users:index"
USER_SESSION = "session:{token}"

# 问题相关
ISSUE_KEY = "issue:{issue_id}"
ISSUE_INDEX = "issues:index"
ISSUE_COMMENTS = "issue:{issue_id}:comments"

# 文档相关
DOCUMENT_KEY = "document:{doc_id}"
DOCUMENT_INDEX = "documents:index"
DOCUMENT_CATEGORIES = "document:categories"

# 产品相关
PRODUCT_KEY = "product:{product_id}"
PRODUCT_INDEX = "products:index"
PRODUCT_FILES = "product:{product_id}:files"
```

#### 数据过期策略
```python
# 会话过期时间
SESSION_EXPIRE = 24 * 60 * 60  # 24小时

# 缓存过期时间
CACHE_EXPIRE = 60 * 60  # 1小时

# 临时数据过期时间
TEMP_EXPIRE = 10 * 60  # 10分钟
```

### 3.2 文件上传处理

#### 文件存储结构
```
storage/
├── documents/
│   ├── 2024/
│   │   ├── 01/
│   │   └── 02/
├── attachments/
│   ├── issues/
│   └── comments/
├── products/
│   ├── releases/
│   └── patches/
└── temp/
    └── uploads/
```

#### 文件处理流程
1. 文件上传验证（大小、类型、权限）
2. 生成唯一文件名（UUID + 原始扩展名）
3. 存储到对应目录
4. 更新Redis中的文件信息
5. 返回文件访问URL

### 3.3 权限控制实现

#### 装饰器权限检查
```python
from functools import wraps
from flask_jwt_extended import get_jwt_identity

def require_permission(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            current_user = get_jwt_identity()
            if not has_permission(current_user, permission):
                return {'error': 'Permission denied'}, 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# 使用示例
@require_permission('issue:create')
def create_issue():
    pass
```

#### 前端权限控制
```javascript
// 权限检查Hook
const usePermission = (permission) => {
  const { user } = useSelector(state => state.auth);
  return user?.permissions?.includes(permission) || false;
};

// 权限组件
const PermissionGuard = ({ permission, children, fallback = null }) => {
  const hasPermission = usePermission(permission);
  return hasPermission ? children : fallback;
};
```

## 4. API接口实现规范

### 4.1 统一响应格式
```python
# 成功响应
{
    "success": true,
    "data": {...},
    "message": "操作成功"
}

# 错误响应
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "参数验证失败",
        "details": {...}
    }
}

# 分页响应
{
    "success": true,
    "data": {
        "items": [...],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 100,
            "pages": 5
        }
    }
}
```

### 4.2 参数验证
```python
from marshmallow import Schema, fields, validate

class IssueCreateSchema(Schema):
    title = fields.Str(required=True, validate=validate.Length(min=1, max=200))
    description = fields.Str(required=True)
    type = fields.Str(required=True, validate=validate.OneOf(['bug', 'feature', 'task']))
    priority = fields.Str(required=True, validate=validate.OneOf(['low', 'medium', 'high', 'critical']))
    assignee = fields.Str(required=False)
    tags = fields.List(fields.Str(), required=False)
```

### 4.3 错误处理
```python
from flask import jsonify

class APIError(Exception):
    def __init__(self, message, code=400, details=None):
        self.message = message
        self.code = code
        self.details = details

@app.errorhandler(APIError)
def handle_api_error(error):
    return jsonify({
        'success': False,
        'error': {
            'code': error.code,
            'message': error.message,
            'details': error.details
        }
    }), error.code
```

## 5. 前端开发规范

### 5.1 组件开发规范
```javascript
// 函数组件模板
import React, { useState, useEffect } from 'react';
import { Card, Button, message } from 'antd';
import PropTypes from 'prop-types';

const ComponentName = ({ prop1, prop2, onAction }) => {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // 组件初始化逻辑
  }, []);

  const handleAction = async () => {
    try {
      setLoading(true);
      await onAction();
      message.success('操作成功');
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <Button loading={loading} onClick={handleAction}>
        执行操作
      </Button>
    </Card>
  );
};

ComponentName.propTypes = {
  prop1: PropTypes.string.isRequired,
  prop2: PropTypes.number,
  onAction: PropTypes.func.isRequired,
};

ComponentName.defaultProps = {
  prop2: 0,
};

export default ComponentName;
```

### 5.2 状态管理规范
```javascript
// Redux Slice示例
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { issueAPI } from '../services/api';

export const fetchIssues = createAsyncThunk(
  'issues/fetchIssues',
  async (params, { rejectWithValue }) => {
    try {
      const response = await issueAPI.getList(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

const issuesSlice = createSlice({
  name: 'issues',
  initialState: {
    list: [],
    loading: false,
    error: null,
    pagination: {
      page: 1,
      size: 20,
      total: 0,
    },
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchIssues.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchIssues.fulfilled, (state, action) => {
        state.loading = false;
        state.list = action.payload.items;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchIssues.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = issuesSlice.actions;
export default issuesSlice.reducer;
```

## 6. 测试策略

### 6.1 后端测试
```python
import pytest
from app import create_app
from app.utils.redis_client import redis_client

@pytest.fixture
def app():
    app = create_app('testing')
    return app

@pytest.fixture
def client(app):
    return app.test_client()

def test_create_issue(client):
    # 测试创建问题
    response = client.post('/api/issues', json={
        'title': '测试问题',
        'description': '这是一个测试问题',
        'type': 'bug',
        'priority': 'medium'
    })
    assert response.status_code == 201
    assert response.json['success'] == True
```

### 6.2 前端测试
```javascript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../store';
import IssueList from '../pages/Issues/IssueList';

test('renders issue list', async () => {
  render(
    <Provider store={store}>
      <IssueList />
    </Provider>
  );

  await waitFor(() => {
    expect(screen.getByText('问题列表')).toBeInTheDocument();
  });
});
```

## 7. 部署配置

### 7.1 Docker配置
```dockerfile
# backend/Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000
CMD ["python", "run.py"]
```

```dockerfile
# frontend/Dockerfile
FROM node:16-alpine

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=0 /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
```

### 7.2 Docker Compose
```yaml
version: '3.8'
services:
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  backend:
    build: ./backend
    ports:
      - "5000:5000"
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  redis_data:
```

这个实现计划提供了详细的技术实现指导，您觉得这个计划如何？需要我开始创建具体的代码文件吗？
