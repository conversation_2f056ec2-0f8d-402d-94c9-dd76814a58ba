import pytest
import logging
from logging.handlers import MemoryHandler
from utils.logger import setup_logger, get_logger

@pytest.fixture(autouse=True)
def cleanup_loggers():
    """
    Fixture to automatically clean up loggers and their handlers after each test.
    This prevents handlers from accumulating across tests.
    """
    yield
    # Teardown:
    loggers = [logging.getLogger(name) for name in logging.root.manager.loggerDict]
    for logger in loggers:
        if "pytest" not in logger.name: # Don't mess with pytest's own loggers
            logger.handlers.clear()

def test_setup_logger_creates_logger():
    """Test that a logger instance is created with the correct name."""
    logger = setup_logger("test_logger_1")
    assert isinstance(logger, logging.Logger)
    assert logger.name == "test_logger_1"

def test_logger_captures_message():
    """Test that a configured logger correctly captures a log message."""
    logger = setup_logger("test_logger_2", level=logging.DEBUG)
    
    # Use a MemoryHandler to capture log output without writing to console/file
    mem_handler = MemoryHandler(capacity=10)
    logger.addHandler(mem_handler)
    
    # Log a message
    test_message = "This is a debug message."
    logger.debug(test_message)
    
    # Check if the message was captured
    assert len(mem_handler.buffer) == 1
    log_record = mem_handler.buffer[0]
    assert log_record.levelname == "DEBUG"
    assert log_record.getMessage() == test_message
    
    # Clear the handler to avoid interfering with other tests
    logger.removeHandler(mem_handler)

def test_get_logger_retrieves_existing():
    """Test that get_logger retrieves the same instance."""
    logger1 = setup_logger("my_app_logger")
    logger2 = get_logger("my_app_logger")
    
    assert logger1 is logger2 