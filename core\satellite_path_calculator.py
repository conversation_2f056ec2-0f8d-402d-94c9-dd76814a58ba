#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卫星路径计算控制器
支持多种路径算法: Dijkstra, BFS, Floyd-Warshall
"""

import heapq
import threading
import time
from collections import defaultdict, deque
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from datetime import datetime
import numpy as np

from utils.logger import setup_logger


@dataclass
class RouteRequest:
    """路由请求数据结构"""
    request_id: str
    source: int
    destination: int
    timestamp: datetime
    priority: int = 1
    constraints: Dict = None


@dataclass
class RouteResult:
    """路由结果数据结构"""
    request_id: str
    path: List[int]
    cost: float
    algorithm: str
    computation_time: float
    timestamp: datetime


class NetworkGraph:
    """网络图表示"""
    
    def __init__(self):
        self.nodes: Set[int] = set()
        self.edges: Dict[Tuple[int, int], float] = {}
        self.adjacency_list: Dict[int, List[Tuple[int, float]]] = defaultdict(list)
        
    def add_node(self, node_id: int):
        """添加节点"""
        self.nodes.add(node_id)
        
    def add_edge(self, source: int, destination: int, weight: float):
        """添加边"""
        self.nodes.add(source)
        self.nodes.add(destination)
        self.edges[(source, destination)] = weight
        self.adjacency_list[source].append((destination, weight))
        
        # 无向图，添加反向边
        self.edges[(destination, source)] = weight
        self.adjacency_list[destination].append((source, weight))
        
    def remove_edge(self, source: int, destination: int):
        """删除边"""
        if (source, destination) in self.edges:
            del self.edges[(source, destination)]
            self.adjacency_list[source] = [
                (dest, weight) for dest, weight in self.adjacency_list[source]
                if dest != destination
            ]
        
        if (destination, source) in self.edges:
            del self.edges[(destination, source)]
            self.adjacency_list[destination] = [
                (dest, weight) for dest, weight in self.adjacency_list[destination]
                if dest != source
            ]
    
    def get_neighbors(self, node: int) -> List[Tuple[int, float]]:
        """获取邻居节点"""
        return self.adjacency_list.get(node, [])
    
    def get_edge_weight(self, source: int, destination: int) -> Optional[float]:
        """获取边权重"""
        return self.edges.get((source, destination))


class DijkstraCalculator:
    """Dijkstra算法实现"""
    
    def __init__(self):
        self.logger = setup_logger("DijkstraCalculator")
    
    def calculate_shortest_path(self, graph: NetworkGraph, source: int, destination: int) -> Tuple[List[int], float]:
        """
        计算最短路径
        
        Args:
            graph: 网络图
            source: 源节点
            destination: 目标节点
            
        Returns:
            path, cost: 路径和成本
        """
        if source not in graph.nodes or destination not in graph.nodes:
            return [], float('inf')
        
        # 初始化距离和前驱节点
        distances = {node: float('inf') for node in graph.nodes}
        predecessors = {node: None for node in graph.nodes}
        distances[source] = 0
        
        # 优先队列: (距离, 节点)
        pq = [(0, source)]
        visited = set()
        
        while pq:
            current_distance, current_node = heapq.heappop(pq)
            
            if current_node in visited:
                continue
                
            visited.add(current_node)
            
            # 到达目标节点
            if current_node == destination:
                break
            
            # 检查所有邻居
            for neighbor, weight in graph.get_neighbors(current_node):
                if neighbor not in visited:
                    new_distance = current_distance + weight
                    
                    if new_distance < distances[neighbor]:
                        distances[neighbor] = new_distance
                        predecessors[neighbor] = current_node
                        heapq.heappush(pq, (new_distance, neighbor))
        
        # 重构路径
        path = self._reconstruct_path(predecessors, source, destination)
        cost = distances[destination]
        
        return path, cost
    
    def _reconstruct_path(self, predecessors: Dict, source: int, destination: int) -> List[int]:
        """重构路径"""
        path = []
        current = destination
        
        while current is not None:
            path.append(current)
            current = predecessors[current]
        
        path.reverse()
        
        # 检查路径是否有效
        if path and path[0] == source:
            return path
        else:
            return []


class BFSCalculator:
    """BFS算法实现（用于无权图或同权重边）"""
    
    def __init__(self):
        self.logger = setup_logger("BFSCalculator")
    
    def calculate_shortest_path(self, graph: NetworkGraph, source: int, destination: int) -> Tuple[List[int], float]:
        """
        使用BFS计算最短路径（按跳数）
        
        Args:
            graph: 网络图
            source: 源节点
            destination: 目标节点
            
        Returns:
            path, hops: 路径和跳数
        """
        if source not in graph.nodes or destination not in graph.nodes:
            return [], float('inf')
        
        if source == destination:
            return [source], 0
        
        queue = deque([(source, [source])])
        visited = {source}
        
        while queue:
            current_node, path = queue.popleft()
            
            # 检查所有邻居
            for neighbor, _ in graph.get_neighbors(current_node):
                if neighbor not in visited:
                    new_path = path + [neighbor]
                    
                    if neighbor == destination:
                        return new_path, len(new_path) - 1
                    
                    visited.add(neighbor)
                    queue.append((neighbor, new_path))
        
        return [], float('inf')


class FloydWarshallCalculator:
    """Floyd-Warshall算法实现"""
    
    def __init__(self):
        self.logger = setup_logger("FloydWarshallCalculator")
        self.distance_matrix = None
        self.next_hop_matrix = None
        self.nodes_list = None
        
    def precompute_all_pairs_shortest_paths(self, graph: NetworkGraph):
        """预计算所有节点对的最短路径"""
        nodes = list(graph.nodes)
        n = len(nodes)
        
        if n == 0:
            return
        
        self.nodes_list = nodes
        node_to_index = {node: i for i, node in enumerate(nodes)}
        
        # 初始化距离矩阵
        self.distance_matrix = np.full((n, n), np.inf)
        self.next_hop_matrix = np.full((n, n), -1, dtype=int)
        
        # 设置对角线为0
        for i in range(n):
            self.distance_matrix[i, i] = 0
        
        # 设置直接相邻的边
        for (source, dest), weight in graph.edges.items():
            i = node_to_index[source]
            j = node_to_index[dest]
            self.distance_matrix[i, j] = weight
            self.next_hop_matrix[i, j] = j
        
        # Floyd-Warshall算法核心
        for k in range(n):
            for i in range(n):
                for j in range(n):
                    if (self.distance_matrix[i, k] + self.distance_matrix[k, j] < 
                        self.distance_matrix[i, j]):
                        self.distance_matrix[i, j] = (self.distance_matrix[i, k] + 
                                                    self.distance_matrix[k, j])
                        self.next_hop_matrix[i, j] = self.next_hop_matrix[i, k]
    
    def calculate_shortest_path(self, source: int, destination: int) -> Tuple[List[int], float]:
        """获取预计算的最短路径"""
        if (self.distance_matrix is None or self.nodes_list is None or
            source not in self.nodes_list or destination not in self.nodes_list):
            return [], float('inf')
        
        node_to_index = {node: i for i, node in enumerate(self.nodes_list)}
        source_idx = node_to_index[source]
        dest_idx = node_to_index[destination]
        
        cost = self.distance_matrix[source_idx, dest_idx]
        
        if cost == np.inf:
            return [], float('inf')
        
        # 重构路径
        path = []
        current = source_idx
        
        while current != dest_idx:
            path.append(self.nodes_list[current])
            current = self.next_hop_matrix[current, dest_idx]
            
            if current == -1:  # 无路径
                return [], float('inf')
        
        path.append(self.nodes_list[dest_idx])
        
        return path, cost


class PathCache:
    """路径缓存管理器"""
    
    def __init__(self, max_size: int = 10000, ttl: float = 300.0):
        self.max_size = max_size
        self.ttl = ttl  # 生存时间（秒）
        self.cache: Dict[Tuple[int, int], Tuple[RouteResult, float]] = {}
        self.access_times: Dict[Tuple[int, int], float] = {}
        self.lock = threading.RLock()
        
    def get(self, source: int, destination: int) -> Optional[RouteResult]:
        """获取缓存的路径"""
        with self.lock:
            key = (source, destination)
            current_time = time.time()
            
            if key in self.cache:
                result, timestamp = self.cache[key]
                
                # 检查是否过期
                if current_time - timestamp <= self.ttl:
                    self.access_times[key] = current_time
                    return result
                else:
                    # 删除过期条目
                    del self.cache[key]
                    del self.access_times[key]
            
            return None
    
    def put(self, source: int, destination: int, result: RouteResult):
        """存储路径到缓存"""
        with self.lock:
            key = (source, destination)
            current_time = time.time()
            
            # 如果缓存已满，删除最久未访问的条目
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            self.cache[key] = (result, current_time)
            self.access_times[key] = current_time
    
    def _evict_lru(self):
        """淘汰最久未使用的条目"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()


class SatellitePathCalculator:
    """卫星路径计算控制器主类"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("SatellitePathCalculator")
        
        # 算法实例
        self.dijkstra = DijkstraCalculator()
        self.bfs = BFSCalculator()
        self.floyd = FloydWarshallCalculator()
        
        # 网络图
        self.network_graph = NetworkGraph()
        
        # 缓存管理
        self.path_cache = PathCache()
        
        # 路由表和映射
        self.ip_to_satellite: Dict[str, int] = {}
        self.satellite_to_domain: Dict[int, int] = {}
        
        # 统计信息
        self.request_count = 0
        self.cache_hits = 0
        self.computation_times = []
        
        # 线程控制
        self.processing_thread = None
        self.running = False
        self.request_queue = deque()
        self.request_lock = threading.Lock()
        
        self.logger.info("路径计算控制器初始化完成")
    
    def update_network_topology(self, topology: Dict):
        """更新网络拓扑"""
        try:
            # 清空现有图
            self.network_graph = NetworkGraph()
            
            # 添加卫星节点
            satellites = topology.get('satellites', [])
            for sat in satellites:
                self.network_graph.add_node(sat['id'])
            
            # 添加星间链路
            inter_sat_links = topology.get('inter_satellite_links', [])
            for source, dest in inter_sat_links:
                # 计算链路权重（可以基于距离、延迟等）
                weight = self._calculate_link_weight(source, dest, satellites)
                self.network_graph.add_edge(source, dest, weight)
            
            # 重新计算Floyd-Warshall表（如果需要）
            self.floyd.precompute_all_pairs_shortest_paths(self.network_graph)
            
            # 清空缓存
            self.path_cache.clear()
            
            self.logger.info(f"网络拓扑已更新: {len(satellites)} 个节点, {len(inter_sat_links)} 条链路")
            
        except Exception as e:
            self.logger.error(f"更新网络拓扑失败: {e}")
    
    def calculate_path(self, source: int, destination: int, algorithm: str = "dijkstra") -> RouteResult:
        """
        计算路径
        
        Args:
            source: 源节点
            destination: 目标节点
            algorithm: 算法选择 ("dijkstra", "bfs", "floyd")
        
        Returns:
            RouteResult: 路由结果
        """
        start_time = time.time()
        request_id = f"req_{int(start_time * 1000)}_{source}_{destination}"
        
        try:
            # 检查缓存
            cached_result = self.path_cache.get(source, destination)
            if cached_result:
                self.cache_hits += 1
                cached_result.request_id = request_id
                cached_result.timestamp = datetime.now()
                return cached_result
            
            # 选择算法计算路径
            if algorithm.lower() == "dijkstra":
                path, cost = self.dijkstra.calculate_shortest_path(
                    self.network_graph, source, destination
                )
            elif algorithm.lower() == "bfs":
                path, cost = self.bfs.calculate_shortest_path(
                    self.network_graph, source, destination
                )
            elif algorithm.lower() == "floyd":
                path, cost = self.floyd.calculate_shortest_path(source, destination)
            else:
                # 默认使用Dijkstra
                path, cost = self.dijkstra.calculate_shortest_path(
                    self.network_graph, source, destination
                )
                algorithm = "dijkstra"
            
            computation_time = time.time() - start_time
            
            # 创建结果
            result = RouteResult(
                request_id=request_id,
                path=path,
                cost=cost,
                algorithm=algorithm,
                computation_time=computation_time,
                timestamp=datetime.now()
            )
            
            # 存储到缓存
            self.path_cache.put(source, destination, result)
            
            # 更新统计
            self.request_count += 1
            self.computation_times.append(computation_time)
            
            return result
            
        except Exception as e:
            self.logger.error(f"路径计算失败: {e}")
            return RouteResult(
                request_id=request_id,
                path=[],
                cost=float('inf'),
                algorithm=algorithm,
                computation_time=time.time() - start_time,
                timestamp=datetime.now()
            )
    
    def calculate_multiple_paths(self, requests: List[RouteRequest]) -> List[RouteResult]:
        """批量计算路径"""
        results = []
        
        for request in requests:
            result = self.calculate_path(
                request.source, 
                request.destination,
                "dijkstra"  # 默认算法
            )
            result.request_id = request.request_id
            results.append(result)
        
        return results
    
    def _calculate_link_weight(self, source: int, dest: int, satellites: List[Dict]) -> float:
        """计算链路权重"""
        # 找到对应的卫星
        source_sat = next((s for s in satellites if s['id'] == source), None)
        dest_sat = next((s for s in satellites if s['id'] == dest), None)
        
        if not source_sat or not dest_sat:
            return float('inf')
        
        # 计算距离作为权重
        source_pos = source_sat['position']
        dest_pos = dest_sat['position']
        
        distance = ((source_pos[0] - dest_pos[0])**2 + 
                   (source_pos[1] - dest_pos[1])**2 + 
                   (source_pos[2] - dest_pos[2])**2)**0.5
        
        # 可以添加其他因素如延迟、带宽等
        delay = distance / 299792.458  # 光速延迟 (ms)
        
        return delay
    
    def update_satellite_mapping(self, ip: str, satellite_id: int, domain: int = 0):
        """更新IP到卫星的映射"""
        self.ip_to_satellite[ip] = satellite_id
        self.satellite_to_domain[satellite_id] = domain
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        avg_computation_time = (
            sum(self.computation_times) / len(self.computation_times)
            if self.computation_times else 0
        )
        
        cache_hit_rate = (
            self.cache_hits / self.request_count
            if self.request_count > 0 else 0
        )
        
        return {
            'total_requests': self.request_count,
            'cache_hits': self.cache_hits,
            'cache_hit_rate': cache_hit_rate,
            'avg_computation_time': avg_computation_time,
            'network_nodes': len(self.network_graph.nodes),
            'network_edges': len(self.network_graph.edges)
        }
    
    def export_routing_table(self, filename: str = None) -> str:
        """导出路由表"""
        if filename is None:
            filename = f"routing_table_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("# SDT路由表\n")
                f.write(f"# 生成时间: {datetime.now()}\n")
                f.write(f"# 节点数: {len(self.network_graph.nodes)}\n")
                f.write(f"# 链路数: {len(self.network_graph.edges)}\n\n")
                
                # 写入节点信息
                f.write("## 节点列表\n")
                for node in sorted(self.network_graph.nodes):
                    domain = self.satellite_to_domain.get(node, 0)
                    f.write(f"Node: {node}, Domain: {domain}\n")
                
                # 写入链路信息
                f.write("\n## 链路列表\n")
                for (source, dest), weight in sorted(self.network_graph.edges.items()):
                    f.write(f"Link: {source} -> {dest}, Weight: {weight:.3f}\n")
            
            self.logger.info(f"路由表已导出到: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"导出路由表失败: {e}")
            return "" 