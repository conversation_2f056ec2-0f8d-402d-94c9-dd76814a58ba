import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Navigate } from 'react-router-dom';
import { 
  Form, 
  Input, 
  Button, 
  Card, 
  Typography, 
  Alert, 
  Space,
  Divider 
} from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';

import { login, clearError } from '../../store/authSlice';
import './Login.css';

const { Title, Text } = Typography;

const Login = () => {
  const dispatch = useDispatch();
  const { loading, error, isAuthenticated } = useSelector(state => state.auth);
  const [form] = Form.useForm();

  // 如果已经登录，重定向到仪表板
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  const handleSubmit = async (values) => {
    dispatch(clearError());
    dispatch(login(values));
  };

  const handleFormChange = () => {
    if (error) {
      dispatch(clearError());
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <Title level={2} className="login-title">
              SDT管理系统
            </Title>
            <Text type="secondary" className="login-subtitle">
              卫星数字孪生系统管理平台
            </Text>
          </div>

          <Divider />

          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            onChange={handleFormChange}
            size="large"
            autoComplete="off"
          >
            {error && (
              <Form.Item>
                <Alert
                  message={error.message || '登录失败'}
                  type="error"
                  showIcon
                  closable
                  onClose={() => dispatch(clearError())}
                />
              </Form.Item>
            )}

            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                icon={<LoginOutlined />}
              >
                登录
              </Button>
            </Form.Item>
          </Form>

          <div className="login-footer">
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Divider plain>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  默认账户信息
                </Text>
              </Divider>
              
              <div className="default-accounts">
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  管理员: admin / admin123
                </Text>
              </div>
              
              <div className="login-info">
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  © 2024 SDT管理系统 - 仅供学习和研究使用
                </Text>
              </div>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;
