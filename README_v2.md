# SDT卫星数字孪生系统 v2.0

## 项目概述

SDT（Satellite Digital Twin）卫星数字孪生系统是一个高保真的卫星轨道仿真和网络拓扑分析平台。本项目按照设计文档要求进行了完全重构，实现了三个核心模块的分离和优化。

## 系统架构

### 核心模块

1. **轨道计算模块 (Module I)**
   - TLE数据解析和验证
   - SGP4/SDP4轨道传播算法
   - 高精度时间处理

2. **数字孪生模型构建模块 (Module II)**
   - 高保真物理引擎
   - 数值积分（RK4算法）
   - 多种摄动力模型（J2、大气阻力、太阳辐射压）

3. **卫星拓扑模拟模块 (Module III)**
   - 视线算法和链路分析
   - 网络拓扑计算
   - 路由算法（Dijkstra、BFS等）

### 技术栈

**后端:**
- Python 3.8+
- NumPy (数值计算)
- Flask (Web API)
- Cython (性能优化)
- pytest (测试框架)

**前端:**
- HTML5
- CSS3
- JavaScript (ES6+)
- Canvas API (图表绘制)

**服务器:**
- Node.js (静态文件服务和WebSocket)
- Express.js
- WebSocket支持

## 项目结构

```
SDTcode1/
├── modules/                          # 核心模块
│   ├── orbit_calculation/           # 轨道计算模块
│   │   ├── __init__.py
│   │   ├── tle.py                   # TLE数据类
│   │   ├── tle_parser.py            # TLE解析器
│   │   └── sgp4_propagator.py       # SGP4传播器
│   ├── digital_twin/                # 数字孪生模块
│   │   ├── __init__.py
│   │   ├── state_vector.py          # 状态向量
│   │   ├── satellite.py             # 卫星属性
│   │   ├── numerical_propagator.py  # 数值传播器
│   │   └── perturbations.py         # 摄动力模型
│   └── topology_simulation/         # 拓扑仿真模块
│       ├── __init__.py
│       ├── topology_simulator.py    # 拓扑模拟器
│       ├── line_of_sight.py         # 视线算法
│       └── routing.py               # 路由算法
├── server/                          # 服务器
│   ├── flask_app.py                 # Flask API服务器
│   ├── server.js                    # Node.js服务器
│   └── package.json                 # Node.js依赖
├── cython_extensions/               # Cython优化
│   ├── __init__.py
│   ├── setup.py                     # 构建脚本
│   └── rk4_integrator.pyx          # RK4积分器优化
├── frontend/                        # 前端界面
│   ├── index_v2.html               # 主页面
│   ├── styles_v2.css               # 样式文件
│   └── app_v2.js                   # JavaScript应用
├── tests/                          # 测试文件
│   ├── __init__.py
│   ├── test_orbit_calculation.py   # 轨道计算测试
│   └── test_digital_twin.py        # 数字孪生测试
├── requirements.txt                # Python依赖
└── README_v2.md                   # 项目说明
```

## 安装和运行

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd SDTcode1

# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装Python依赖
pip install -r requirements.txt
```

### 2. 编译Cython扩展（可选，用于性能优化）

```bash
cd cython_extensions
python setup.py build_ext --inplace
cd ..
```

### 3. 启动Flask API服务器

```bash
cd server
python flask_app.py
```

Flask服务器将在 http://localhost:5000 启动

### 4. 启动Node.js服务器

```bash
cd server
npm install
npm start
```

Node.js服务器将在 http://localhost:3000 启动

### 5. 访问系统

打开浏览器访问 http://localhost:3000 即可使用系统。

## API接口

### 轨道计算模块

- `POST /api/v1/tle/parse` - 解析TLE数据
- `POST /api/v1/propagate/sgp4` - SGP4轨道传播

### 数字孪生模块

- `POST /api/v1/simulate/orbit` - 高精度轨道仿真

### 拓扑仿真模块

- `POST /api/v1/topology/analyze` - 网络拓扑分析

### 系统状态

- `GET /api/v1/health` - 健康检查

## 测试

运行单元测试：

```bash
# 运行所有测试
pytest

# 运行特定模块测试
pytest tests/test_orbit_calculation.py
pytest tests/test_digital_twin.py

# 生成覆盖率报告
pytest --cov=modules tests/
```

## 使用示例

### 1. TLE解析和SGP4传播

```python
from modules.orbit_calculation import TLEParser, SGP4Propagator

# 解析TLE
parser = TLEParser()
tle_objects = parser.parse(tle_string)

# SGP4传播
propagator = SGP4Propagator()
position, velocity, error = propagator.propagate(tle_objects[0], julian_date)
```

### 2. 数字孪生仿真

```python
from modules.digital_twin import StateVector, Satellite, NumericalPropagator

# 创建初始状态
initial_state = StateVector(
    position=np.array([6678.0, 0.0, 0.0]),
    velocity=np.array([0.0, 7.727, 0.0])
)

# 创建卫星对象
satellite = Satellite(mass=1000.0, drag_area=10.0)

# 数值传播
propagator = NumericalPropagator()
ephemeris = propagator.propagate(initial_state, satellite, start_jd, end_jd)
```

### 3. 拓扑分析

```python
from modules.topology_simulation import TopologySimulator

# 创建拓扑模拟器
simulator = TopologySimulator(max_link_distance=5000.0)

# 运行仿真
topology_snapshots = simulator.run_simulation(ephemeris_data)
```

## 性能优化

系统包含多层性能优化：

1. **Cython优化**: 关键数值计算函数使用Cython编译
2. **向量化计算**: 大量使用NumPy向量化操作
3. **内存优化**: 使用结构化数组减少内存占用
4. **算法优化**: 实现高效的数值积分和拓扑算法

## 开发指南

### 代码规范

- 使用Black进行代码格式化
- 使用flake8进行代码检查
- 遵循PEP 8编码规范
- 编写完整的文档字符串

### 测试要求

- 所有新功能必须包含单元测试
- 测试覆盖率应保持在90%以上
- 使用pytest框架编写测试

### 提交规范

- 提交信息应清晰描述变更内容
- 大的功能变更应分解为多个小的提交
- 确保所有测试通过后再提交

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请联系开发团队。

---

**SDT卫星数字孪生系统 v2.0**  
*高保真卫星轨道仿真和网络拓扑分析平台*
