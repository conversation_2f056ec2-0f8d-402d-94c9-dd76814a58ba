# 网络离散事件引擎 - 使用指南

## 概述

这是一个专为SDT卫星数字孪生系统设计的纯Python网络离散事件引擎，无需任何外部依赖库。

## ✅ 测试状态

- ✅ 基本功能测试通过
- ✅ 事件类型测试通过  
- ✅ 示例程序运行正常
- ✅ 所有核心组件工作正常

## 🚀 快速开始

### 1. 基本使用

```python
from engine import SimulationEngine
from engine.network_events import create_packet_arrival

# 创建仿真引擎
engine = SimulationEngine(max_simulation_time=10.0)

# 调度事件
packet_event = create_packet_arrival(
    event_id="packet_1",
    scheduled_time=1.0,
    source="node_A",
    destination="node_B",
    packet_id="packet_001",
    packet_size=1024,
    protocol="TCP"
)
engine.schedule_event(packet_event)

# 运行仿真
success = engine.run()
```

### 2. 运行示例

```bash
# 快速测试
python -m engine.quick_test

# 完整示例
python -m engine.examples

# 全面测试
python -m engine.test_engine
```

## 📋 核心功能

### 支持的事件类型
- 📦 **数据包事件**: 到达、发送、丢弃
- 🔗 **链路事件**: 上线、下线  
- 🛤️ **路由事件**: 更新、发现
- 📡 **传输事件**: 开始、结束
- ⏰ **系统事件**: 超时、周期更新

### 主要特性
- ⚡ **高效调度**: 基于堆的优先队列，O(log n)性能
- 🎯 **精确时间控制**: 支持暂停、恢复、时间步进
- 📊 **详细统计**: 实时监控事件处理和性能指标
- 🔧 **高度可扩展**: 支持自定义事件类型和处理器
- 🎮 **完整控制**: 仿真状态管理和回调支持

## 📁 文件结构

```
engine/
├── __init__.py              # 模块初始化
├── event.py                 # 事件基类和网络事件
├── scheduler.py             # 事件调度器
├── time_manager.py          # 时间管理器  
├── event_handler.py         # 事件处理器
├── simulation_engine.py     # 仿真引擎核心
├── network_events.py        # 网络事件工厂
├── examples.py              # 使用示例
├── test_engine.py           # 完整测试套件
├── quick_test.py            # 快速功能测试
├── README.md                # 详细文档
└── USAGE_GUIDE.md           # 本使用指南
```

## 🎯 典型应用场景

### 1. 网络协议仿真
- TCP/UDP数据传输
- 路由协议模拟
- 网络拓扑变化

### 2. 卫星通信仿真  
- 卫星链路管理
- 信号传播延迟
- 切换和路由

### 3. 分布式系统仿真
- 节点故障恢复
- 消息传递
- 负载均衡

## 📈 性能特点

- **事件处理速度**: >10,000 events/sec
- **内存使用**: 优化的事件队列管理
- **可扩展性**: 支持大规模事件队列（默认10,000个事件）
- **实时性**: 可选的实时同步功能

## 🛠️ 自定义开发

### 自定义事件处理器

```python
def custom_packet_handler(event):
    print(f"处理数据包: {event.packet_id}")
    # 自定义逻辑...
    return True

engine.register_event_handler(EventType.PACKET_ARRIVAL, custom_packet_handler)
```

### 自定义事件类型

```python
from engine.event import NetworkEvent, EventType

class CustomEvent(NetworkEvent):
    def __init__(self, event_id, scheduled_time, custom_data):
        super().__init__(
            event_id=event_id,
            event_type=EventType.PACKET_ARRIVAL,  # 或自定义类型
            scheduled_time=scheduled_time,
            data={'custom_data': custom_data}
        )
```

## 🔍 故障排除

### 常见问题

1. **导入错误**: 确保以模块方式运行 `python -m engine.xxx`
2. **事件未处理**: 检查事件时间是否在仿真时间范围内
3. **性能问题**: 调整队列大小和时间步长

### 调试技巧

```python
# 启用事件日志
engine.set_config('log_events', True)
engine.set_config('event_log_file', 'simulation.log')

# 获取详细统计
stats = engine.get_statistics()
print(f"队列大小: {stats['queue_size']}")
print(f"处理事件: {stats['simulation']['total_events_processed']}")
```

## 📞 技术支持

- 查看 `README.md` 了解详细API文档
- 运行 `python -m engine.test_engine` 进行全面测试  
- 参考 `examples.py` 中的完整示例

---

**版本**: 1.0.0  
**作者**: SDT开发团队  
**更新**: 2024年 