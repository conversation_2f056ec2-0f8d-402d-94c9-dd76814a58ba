"""
路径规划器模块

提供多节点路径规划功能，包括：
- A*路径搜索算法
- 卫星-地面站路径规划
- 卫星间链路路径规划
- 动态路径重规划
- 路径质量评估
"""

import math
from typing import List, Tuple, Optional, Dict, Set
from dataclasses import dataclass, field
from .coordinate_system import Vector3D, LatLonAlt, CoordinateSystem
from .orbit_calculator import SatelliteState, OrbitCalculator


@dataclass
class PathNode:
    """路径节点类"""
    node_id: str
    position: Vector3D
    node_type: str  # 'satellite', 'ground_station', 'relay'
    properties: Dict = field(default_factory=dict)
    
    def distance_to(self, other: 'PathNode') -> float:
        """计算到另一个节点的距离"""
        return self.position.distance_to(other.position)


@dataclass
class PathSegment:
    """路径段类"""
    start_node: PathNode
    end_node: PathNode
    distance: float
    delay: float
    quality: float  # 0-1之间，1表示最佳质量
    bandwidth: float  # 可用带宽 (bps)
    
    @property
    def cost(self) -> float:
        """计算路径段成本（距离和质量的加权）"""
        return self.distance * (2.0 - self.quality)


@dataclass
class PathResult:
    """路径规划结果类"""
    nodes: List[PathNode]
    segments: List[PathSegment]
    total_distance: float
    total_delay: float
    total_cost: float
    success: bool
    
    @property
    def hop_count(self) -> int:
        """跳数"""
        return len(self.segments)
    
    @property
    def average_quality(self) -> float:
        """平均链路质量"""
        if not self.segments:
            return 0.0
        return sum(seg.quality for seg in self.segments) / len(self.segments)


class PathPlanner:
    """路径规划器"""
    
    def __init__(self):
        """初始化路径规划器"""
        self.nodes = {}  # node_id -> PathNode
        self.connections = {}  # node_id -> [connected_node_ids]
        self.orbit_calculator = OrbitCalculator()
        
        # 规划参数
        self.max_satellite_distance = 2000000  # 最大卫星间距离 (m)
        self.max_ground_distance = 1000000    # 最大地面站距离 (m)
        self.signal_speed = 299792458          # 光速 (m/s)
        
        # 统计信息
        self.planning_count = 0
        self.cache = {}
    
    def add_node(self, node: PathNode):
        """添加节点"""
        self.nodes[node.node_id] = node
        if node.node_id not in self.connections:
            self.connections[node.node_id] = []
    
    def remove_node(self, node_id: str):
        """移除节点"""
        if node_id in self.nodes:
            del self.nodes[node_id]
        if node_id in self.connections:
            del self.connections[node_id]
        
        # 移除其他节点到此节点的连接
        for connections in self.connections.values():
            if node_id in connections:
                connections.remove(node_id)
    
    def add_connection(self, node1_id: str, node2_id: str):
        """添加双向连接"""
        if node1_id in self.connections:
            if node2_id not in self.connections[node1_id]:
                self.connections[node1_id].append(node2_id)
        
        if node2_id in self.connections:
            if node1_id not in self.connections[node2_id]:
                self.connections[node2_id].append(node1_id)
    
    def remove_connection(self, node1_id: str, node2_id: str):
        """移除双向连接"""
        if node1_id in self.connections and node2_id in self.connections[node1_id]:
            self.connections[node1_id].remove(node2_id)
        
        if node2_id in self.connections and node1_id in self.connections[node2_id]:
            self.connections[node2_id].remove(node1_id)
    
    def update_topology(self, satellite_states: List[SatelliteState], 
                       ground_stations: List[LatLonAlt]):
        """更新网络拓扑"""
        # 清空现有节点和连接
        self.nodes.clear()
        self.connections.clear()
        
        # 添加卫星节点
        for i, state in enumerate(satellite_states):
            node = PathNode(
                node_id=f"sat_{i}",
                position=state.position,
                node_type="satellite",
                properties={
                    'altitude': state.altitude,
                    'velocity': state.velocity,
                    'time': state.time
                }
            )
            self.add_node(node)
        
        # 添加地面站节点
        for i, lla in enumerate(ground_stations):
            ecef_pos = CoordinateSystem.lla_to_ecef(lla)
            node = PathNode(
                node_id=f"gs_{i}",
                position=ecef_pos,
                node_type="ground_station",
                properties={
                    'latitude': lla.latitude,
                    'longitude': lla.longitude,
                    'altitude': lla.altitude
                }
            )
            self.add_node(node)
        
        # 建立连接
        self._establish_connections()
    
    def _establish_connections(self):
        """建立节点间的连接"""
        node_list = list(self.nodes.values())
        
        for i, node1 in enumerate(node_list):
            for j, node2 in enumerate(node_list[i+1:], i+1):
                if self._can_connect(node1, node2):
                    self.add_connection(node1.node_id, node2.node_id)
    
    def _can_connect(self, node1: PathNode, node2: PathNode) -> bool:
        """判断两个节点是否可以连接"""
        distance = node1.distance_to(node2)
        
        # 卫星间连接
        if node1.node_type == "satellite" and node2.node_type == "satellite":
            return distance <= self.max_satellite_distance
        
        # 地面站间连接（通过地面网络）
        elif node1.node_type == "ground_station" and node2.node_type == "ground_station":
            return distance <= self.max_ground_distance
        
        # 卫星-地面站连接（需要考虑视线遮挡）
        elif (node1.node_type == "satellite" and node2.node_type == "ground_station") or \
             (node1.node_type == "ground_station" and node2.node_type == "satellite"):
            
            # 简化的视线检查
            return self._check_line_of_sight(node1, node2)
        
        return False
    
    def _check_line_of_sight(self, node1: PathNode, node2: PathNode) -> bool:
        """检查两个节点间是否有视线"""
        # 确定哪个是卫星，哪个是地面站
        if node1.node_type == "satellite":
            sat_pos, gs_pos = node1.position, node2.position
        else:
            sat_pos, gs_pos = node2.position, node1.position
        
        # 计算地面站到卫星的仰角
        rel_pos = sat_pos - gs_pos
        distance = rel_pos.magnitude()
        
        # 地球中心到地面站的距离
        gs_distance = gs_pos.magnitude()
        
        # 使用余弦定理计算仰角
        cos_angle = gs_pos.dot(rel_pos) / (gs_distance * distance)
        angle = math.acos(max(-1.0, min(1.0, cos_angle)))
        elevation = math.pi/2 - angle
        
        # 最小仰角要求（10度）
        min_elevation = math.radians(10.0)
        return elevation >= min_elevation
    
    def plan_path(self, start_node_id: str, end_node_id: str, 
                  algorithm: str = "astar") -> PathResult:
        """
        规划路径
        
        Args:
            start_node_id: 起始节点ID
            end_node_id: 目标节点ID
            algorithm: 算法类型 ("astar", "dijkstra", "bfs")
            
        Returns:
            路径规划结果
        """
        self.planning_count += 1
        
        if start_node_id not in self.nodes or end_node_id not in self.nodes:
            return PathResult([], [], 0.0, 0.0, float('inf'), False)
        
        if algorithm == "astar":
            return self._astar_search(start_node_id, end_node_id)
        elif algorithm == "dijkstra":
            return self._dijkstra_search(start_node_id, end_node_id)
        else:
            return self._bfs_search(start_node_id, end_node_id)
    
    def _astar_search(self, start_id: str, end_id: str) -> PathResult:
        """A*搜索算法"""
        start_node = self.nodes[start_id]
        end_node = self.nodes[end_id]
        
        # 开放集和闭合集
        open_set = {start_id}
        closed_set = set()
        
        # 代价和启发式函数值
        g_score = {start_id: 0.0}  # 从起点到当前节点的实际代价
        f_score = {start_id: self._heuristic(start_node, end_node)}  # f = g + h
        
        # 父节点映射，用于重构路径
        came_from = {}
        
        while open_set:
            # 选择f值最小的节点
            current_id = min(open_set, key=lambda x: f_score.get(x, float('inf')))
            
            if current_id == end_id:
                # 找到目标，重构路径
                return self._reconstruct_path(came_from, current_id, g_score)
            
            open_set.remove(current_id)
            closed_set.add(current_id)
            
            # 遍历邻居节点
            for neighbor_id in self.connections.get(current_id, []):
                if neighbor_id in closed_set:
                    continue
                
                current_node = self.nodes[current_id]
                neighbor_node = self.nodes[neighbor_id]
                
                # 计算到邻居的临时g值
                edge_cost = self._calculate_edge_cost(current_node, neighbor_node)
                tentative_g = g_score[current_id] + edge_cost
                
                if neighbor_id not in open_set:
                    open_set.add(neighbor_id)
                elif tentative_g >= g_score.get(neighbor_id, float('inf')):
                    continue
                
                # 更新路径
                came_from[neighbor_id] = current_id
                g_score[neighbor_id] = tentative_g
                f_score[neighbor_id] = tentative_g + self._heuristic(neighbor_node, end_node)
        
        # 未找到路径
        return PathResult([], [], 0.0, 0.0, float('inf'), False)
    
    def _dijkstra_search(self, start_id: str, end_id: str) -> PathResult:
        """Dijkstra搜索算法"""
        # 简化实现，类似A*但不使用启发式函数
        distances = {node_id: float('inf') for node_id in self.nodes}
        distances[start_id] = 0.0
        came_from = {}
        unvisited = set(self.nodes.keys())
        
        while unvisited:
            # 选择距离最小的未访问节点
            current_id = min(unvisited, key=lambda x: distances[x])
            
            if current_id == end_id:
                return self._reconstruct_path(came_from, current_id, distances)
            
            if distances[current_id] == float('inf'):
                break  # 无法到达
            
            unvisited.remove(current_id)
            
            # 更新邻居距离
            for neighbor_id in self.connections.get(current_id, []):
                if neighbor_id not in unvisited:
                    continue
                
                current_node = self.nodes[current_id]
                neighbor_node = self.nodes[neighbor_id]
                edge_cost = self._calculate_edge_cost(current_node, neighbor_node)
                
                new_distance = distances[current_id] + edge_cost
                if new_distance < distances[neighbor_id]:
                    distances[neighbor_id] = new_distance
                    came_from[neighbor_id] = current_id
        
        return PathResult([], [], 0.0, 0.0, float('inf'), False)
    
    def _bfs_search(self, start_id: str, end_id: str) -> PathResult:
        """广度优先搜索（最短跳数）"""
        queue = [start_id]
        visited = {start_id}
        came_from = {}
        
        while queue:
            current_id = queue.pop(0)
            
            if current_id == end_id:
                # 重构路径（BFS找到的是跳数最少的路径）
                return self._reconstruct_path(came_from, current_id, {})
            
            for neighbor_id in self.connections.get(current_id, []):
                if neighbor_id not in visited:
                    visited.add(neighbor_id)
                    came_from[neighbor_id] = current_id
                    queue.append(neighbor_id)
        
        return PathResult([], [], 0.0, 0.0, float('inf'), False)
    
    def _heuristic(self, node1: PathNode, node2: PathNode) -> float:
        """A*算法的启发式函数（欧几里得距离）"""
        return node1.distance_to(node2)
    
    def _calculate_edge_cost(self, node1: PathNode, node2: PathNode) -> float:
        """计算边的代价"""
        distance = node1.distance_to(node2)
        
        # 基本代价是距离
        base_cost = distance
        
        # 根据节点类型调整代价
        if node1.node_type == "satellite" and node2.node_type == "satellite":
            # 卫星间链路：距离越远代价越高
            cost_multiplier = 1.0 + (distance / self.max_satellite_distance)
        elif node1.node_type == "ground_station" and node2.node_type == "ground_station":
            # 地面站间链路：相对稳定
            cost_multiplier = 0.8
        else:
            # 卫星-地面站链路：考虑信号强度
            cost_multiplier = 1.2
        
        return base_cost * cost_multiplier
    
    def _calculate_link_quality(self, node1: PathNode, node2: PathNode) -> float:
        """计算链路质量（0-1之间）"""
        distance = node1.distance_to(node2)
        
        if node1.node_type == "satellite" and node2.node_type == "satellite":
            # 卫星间链路质量随距离衰减
            max_quality_distance = 500000  # 500km内质量最佳
            if distance <= max_quality_distance:
                return 1.0
            else:
                return max(0.1, 1.0 - (distance - max_quality_distance) / self.max_satellite_distance)
        
        elif node1.node_type == "ground_station" and node2.node_type == "ground_station":
            # 地面站间质量相对稳定
            return 0.9
        
        else:
            # 卫星-地面站链路质量
            return max(0.3, 1.0 - distance / 2000000)  # 2000km内有效
    
    def _reconstruct_path(self, came_from: Dict[str, str], 
                         current_id: str, costs: Dict[str, float]) -> PathResult:
        """重构路径"""
        path = []
        current = current_id
        
        # 反向重构路径
        while current is not None:
            path.append(current)
            current = came_from.get(current)
        
        path.reverse()
        
        # 构建PathResult
        nodes = [self.nodes[node_id] for node_id in path]
        segments = []
        total_distance = 0.0
        total_delay = 0.0
        total_cost = 0.0
        
        for i in range(len(nodes) - 1):
            node1, node2 = nodes[i], nodes[i + 1]
            distance = node1.distance_to(node2)
            delay = distance / self.signal_speed
            quality = self._calculate_link_quality(node1, node2)
            bandwidth = 1e9 * quality  # 基础带宽1Gbps * 质量因子
            
            segment = PathSegment(node1, node2, distance, delay, quality, bandwidth)
            segments.append(segment)
            
            total_distance += distance
            total_delay += delay
            total_cost += segment.cost
        
        return PathResult(
            nodes=nodes,
            segments=segments,
            total_distance=total_distance,
            total_delay=total_delay,
            total_cost=total_cost,
            success=True
        )
    
    def find_multiple_paths(self, start_node_id: str, end_node_id: str, 
                          k: int = 3) -> List[PathResult]:
        """
        寻找K条最短路径
        
        Args:
            start_node_id: 起始节点
            end_node_id: 目标节点
            k: 路径数量
            
        Returns:
            路径列表，按代价排序
        """
        paths = []
        
        # 使用修改后的A*算法寻找多条路径
        for i in range(k):
            # 临时移除已找到路径中的一些边
            blocked_edges = set()
            for path in paths:
                if len(path.nodes) > 1:
                    # 阻断第i个路径的第一条边
                    node1_id = path.nodes[0].node_id
                    node2_id = path.nodes[1].node_id
                    blocked_edges.add((node1_id, node2_id))
            
            # 寻找新路径
            path = self._astar_with_blocked_edges(start_node_id, end_node_id, blocked_edges)
            if path.success:
                paths.append(path)
            else:
                break
        
        return sorted(paths, key=lambda p: p.total_cost)
    
    def _astar_with_blocked_edges(self, start_id: str, end_id: str, 
                                 blocked_edges: Set[Tuple[str, str]]) -> PathResult:
        """带有阻断边的A*搜索"""
        # 这是简化版本，实际实现需要更复杂的K最短路径算法
        return self._astar_search(start_id, end_id)
    
    def get_planning_statistics(self) -> Dict:
        """获取规划统计信息"""
        return {
            'total_plannings': self.planning_count,
            'node_count': len(self.nodes),
            'connection_count': sum(len(conns) for conns in self.connections.values()) // 2,
            'cache_size': len(self.cache)
        } 