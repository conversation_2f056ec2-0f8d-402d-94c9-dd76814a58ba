#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轨道计算模块测试
测试TLE解析和SGP4传播功能
"""

import pytest
import numpy as np
from datetime import datetime, timezone

from modules.orbit_calculation import TLE, TLEParser, SGP4Propagator


class TestTLE:
    """TLE类测试"""
    
    def test_tle_creation(self):
        """测试TLE对象创建"""
        tle = TLE(
            name="TEST SAT",
            satellite_number=12345,
            classification="U",
            launch_year=2023,
            launch_number=1,
            launch_piece="A",
            epoch_year=2023,
            epoch_day=100.5,
            mean_motion_dot=0.0001,
            mean_motion_ddot=0.0,
            bstar_drag=0.0001,
            ephemeris_type=0,
            element_set_number=999,
            inclination=51.6,
            raan=45.0,
            eccentricity=0.001,
            arg_of_perigee=90.0,
            mean_anomaly=180.0,
            mean_motion=15.5,
            rev_at_epoch=1000
        )
        
        assert tle.name == "TEST SAT"
        assert tle.satellite_number == 12345
        assert tle.inclination == 51.6
        assert tle.mean_motion == 15.5
    
    def test_tle_validation(self):
        """测试TLE数据验证"""
        # 测试无效倾角
        with pytest.raises(ValueError, match="轨道倾角必须在0-180度之间"):
            TLE(
                name="TEST SAT",
                satellite_number=12345,
                classification="U",
                launch_year=2023,
                launch_number=1,
                launch_piece="A",
                epoch_year=2023,
                epoch_day=100.5,
                mean_motion_dot=0.0,
                mean_motion_ddot=0.0,
                bstar_drag=0.0,
                ephemeris_type=0,
                element_set_number=999,
                inclination=200.0,  # 无效值
                raan=45.0,
                eccentricity=0.001,
                arg_of_perigee=90.0,
                mean_anomaly=180.0,
                mean_motion=15.5,
                rev_at_epoch=1000
            )
        
        # 测试无效偏心率
        with pytest.raises(ValueError, match="偏心率必须在0-1之间"):
            TLE(
                name="TEST SAT",
                satellite_number=12345,
                classification="U",
                launch_year=2023,
                launch_number=1,
                launch_piece="A",
                epoch_year=2023,
                epoch_day=100.5,
                mean_motion_dot=0.0,
                mean_motion_ddot=0.0,
                bstar_drag=0.0,
                ephemeris_type=0,
                element_set_number=999,
                inclination=51.6,
                raan=45.0,
                eccentricity=1.5,  # 无效值
                arg_of_perigee=90.0,
                mean_anomaly=180.0,
                mean_motion=15.5,
                rev_at_epoch=1000
            )
    
    def test_tle_dict_conversion(self):
        """测试TLE字典转换"""
        tle = TLE(
            name="TEST SAT",
            satellite_number=12345,
            classification="U",
            launch_year=2023,
            launch_number=1,
            launch_piece="A",
            epoch_year=2023,
            epoch_day=100.5,
            mean_motion_dot=0.0,
            mean_motion_ddot=0.0,
            bstar_drag=0.0,
            ephemeris_type=0,
            element_set_number=999,
            inclination=51.6,
            raan=45.0,
            eccentricity=0.001,
            arg_of_perigee=90.0,
            mean_anomaly=180.0,
            mean_motion=15.5,
            rev_at_epoch=1000
        )
        
        # 转换为字典
        tle_dict = tle.to_dict()
        assert isinstance(tle_dict, dict)
        assert tle_dict['name'] == "TEST SAT"
        assert tle_dict['satellite_number'] == 12345
        
        # 从字典创建
        tle2 = TLE.from_dict(tle_dict)
        assert tle2.name == tle.name
        assert tle2.satellite_number == tle.satellite_number
        assert tle2.inclination == tle.inclination


class TestTLEParser:
    """TLE解析器测试"""
    
    def test_parse_three_line_format(self):
        """测试3行格式TLE解析"""
        tle_string = """ISS (ZARYA)
1 25544U 98067A   23001.00000000  .00002182  00000-0  40768-4 0  9990
2 25544  51.6461 339.7939 0001220  92.8340 267.3124 15.49309620368473"""
        
        parser = TLEParser()
        tle_objects = parser.parse(tle_string)
        
        assert len(tle_objects) == 1
        tle = tle_objects[0]
        
        assert tle.name == "ISS (ZARYA)"
        assert tle.satellite_number == 25544
        assert abs(tle.inclination - 51.6461) < 1e-4
        assert abs(tle.mean_motion - 15.49309620) < 1e-6
    
    def test_parse_two_line_format(self):
        """测试2行格式TLE解析"""
        tle_string = """1 25544U 98067A   23001.00000000  .00002182  00000-0  40768-4 0  9990
2 25544  51.6461 339.7939 0001220  92.8340 267.3124 15.49309620368473"""
        
        parser = TLEParser()
        tle_objects = parser.parse(tle_string)
        
        assert len(tle_objects) == 1
        tle = tle_objects[0]
        
        assert tle.name == "SATELLITE 25544"  # 默认名称
        assert tle.satellite_number == 25544
    
    def test_parse_multiple_tle(self):
        """测试多个TLE解析"""
        tle_string = """ISS (ZARYA)
1 25544U 98067A   23001.00000000  .00002182  00000-0  40768-4 0  9990
2 25544  51.6461 339.7939 0001220  92.8340 267.3124 15.49309620368473
HUBBLE SPACE TELESCOPE
1 20580U 90037B   23001.00000000  .00000000  00000-0  00000-0 0  9999
2 20580  28.4690 123.4567 0002000 100.0000 260.0000 15.09000000123456"""
        
        parser = TLEParser()
        tle_objects = parser.parse(tle_string)
        
        assert len(tle_objects) == 2
        assert tle_objects[0].name == "ISS (ZARYA)"
        assert tle_objects[1].name == "HUBBLE SPACE TELESCOPE"
    
    def test_parse_invalid_tle(self):
        """测试无效TLE解析"""
        parser = TLEParser()
        
        # 空字符串
        with pytest.raises(Exception):
            parser.parse("")
        
        # 格式错误
        with pytest.raises(Exception):
            parser.parse("invalid tle data")
    
    def test_checksum_validation(self):
        """测试校验和验证"""
        parser = TLEParser()
        
        # 正确的校验和
        valid_line = "1 25544U 98067A   23001.00000000  .00002182  00000-0  40768-4 0  9990"
        assert parser._validate_checksum(valid_line) == True
        
        # 错误的校验和
        invalid_line = "1 25544U 98067A   23001.00000000  .00002182  00000-0  40768-4 0  9999"
        assert parser._validate_checksum(invalid_line) == False


class TestSGP4Propagator:
    """SGP4传播器测试"""
    
    def setup_method(self):
        """测试设置"""
        self.tle = TLE(
            name="TEST SAT",
            satellite_number=25544,
            classification="U",
            launch_year=98,
            launch_number=67,
            launch_piece="A",
            epoch_year=2023,
            epoch_day=1.0,
            mean_motion_dot=0.00002182,
            mean_motion_ddot=0.0,
            bstar_drag=0.40768e-4,
            ephemeris_type=0,
            element_set_number=999,
            inclination=51.6461,
            raan=339.7939,
            eccentricity=0.0001220,
            arg_of_perigee=92.8340,
            mean_anomaly=267.3124,
            mean_motion=15.49309620,
            rev_at_epoch=36847
        )
        
        self.propagator = SGP4Propagator()
    
    def test_sgp4_initialization(self):
        """测试SGP4初始化"""
        # 第一次传播会触发初始化
        jd_utc = 2459945.5  # 2023-01-01 00:00:00 UTC
        
        position, velocity, error_code = self.propagator.propagate(self.tle, jd_utc)
        
        assert error_code == 0
        assert len(position) == 3
        assert len(velocity) == 3
        assert self.propagator.initialized == True
    
    def test_sgp4_propagation(self):
        """测试SGP4传播"""
        jd_utc = 2459945.5  # 2023-01-01 00:00:00 UTC
        
        position, velocity, error_code = self.propagator.propagate(self.tle, jd_utc)
        
        assert error_code == 0
        
        # 检查位置向量合理性（ISS轨道高度约400km）
        r_norm = np.linalg.norm(position)
        assert 6700 < r_norm < 6900  # km
        
        # 检查速度向量合理性（ISS速度约7.7km/s）
        v_norm = np.linalg.norm(velocity)
        assert 7.0 < v_norm < 8.0  # km/s
    
    def test_sgp4_time_series(self):
        """测试SGP4时间序列传播"""
        jd_start = 2459945.5  # 2023-01-01 00:00:00 UTC
        
        positions = []
        velocities = []
        
        # 传播24小时，每小时一个点
        for i in range(25):
            jd = jd_start + i / 24.0
            position, velocity, error_code = self.propagator.propagate(self.tle, jd)
            
            assert error_code == 0
            positions.append(position)
            velocities.append(velocity)
        
        # 检查轨道连续性
        for i in range(1, len(positions)):
            # 相邻位置不应该相差太大
            distance = np.linalg.norm(positions[i] - positions[i-1])
            assert distance < 1000  # km，1小时内移动距离应该合理
    
    def test_kepler_equation_solver(self):
        """测试开普勒方程求解器"""
        # 测试不同偏心率和平近点角
        test_cases = [
            (0.0, 0.0),      # 圆轨道，M=0
            (0.0, np.pi),    # 圆轨道，M=π
            (0.1, 0.5),      # 小偏心率
            (0.5, 1.0),      # 中等偏心率
            (0.9, 2.0),      # 大偏心率
        ]
        
        for e, M in test_cases:
            E = self.propagator._solve_kepler_equation(M, e)
            
            # 验证开普勒方程：M = E - e*sin(E)
            calculated_M = E - e * np.sin(E)
            assert abs(calculated_M - M) < 1e-10
    
    def test_coordinate_transformation(self):
        """测试坐标系转换"""
        # 轨道平面内的位置和速度
        r_orb = np.array([7000.0, 0.0, 0.0])  # km
        v_orb = np.array([0.0, 7.5, 0.0])     # km/s
        
        # 轨道要素
        i = np.radians(51.6)    # 倾角
        omega = np.radians(90.0)  # 近地点幅角
        Omega = np.radians(45.0)  # 升交点赤经
        
        # 转换到TEME坐标系
        r_teme, v_teme = self.propagator._orbital_to_teme(r_orb, v_orb, i, omega, Omega)
        
        # 检查向量长度保持不变
        assert abs(np.linalg.norm(r_teme) - np.linalg.norm(r_orb)) < 1e-10
        assert abs(np.linalg.norm(v_teme) - np.linalg.norm(v_orb)) < 1e-10


if __name__ == '__main__':
    pytest.main([__file__])
