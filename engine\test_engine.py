"""
网络离散事件引擎测试脚本

验证引擎的核心功能和正确性。
"""

import sys
import time
from typing import List

# 导入引擎组件
from .simulation_engine import SimulationEngine, SimulationState
from .event import Event, EventType, EventPriority
from .scheduler import EventScheduler
from .time_manager import TimeManager
from .event_handler import EventHandler
from .network_events import *


def test_basic_event_creation():
    """测试基本事件创建"""
    print("测试: 基本事件创建")
    
    try:
        # 创建基本事件
        event = Event(
            event_id="test_event_1",
            event_type=EventType.PACKET_ARRIVAL,
            scheduled_time=1.0,
            priority=EventPriority.NORMAL
        )
        
        assert event.event_id == "test_event_1"
        assert event.event_type == EventType.PACKET_ARRIVAL
        assert event.scheduled_time == 1.0
        assert event.priority == EventPriority.NORMAL
        assert not event.is_cancelled()
        assert not event.is_processed()
        
        # 创建网络事件
        network_event = NetworkEvent(
            event_id="network_test_1",
            event_type=EventType.PACKET_ARRIVAL,
            scheduled_time=2.0,
            source_node="node_A",
            destination_node="node_B",
            packet_id="packet_001",
            packet_size=1024,
            protocol="TCP"
        )
        
        assert network_event.source_node == "node_A"
        assert network_event.destination_node == "node_B"
        assert network_event.packet_id == "packet_001"
        assert network_event.packet_size == 1024
        
        print("✓ 基本事件创建测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本事件创建测试失败: {e}")
        return False


def test_event_scheduler():
    """测试事件调度器"""
    print("测试: 事件调度器")
    
    try:
        scheduler = EventScheduler()
        
        # 创建测试事件
        events = [
            Event("event_1", EventType.PACKET_ARRIVAL, 3.0),
            Event("event_2", EventType.PACKET_DEPARTURE, 1.0),
            Event("event_3", EventType.LINK_UP, 2.0),
            Event("event_4", EventType.TIMEOUT, 1.5, EventPriority.HIGH)
        ]
        
        # 调度事件
        for event in events:
            success = scheduler.schedule_event(event)
            assert success, f"调度事件 {event.event_id} 失败"
        
        assert scheduler.get_queue_size() == 4
        
        # 测试事件顺序（应该按时间和优先级排序）
        next_event = scheduler.get_next_event()
        assert next_event.event_id == "event_2"  # 时间1.0
        
        # 弹出并检查顺序
        processed_events = []
        while True:
            event = scheduler.pop_next_event()
            if not event:
                break
            processed_events.append(event)
        
        expected_order = ["event_2", "event_4", "event_3", "event_1"]
        actual_order = [e.event_id for e in processed_events]
        assert actual_order == expected_order, f"事件顺序错误: {actual_order}"
        
        print("✓ 事件调度器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 事件调度器测试失败: {e}")
        return False


def test_time_manager():
    """测试时间管理器"""
    print("测试: 时间管理器")
    
    try:
        time_mgr = TimeManager(start_time=0.0)
        
        # 测试时间推进
        assert time_mgr.get_current_time() == 0.0
        
        success = time_mgr.advance_time(5.0)
        assert success
        assert time_mgr.get_current_time() == 5.0
        assert time_mgr.get_elapsed_time() == 5.0
        
        # 测试不允许时间倒退
        success = time_mgr.advance_time(3.0)
        assert not success
        assert time_mgr.get_current_time() == 5.0
        
        # 测试暂停和恢复
        time_mgr.pause()
        assert time_mgr.is_paused()
        
        # 暂停时不能推进时间
        success = time_mgr.advance_time(10.0)
        assert not success
        
        time_mgr.resume()
        assert not time_mgr.is_paused()
        
        success = time_mgr.advance_time(10.0)
        assert success
        assert time_mgr.get_current_time() == 10.0
        
        # 测试检查点
        time_mgr.create_checkpoint("test_checkpoint")
        time_mgr.advance_time(15.0)
        
        checkpoint = time_mgr.get_checkpoint("test_checkpoint")
        assert checkpoint is not None
        assert checkpoint['simulation_time'] == 10.0
        
        time_since = time_mgr.get_time_since_checkpoint("test_checkpoint")
        assert time_since == 5.0
        
        print("✓ 时间管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 时间管理器测试失败: {e}")
        return False


def test_event_handler():
    """测试事件处理器"""
    print("测试: 事件处理器")
    
    try:
        handler = EventHandler()
        
        # 测试默认处理器
        event = Event("test_event", EventType.PACKET_ARRIVAL, 1.0)
        success = handler.handle_event(event)
        assert success
        
        stats = handler.get_statistics()
        assert stats['processed_events'] == 1
        assert stats['failed_events'] == 0
        
        # 测试自定义处理器
        custom_handler_called = False
        
        def custom_handler(event):
            nonlocal custom_handler_called
            custom_handler_called = True
            return True
        
        handler.register_handler(EventType.LINK_UP, custom_handler)
        
        link_event = Event("link_event", EventType.LINK_UP, 2.0)
        success = handler.handle_event(link_event)
        assert success
        assert custom_handler_called
        
        # 测试失败处理器
        def failing_handler(event):
            return False
        
        handler.register_handler(EventType.LINK_DOWN, failing_handler)
        
        down_event = Event("down_event", EventType.LINK_DOWN, 3.0)
        success = handler.handle_event(down_event)
        assert not success
        
        stats = handler.get_statistics()
        assert stats['failed_events'] == 1
        
        print("✓ 事件处理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 事件处理器测试失败: {e}")
        return False


def test_network_events():
    """测试网络事件工厂方法"""
    print("测试: 网络事件工厂方法")
    
    try:
        # 测试数据包到达事件
        arrival_event = create_packet_arrival(
            event_id="arrival_test",
            scheduled_time=1.0,
            source="node_A",
            destination="node_B",
            packet_id="packet_123",
            packet_size=512,
            protocol="UDP"
        )
        
        assert arrival_event.event_type == EventType.PACKET_ARRIVAL
        assert arrival_event.source_node == "node_A"
        assert arrival_event.destination_node == "node_B"
        assert arrival_event.packet_id == "packet_123"
        assert arrival_event.packet_size == 512
        assert arrival_event.protocol == "UDP"
        
        # 测试链路状态事件
        link_up = create_link_up(
            event_id="link_up_test",
            scheduled_time=2.0,
            link_id="link_AB",
            node_a="router_A",
            node_b="router_B"
        )
        
        assert link_up.event_type == EventType.LINK_UP
        assert link_up.source_node == "router_A"
        assert link_up.destination_node == "router_B"
        assert link_up.get_data('link_id') == "link_AB"
        assert link_up.get_data('is_up') is True
        
        # 测试路由更新事件
        route_update = create_route_update(
            event_id="route_test",
            scheduled_time=3.0,
            node_id="router_A",
            destination="router_D",
            next_hop="router_B",
            metric=2.5
        )
        
        assert route_update.event_type == EventType.ROUTE_UPDATE
        assert route_update.source_node == "router_A"
        assert route_update.destination_node == "router_D"
        assert route_update.get_data('next_hop') == "router_B"
        assert route_update.get_data('metric') == 2.5
        
        print("✓ 网络事件工厂方法测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 网络事件工厂方法测试失败: {e}")
        return False


def test_simulation_engine():
    """测试仿真引擎"""
    print("测试: 仿真引擎")
    
    try:
        engine = SimulationEngine(max_simulation_time=10.0)
        
        # 测试初始状态
        assert engine.get_state() == SimulationState.IDLE
        assert engine.get_current_time() == 0.0
        
        # 调度一些测试事件
        events_scheduled = 0
        
        for i in range(5):
            success = engine.schedule_event_at(
                event_id=f"test_event_{i}",
                event_type=EventType.PACKET_ARRIVAL,
                scheduled_time=float(i + 1),
                data={'test_data': f'data_{i}'}
            )
            if success:
                events_scheduled += 1
        
        assert events_scheduled == 5
        
        # 测试仿真运行
        success = engine.run(start_time=0.0, end_time=6.0)
        assert success
        
        assert engine.get_state() == SimulationState.STOPPED
        assert engine.get_current_time() == 5.0  # 最后一个事件的时间
        
        # 检查统计信息
        stats = engine.get_statistics()
        assert stats['simulation']['total_events_processed'] == 5
        assert stats['simulation']['total_events_scheduled'] == 5
        
        # 测试暂停和恢复
        engine.reset()
        
        # 调度长时间事件
        engine.schedule_event_at("long_event", EventType.TIMEOUT, 20.0)
        
        # 在新线程中运行仿真（模拟）
        engine.state = SimulationState.RUNNING
        engine.pause()
        assert engine.get_state() == SimulationState.PAUSED
        
        engine.resume()
        assert engine.get_state() == SimulationState.RUNNING
        
        engine.stop()
        assert engine.get_state() == SimulationState.STOPPED
        
        print("✓ 仿真引擎测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 仿真引擎测试失败: {e}")
        return False


def test_integration():
    """集成测试"""
    print("测试: 集成测试")
    
    try:
        engine = SimulationEngine(max_simulation_time=15.0)
        
        # 设置自定义处理器和统计
        packet_count = 0
        link_changes = 0
        
        def packet_handler(event):
            nonlocal packet_count
            packet_count += 1
            print(f"  处理数据包事件 {event.packet_id} 在时间 {event.scheduled_time}")
            return True
        
        def link_handler(event):
            nonlocal link_changes
            link_changes += 1
            state = "上线" if event.event_type == EventType.LINK_UP else "下线"
            print(f"  链路 {event.get_data('link_id')} {state} 在时间 {event.scheduled_time}")
            return True
        
        engine.register_event_handler(EventType.PACKET_ARRIVAL, packet_handler)
        engine.register_event_handler(EventType.PACKET_DEPARTURE, packet_handler)
        engine.register_event_handler(EventType.LINK_UP, link_handler)
        engine.register_event_handler(EventType.LINK_DOWN, link_handler)
        
        # 创建复杂的事件序列
        
        # 1. 网络初始化 - 链路上线
        for i in range(3):
            link_up = create_link_up(
                event_id=f"init_link_{i}",
                scheduled_time=0.1 + i * 0.1,
                link_id=f"link_{i}",
                node_a=f"router_{i}",
                node_b=f"router_{i+1}"
            )
            engine.schedule_event(link_up)
        
        # 2. 数据包传输
        for i in range(5):
            # 数据包到达
            arrival = create_packet_arrival(
                event_id=f"packet_{i}_arrival",
                scheduled_time=1.0 + i * 2.0,
                source="client",
                destination="server",
                packet_id=f"packet_{i:03d}",
                packet_size=1024 + i * 256,
                protocol="TCP"
            )
            engine.schedule_event(arrival)
            
            # 数据包发送
            departure = create_packet_departure(
                event_id=f"packet_{i}_departure",
                scheduled_time=1.5 + i * 2.0,
                source="server", 
                destination="client",
                packet_id=f"ack_{i:03d}",
                packet_size=64,
                protocol="TCP"
            )
            engine.schedule_event(departure)
        
        # 3. 网络故障和恢复
        link_down = create_link_down(
            event_id="link_failure",
            scheduled_time=6.0,
            link_id="link_1",
            node_a="router_1",
            node_b="router_2"
        )
        engine.schedule_event(link_down)
        
        link_up = create_link_up(
            event_id="link_recovery",
            scheduled_time=8.0,
            link_id="link_1",
            node_a="router_1",
            node_b="router_2"
        )
        engine.schedule_event(link_up)
        
        # 4. 路由更新
        for i in range(2):
            route_update = create_route_update(
                event_id=f"route_update_{i}",
                scheduled_time=9.0 + i * 1.0,
                node_id="router_0",
                destination="router_3",
                next_hop=f"router_{i+1}",
                metric=float(i + 1)
            )
            engine.schedule_event(route_update)
        
        print(f"  调度了 {engine.get_statistics()['simulation']['total_events_scheduled']} 个事件")
        
        # 运行仿真
        start_time = time.time()
        success = engine.run()
        end_time = time.time()
        
        assert success
        
        # 验证结果
        stats = engine.get_statistics()
        
        print(f"  仿真用时: {end_time - start_time:.3f}秒")
        print(f"  仿真时间: {engine.get_current_time():.1f}")
        print(f"  处理事件: {stats['simulation']['total_events_processed']}")
        print(f"  数据包事件: {packet_count}")
        print(f"  链路变化: {link_changes}")
        
        # 验证处理的事件数量是否正确
        expected_events = 3 + 10 + 2 + 2  # 链路初始化 + 数据包 + 链路故障恢复 + 路由更新
        assert stats['simulation']['total_events_processed'] == expected_events
        assert packet_count == 10  # 5个到达 + 5个发送
        assert link_changes == 5   # 3个初始上线 + 1个下线 + 1个恢复
        
        print("✓ 集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("网络离散事件引擎测试套件")
    print("=" * 50)
    
    tests = [
        test_basic_event_creation,
        test_event_scheduler,
        test_time_manager,
        test_event_handler,
        test_network_events,
        test_simulation_engine,
        test_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 出现异常: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！网络离散事件引擎工作正常。")
    else:
        print("❌ 部分测试失败，请检查代码。")
    
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 