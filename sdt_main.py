#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卫星数字孪生系统 (SDT - Satellite Digital Twin System)
主程序入口
"""

import sys
import threading
import time
from typing import Dict, Any
import logging

from core.satellite_topology_controller import SatelliteTopologyController
from core.satellite_path_calculator import SatellitePathCalculator
from core.satellite_simulator import SatelliteSimulator
from core.communication_module import CommunicationModule
from gui.visualization_module import VisualizationModule
from physics.physical_layer_simulator import PhysicalLayerSimulator
from utils.config_manager import ConfigManager
from utils.logger import setup_logger


class SDTSystem:
    """
    卫星数字孪生系统主控制器
    """
    
    def __init__(self):
        self.logger = setup_logger("SDT_MAIN")
        self.config = ConfigManager()
        self.running = False
        
        # 核心模块初始化
        self.topology_controller = None
        self.path_calculator = None
        self.simulator = None
        self.communication_module = None
        self.visualization = None
        self.physical_layer = None
        
        self.logger.info("SDT系统初始化完成")
    
    def initialize_modules(self):
        """初始化所有模块"""
        try:
            self.logger.info("开始初始化SDT系统模块...")
            
            # 初始化卫星拓扑控制器
            self.topology_controller = SatelliteTopologyController(self.config)
            self.logger.info("卫星拓扑控制器初始化完成")
            
            # 初始化路径计算控制器
            self.path_calculator = SatellitePathCalculator(self.config)
            self.logger.info("路径计算控制器初始化完成")
            
            # 初始化通信模块
            self.communication_module = CommunicationModule(self.config)
            self.logger.info("通信模块初始化完成")
            
            # 初始化卫星模拟器
            self.simulator = SatelliteSimulator(
                self.config, 
                self.topology_controller,
                self.path_calculator,
                self.communication_module
            )
            self.logger.info("卫星模拟器初始化完成")
            
            # 初始化物理层仿真模块
            self.physical_layer = PhysicalLayerSimulator(self.config)
            self.logger.info("物理层仿真模块初始化完成")
            
            # 初始化可视化模块
            self.visualization = VisualizationModule(
                self.config,
                self.topology_controller,
                self.simulator
            )
            self.logger.info("可视化模块初始化完成")
            
            self.logger.info("所有模块初始化成功")
            
        except Exception as e:
            self.logger.error(f"模块初始化失败: {e}")
            raise
    
    def start_system(self):
        """启动SDT系统"""
        try:
            self.logger.info("启动SDT系统...")
            self.running = True
            
            # 启动各个模块
            self.communication_module.start()
            self.simulator.start()
            
            # 启动可视化界面（在主线程中）
            self.visualization.start()
            
            self.logger.info("SDT系统启动成功")
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            self.stop_system()
            raise
    
    def stop_system(self):
        """停止SDT系统"""
        self.logger.info("正在停止SDT系统...")
        self.running = False
        
        if self.simulator:
            self.simulator.stop()
        if self.communication_module:
            self.communication_module.stop()
        if self.visualization:
            self.visualization.stop()
            
        self.logger.info("SDT系统已停止")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'running': self.running,
            'satellite_count': self.topology_controller.get_satellite_count() if self.topology_controller else 0,
            'active_links': self.simulator.get_active_links() if self.simulator else 0,
            'simulation_time': self.simulator.get_simulation_time() if self.simulator else 0,
            'cpu_usage': self.get_cpu_usage(),
            'memory_usage': self.get_memory_usage()
        }
    
    def get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        # 模拟CPU使用率
        import random
        return random.uniform(20.0, 80.0)
    
    def get_memory_usage(self) -> float:
        """获取内存使用率"""
        # 模拟内存使用率
        import random
        return random.uniform(30.0, 70.0)


def main():
    """主函数"""
    print("=" * 60)
    print("    卫星数字孪生系统 (SDT) v1.0")
    print("    Satellite Digital Twin System")
    print("=" * 60)
    
    sdt_system = None
    try:
        # 创建并初始化SDT系统
        sdt_system = SDTSystem()
        sdt_system.initialize_modules()
        
        # 启动系统
        sdt_system.start_system()
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭系统...")
    except Exception as e:
        print(f"系统运行错误: {e}")
        logging.error(f"系统运行错误: {e}", exc_info=True)
    finally:
        if sdt_system:
            sdt_system.stop_system()
        print("SDT系统已安全退出")


if __name__ == "__main__":
    main() 