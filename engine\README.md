# 网络离散事件引擎

一个专为网络仿真设计的纯Python离散事件引擎，不依赖任何外部库。

## 特性

- **完整的事件调度系统**: 支持事件优先级、时间调度和自动排序
- **灵活的时间管理**: 仿真时间控制、暂停/恢复、时间统计
- **网络事件支持**: 内置数据包传输、链路状态、路由更新等网络事件
- **可扩展的事件处理**: 支持自定义事件处理器和回调函数
- **详细的统计信息**: 实时监控仿真状态和性能指标
- **无外部依赖**: 纯Python实现，不需要任何第三方库

## 核心组件

### 1. 事件系统 (`event.py`)
- `Event`: 基础事件类
- `NetworkEvent`: 网络事件扩展类
- `EventType`: 事件类型枚举
- `EventPriority`: 事件优先级枚举

### 2. 事件调度器 (`scheduler.py`)
- 基于堆的优先队列
- 事件调度、取消、批量操作
- 事件处理器注册和管理
- 详细的调度统计

### 3. 时间管理器 (`time_manager.py`)
- 仿真时间推进控制
- 暂停/恢复功能
- 时间检查点和统计
- 仿真速度监控

### 4. 事件处理器 (`event_handler.py`)
- 默认事件处理逻辑
- 自定义处理器注册
- 处理统计和错误管理

### 5. 仿真引擎 (`simulation_engine.py`)
- 整合所有组件的主控制器
- 仿真生命周期管理
- 配置管理和回调支持
- 完整的统计信息

### 6. 网络事件 (`network_events.py`)
- 预定义的网络事件类型
- 事件工厂方法
- 网络特定的属性和方法

## 快速开始

### 基本使用

```python
from engine import SimulationEngine, EventType
from engine.network_events import create_packet_arrival

# 创建仿真引擎
engine = SimulationEngine(max_simulation_time=10.0)

# 调度数据包到达事件
packet_event = create_packet_arrival(
    event_id="packet_1",
    scheduled_time=1.0,
    source="node_A",
    destination="node_B",
    packet_id="packet_001",
    packet_size=1024,
    protocol="TCP"
)
engine.schedule_event(packet_event)

# 运行仿真
success = engine.run()
if success:
    print("仿真完成！")
    stats = engine.get_statistics()
    print(f"处理事件数: {stats['simulation']['total_events_processed']}")
```

### 自定义事件处理器

```python
def custom_packet_handler(event):
    print(f"收到数据包 {event.packet_id}，大小: {event.packet_size} bytes")
    # 自定义处理逻辑...
    return True

# 注册自定义处理器
engine.register_event_handler(EventType.PACKET_ARRIVAL, custom_packet_handler)
```

### 网络拓扑仿真

```python
from engine.network_events import create_link_up, create_link_down

# 创建链路状态变化事件
link_down = create_link_down(
    event_id="link_failure",
    scheduled_time=5.0,
    link_id="link_AB",
    node_a="router_A",
    node_b="router_B"
)
engine.schedule_event(link_down)

# 链路恢复
link_up = create_link_up(
    event_id="link_recovery",
    scheduled_time=8.0,
    link_id="link_AB", 
    node_a="router_A",
    node_b="router_B"
)
engine.schedule_event(link_up)
```

## 支持的事件类型

- `PACKET_ARRIVAL`: 数据包到达
- `PACKET_DEPARTURE`: 数据包发送
- `PACKET_DROP`: 数据包丢弃
- `LINK_UP`: 链路上线
- `LINK_DOWN`: 链路下线
- `ROUTE_UPDATE`: 路由更新
- `ROUTE_DISCOVERY`: 路由发现
- `TRANSMISSION_START`: 传输开始
- `TRANSMISSION_END`: 传输结束
- `TIMEOUT`: 超时事件
- `PERIODIC_UPDATE`: 周期性更新
- `SIMULATION_END`: 仿真结束

## 高级功能

### 仿真控制

```python
# 暂停仿真
engine.pause()

# 恢复仿真
engine.resume()

# 停止仿真
engine.stop()

# 重置引擎
engine.reset()
```

### 配置选项

```python
# 队列为空时自动停止
engine.set_config('auto_stop_on_empty_queue', True)

# 启用事件日志记录
engine.set_config('log_events', True)
engine.set_config('event_log_file', 'simulation.log')

# 实时同步（仿真时间与真实时间同步）
engine.set_config('enable_real_time_sync', True)
engine.set_config('real_time_factor', 1.0)
```

### 回调函数

```python
def on_simulation_start():
    print("仿真开始")

def on_event_processed(event):
    print(f"处理事件: {event.event_id}")

# 添加回调
engine.add_callback('on_start', on_simulation_start)
engine.add_callback('on_event_processed', on_event_processed)
```

### 统计信息

```python
# 获取完整统计
stats = engine.get_statistics()

print(f"仿真状态: {stats['current_state']}")
print(f"当前时间: {stats['current_time']}")
print(f"队列大小: {stats['queue_size']}")
print(f"处理事件数: {stats['simulation']['total_events_processed']}")
print(f"调度器统计: {stats['scheduler']}")
print(f"时间管理统计: {stats['time_manager']}")
```

## 示例程序

查看 `examples.py` 文件中的完整示例：

1. **简单数据包仿真**: 基本的数据包传输模拟
2. **网络拓扑仿真**: 链路状态变化和路由更新
3. **自定义处理器**: 演示如何实现自定义事件处理逻辑

运行示例：

```python
from engine.examples import run_all_examples
run_all_examples()
```

## 性能特点

- **高效的事件调度**: 使用堆数据结构，O(log n)的插入和删除
- **内存优化**: 智能的事件清理和统计信息管理
- **可扩展性**: 支持大规模事件队列（默认10,000个事件）
- **实时监控**: 详细的性能统计和状态监控

## 扩展开发

### 自定义事件类型

```python
from engine.event import NetworkEvent, EventType

# 扩展事件类型枚举
class CustomEventType(Enum):
    CUSTOM_EVENT = "custom_event"

# 创建自定义事件类
class CustomEvent(NetworkEvent):
    def __init__(self, event_id, scheduled_time, custom_data):
        super().__init__(
            event_id=event_id,
            event_type=CustomEventType.CUSTOM_EVENT,
            scheduled_time=scheduled_time,
            data={'custom_data': custom_data}
        )
```

### 自定义处理器

```python
class CustomEventHandler:
    def __init__(self):
        self.custom_stats = {}
    
    def handle_custom_event(self, event):
        # 自定义处理逻辑
        custom_data = event.get_data('custom_data')
        # 处理custom_data...
        return True

# 注册自定义处理器
handler = CustomEventHandler()
engine.register_event_handler(
    CustomEventType.CUSTOM_EVENT, 
    handler.handle_custom_event
)
```

## 许可证

本项目为SDT卫星数字孪生系统的一部分，版权归SDT开发团队所有。 