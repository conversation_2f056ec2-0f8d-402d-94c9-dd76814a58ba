#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TLE解析器
负责将ASCII格式的TLE文件解析为TLE对象列表
"""

import re
from typing import List, Optional
from .tle import TLE


class TLEParseError(Exception):
    """TLE解析错误"""
    pass


class TLEParser:
    """
    TLE解析器类
    按照设计文档要求实现TLE解析功能
    """
    
    def __init__(self):
        """初始化解析器"""
        pass
    
    def parse(self, tle_string: str) -> List[TLE]:
        """
        解析TLE字符串为TLE对象列表
        
        Args:
            tle_string: 包含一个或多个TLE条目的完整字符串
            
        Returns:
            TLE对象列表
            
        Raises:
            TLEParseError: 解析错误时抛出
        """
        try:
            # 按行分割字符串
            lines = [line.strip() for line in tle_string.strip().split('\n') if line.strip()]
            
            if not lines:
                raise TLEParseError("输入字符串为空")
            
            tle_objects = []
            i = 0
            
            while i < len(lines):
                # 检查是否为3行格式（包含卫星名称）
                if (i + 2 < len(lines) and 
                    not lines[i].startswith('1 ') and 
                    not lines[i].startswith('2 ') and
                    lines[i + 1].startswith('1 ') and 
                    lines[i + 2].startswith('2 ')):
                    # 3行格式
                    name = lines[i]
                    line1 = lines[i + 1]
                    line2 = lines[i + 2]
                    i += 3
                elif (i + 1 < len(lines) and 
                      lines[i].startswith('1 ') and 
                      lines[i + 1].startswith('2 ')):
                    # 2行格式
                    name = None
                    line1 = lines[i]
                    line2 = lines[i + 1]
                    i += 2
                else:
                    raise TLEParseError(f"无法识别第{i+1}行的TLE格式: {lines[i]}")
                
                # 解析TLE条目
                tle_obj = self._parse_lines(line1, line2, name)
                tle_objects.append(tle_obj)
            
            return tle_objects
            
        except Exception as e:
            if isinstance(e, TLEParseError):
                raise
            else:
                raise TLEParseError(f"TLE解析失败: {str(e)}")
    
    def _parse_lines(self, line1: str, line2: str, name: Optional[str] = None) -> TLE:
        """
        解析TLE的两行数据
        
        Args:
            line1: TLE第一行
            line2: TLE第二行  
            name: 卫星名称（可选）
            
        Returns:
            TLE对象
            
        Raises:
            TLEParseError: 解析错误时抛出
        """
        try:
            # 校验和验证
            if not self._validate_checksum(line1):
                raise TLEParseError(f"第一行校验和错误: {line1}")
            
            if not self._validate_checksum(line2):
                raise TLEParseError(f"第二行校验和错误: {line2}")
            
            # 解析第一行
            if len(line1) < 69:
                raise TLEParseError(f"第一行长度不足: {len(line1)} < 69")
            
            satellite_number = int(line1[2:7])
            classification = line1[7]
            launch_year = int(line1[9:11])
            launch_number = int(line1[11:14])
            launch_piece = line1[14:17].strip()
            epoch_year = int(line1[18:20])
            epoch_day = float(line1[20:32])
            
            # 处理平运动导数（特殊格式）
            mean_motion_dot_str = line1[33:43].strip()
            mean_motion_dot = float(mean_motion_dot_str) * 2.0 if mean_motion_dot_str else 0.0
            
            # 处理平运动二阶导数（科学计数法格式）
            mean_motion_ddot_str = line1[44:52].strip()
            if mean_motion_ddot_str:
                # 格式如 "12345-3" 表示 0.12345e-3
                if '-' in mean_motion_ddot_str or '+' in mean_motion_ddot_str:
                    mantissa = mean_motion_ddot_str[:-2]
                    exponent = mean_motion_ddot_str[-2:]
                    mean_motion_ddot = float(f"0.{mantissa}e{exponent}")
                else:
                    mean_motion_ddot = float(mean_motion_ddot_str)
            else:
                mean_motion_ddot = 0.0
            
            # 处理B*阻力项（科学计数法格式）
            bstar_str = line1[53:61].strip()
            if bstar_str:
                if '-' in bstar_str or '+' in bstar_str:
                    mantissa = bstar_str[:-2]
                    exponent = bstar_str[-2:]
                    bstar_drag = float(f"0.{mantissa}e{exponent}")
                else:
                    bstar_drag = float(bstar_str)
            else:
                bstar_drag = 0.0
            
            ephemeris_type = int(line1[62]) if line1[62].isdigit() else 0
            element_set_number = int(line1[64:68])
            
            # 解析第二行
            if len(line2) < 69:
                raise TLEParseError(f"第二行长度不足: {len(line2)} < 69")
            
            inclination = float(line2[8:16])
            raan = float(line2[17:25])
            
            # 偏心率（前面隐含小数点）
            eccentricity_str = line2[26:33]
            eccentricity = float(f"0.{eccentricity_str}")
            
            arg_of_perigee = float(line2[34:42])
            mean_anomaly = float(line2[43:51])
            mean_motion = float(line2[52:63])
            rev_at_epoch = int(line2[63:68])
            
            # 处理年份（57规则：00-56为20xx，57-99为19xx）
            if epoch_year <= 56:
                epoch_year += 2000
            else:
                epoch_year += 1900
            
            # 创建TLE对象
            return TLE(
                name=name or f"SATELLITE {satellite_number}",
                satellite_number=satellite_number,
                classification=classification,
                launch_year=launch_year,
                launch_number=launch_number,
                launch_piece=launch_piece,
                epoch_year=epoch_year,
                epoch_day=epoch_day,
                mean_motion_dot=mean_motion_dot,
                mean_motion_ddot=mean_motion_ddot,
                bstar_drag=bstar_drag,
                ephemeris_type=ephemeris_type,
                element_set_number=element_set_number,
                inclination=inclination,
                raan=raan,
                eccentricity=eccentricity,
                arg_of_perigee=arg_of_perigee,
                mean_anomaly=mean_anomaly,
                mean_motion=mean_motion,
                rev_at_epoch=rev_at_epoch
            )
            
        except ValueError as e:
            raise TLEParseError(f"数值解析错误: {str(e)}")
        except Exception as e:
            raise TLEParseError(f"行解析失败: {str(e)}")
    
    def _validate_checksum(self, line: str) -> bool:
        """
        验证TLE行的校验和
        
        Args:
            line: TLE行字符串
            
        Returns:
            校验和是否正确
        """
        if len(line) < 69:
            return False
        
        # 计算校验和
        checksum = 0
        for char in line[:-1]:  # 除最后一个字符外的所有字符
            if char.isdigit():
                checksum += int(char)
            elif char == '-':
                checksum += 1
        
        # 校验和模10
        calculated_checksum = checksum % 10
        expected_checksum = int(line[-1])
        
        return calculated_checksum == expected_checksum
