import pytest
import time
from engine.event import Event, EventType, EventPriority, NetworkEvent

def test_event_creation():
    """Test basic Event creation and properties."""
    event_id = "test_event_001"
    event_type = EventType.TIMEOUT
    scheduled_time = time.time() + 10
    
    event = Event(event_id, event_type, scheduled_time)
    
    assert event.event_id == event_id
    assert event.event_type == event_type
    assert event.scheduled_time == scheduled_time
    assert event.priority == EventPriority.NORMAL
    assert not event.is_cancelled()
    assert not event.is_processed()

def test_event_comparison():
    """Test event comparison for priority queue sorting."""
    t = time.time()
    event1 = Event("e1", EventType.TIMEOUT, t + 10, EventPriority.NORMAL)
    event2 = Event("e2", EventType.TIMEOUT, t + 5, EventPriority.NORMAL)
    event3 = Event("e3", EventType.TIMEOUT, t + 5, EventPriority.CRITICAL)
    
    assert event2 < event1
    assert event3 < event2 # Same time, higher priority
    assert not (event1 < event2)

def test_network_event_creation():
    """Test creation of the specialized NetworkEvent."""
    event_id = "net_event_001"
    event_type = EventType.PACKET_ARRIVAL
    scheduled_time = time.time() + 20
    source = "node_A"
    dest = "node_B"
    
    net_event = NetworkEvent(
        event_id,
        event_type,
        scheduled_time,
        source_node=source,
        destination_node=dest,
        packet_id="pkt123"
    )
    
    assert net_event.event_id == event_id
    assert net_event.source_node == source
    assert net_event.destination_node == dest
    assert net_event.packet_id == "pkt123"
    assert "src=node_A" in str(net_event) 