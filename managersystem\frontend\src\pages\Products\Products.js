import React from 'react';
import { Card, Typography, Empty, Button } from 'antd';
import { AppstoreOutlined, PlusOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Products = () => {
  return (
    <div className="products-container">
      <div className="page-header">
        <Title level={2}>产品库</Title>
      </div>

      <Card>
        <Empty
          image={<AppstoreOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
          description="产品管理功能开发中..."
        >
          <Button type="primary" icon={<PlusOutlined />}>
            创建产品
          </Button>
        </Empty>
      </Card>
    </div>
  );
};

export default Products;
