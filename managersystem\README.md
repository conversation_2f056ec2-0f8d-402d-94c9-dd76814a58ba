# SDT管理系统

基于Flask + React + Redis的管理系统，用于缺陷问题管理、文档库管理、产品库管理。

## 快速开始

### 开发环境要求
- Python 3.8+
- Node.js 16+
- Redis 6.0+

### 安装和运行

1. **后端启动**
```bash
cd backend
pip install -r requirements.txt
python run.py
```

2. **前端启动**
```bash
cd frontend
npm install
npm start
```

3. **Redis启动**
```bash
redis-server
```

### Docker部署

```bash
docker-compose up -d
```

## 项目结构

```
managersystem/
├── backend/           # Flask后端
├── frontend/          # React前端
├── storage/           # 文件存储
├── docs/             # 项目文档
└── docker-compose.yml # Docker配置
```

## 功能模块

- **缺陷问题管理**: Bug跟踪、任务分配、状态管理
- **文档库管理**: 文档上传、分类、版本管理
- **产品库管理**: 产品版本、发布管理
- **用户权限管理**: 用户认证、角色权限

## 技术栈

- **后端**: Flask, Redis, JWT
- **前端**: React, Ant Design, Redux
- **部署**: Docker, Nginx

## 开发指南

详细的开发指南请参考：
- [系统设计方案](SYSTEM_DESIGN.md)
- [实现计划](IMPLEMENTATION_PLAN.md)

## 许可证

仅供学习和研究使用。
