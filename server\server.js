#!/usr/bin/env node
/**
 * SDT卫星数字孪生系统Node.js服务器
 * 提供静态文件服务和WebSocket支持
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');
const http = require('http');
const WebSocket = require('ws');
const axios = require('axios');
require('dotenv').config();

// 创建Express应用
const app = express();
const server = http.createServer(app);

// 配置
const PORT = process.env.PORT || 3000;
const FLASK_API_URL = process.env.FLASK_API_URL || 'http://localhost:5000';
const FRONTEND_DIR = path.join(__dirname, '..', 'frontend');

// 中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "ws:", "wss:"]
        }
    }
}));

app.use(compression());
app.use(morgan('combined'));
app.use(cors());
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(FRONTEND_DIR));

// API代理路由
app.use('/api', async (req, res) => {
    try {
        const response = await axios({
            method: req.method,
            url: `${FLASK_API_URL}${req.originalUrl}`,
            data: req.body,
            headers: {
                'Content-Type': 'application/json',
                ...req.headers
            },
            timeout: 30000
        });
        
        res.status(response.status).json(response.data);
    } catch (error) {
        console.error('API代理错误:', error.message);
        
        if (error.response) {
            res.status(error.response.status).json(error.response.data);
        } else if (error.code === 'ECONNREFUSED') {
            res.status(503).json({
                error: 'Flask API服务不可用',
                message: '请确保Flask服务器正在运行'
            });
        } else {
            res.status(500).json({
                error: '内部服务器错误',
                message: error.message
            });
        }
    }
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(FRONTEND_DIR, 'index.html'));
});

// 健康检查
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        services: {
            node: 'running',
            flask: 'checking...'
        }
    });
});

// WebSocket服务器
const wss = new WebSocket.Server({ server });

// WebSocket连接管理
const clients = new Set();

wss.on('connection', (ws, req) => {
    console.log('新的WebSocket连接:', req.socket.remoteAddress);
    clients.add(ws);
    
    // 发送欢迎消息
    ws.send(JSON.stringify({
        type: 'welcome',
        message: '欢迎连接SDT卫星数字孪生系统',
        timestamp: new Date().toISOString()
    }));
    
    // 处理消息
    ws.on('message', async (message) => {
        try {
            const data = JSON.parse(message);
            console.log('收到WebSocket消息:', data.type);
            
            switch (data.type) {
                case 'ping':
                    ws.send(JSON.stringify({
                        type: 'pong',
                        timestamp: new Date().toISOString()
                    }));
                    break;
                    
                case 'subscribe':
                    // 订阅实时数据
                    ws.subscriptions = data.channels || [];
                    ws.send(JSON.stringify({
                        type: 'subscribed',
                        channels: ws.subscriptions,
                        timestamp: new Date().toISOString()
                    }));
                    break;
                    
                case 'simulation_request':
                    // 处理仿真请求
                    await handleSimulationRequest(ws, data);
                    break;
                    
                default:
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: `未知消息类型: ${data.type}`,
                        timestamp: new Date().toISOString()
                    }));
            }
        } catch (error) {
            console.error('WebSocket消息处理错误:', error);
            ws.send(JSON.stringify({
                type: 'error',
                message: '消息处理失败',
                timestamp: new Date().toISOString()
            }));
        }
    });
    
    // 连接关闭
    ws.on('close', () => {
        console.log('WebSocket连接关闭');
        clients.delete(ws);
    });
    
    // 错误处理
    ws.on('error', (error) => {
        console.error('WebSocket错误:', error);
        clients.delete(ws);
    });
});

// 处理仿真请求
async function handleSimulationRequest(ws, data) {
    try {
        ws.send(JSON.stringify({
            type: 'simulation_started',
            request_id: data.request_id,
            timestamp: new Date().toISOString()
        }));
        
        // 调用Flask API
        const response = await axios.post(`${FLASK_API_URL}/api/v1/simulate/orbit`, data.payload);
        
        // 发送结果
        ws.send(JSON.stringify({
            type: 'simulation_completed',
            request_id: data.request_id,
            result: response.data,
            timestamp: new Date().toISOString()
        }));
        
    } catch (error) {
        console.error('仿真请求处理错误:', error);
        ws.send(JSON.stringify({
            type: 'simulation_error',
            request_id: data.request_id,
            error: error.message,
            timestamp: new Date().toISOString()
        }));
    }
}

// 广播消息到所有客户端
function broadcast(message, channel = null) {
    const messageStr = JSON.stringify(message);
    
    clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            // 如果指定了频道，只发送给订阅了该频道的客户端
            if (!channel || (client.subscriptions && client.subscriptions.includes(channel))) {
                client.send(messageStr);
            }
        }
    });
}

// 定期广播系统状态
setInterval(async () => {
    try {
        // 检查Flask API状态
        const flaskStatus = await checkFlaskStatus();
        
        broadcast({
            type: 'system_status',
            status: {
                node: 'running',
                flask: flaskStatus,
                clients: clients.size,
                timestamp: new Date().toISOString()
            }
        }, 'system');
        
    } catch (error) {
        console.error('系统状态广播错误:', error);
    }
}, 30000); // 每30秒广播一次

// 检查Flask API状态
async function checkFlaskStatus() {
    try {
        const response = await axios.get(`${FLASK_API_URL}/api/v1/health`, { timeout: 5000 });
        return response.status === 200 ? 'running' : 'error';
    } catch (error) {
        return 'offline';
    }
}

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('服务器错误:', error);
    res.status(500).json({
        error: '内部服务器错误',
        message: process.env.NODE_ENV === 'development' ? error.message : '服务器遇到错误'
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        error: '页面未找到',
        path: req.path
    });
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');
    
    // 关闭WebSocket服务器
    wss.close(() => {
        console.log('WebSocket服务器已关闭');
    });
    
    // 关闭HTTP服务器
    server.close(() => {
        console.log('HTTP服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('收到SIGINT信号，正在关闭服务器...');
    
    // 关闭WebSocket服务器
    wss.close(() => {
        console.log('WebSocket服务器已关闭');
    });
    
    // 关闭HTTP服务器
    server.close(() => {
        console.log('HTTP服务器已关闭');
        process.exit(0);
    });
});

// 启动服务器
server.listen(PORT, () => {
    console.log(`SDT卫星数字孪生系统服务器启动成功`);
    console.log(`HTTP服务器: http://localhost:${PORT}`);
    console.log(`WebSocket服务器: ws://localhost:${PORT}`);
    console.log(`Flask API代理: ${FLASK_API_URL}`);
    console.log(`静态文件目录: ${FRONTEND_DIR}`);
});

// 导出应用（用于测试）
module.exports = { app, server, wss };
