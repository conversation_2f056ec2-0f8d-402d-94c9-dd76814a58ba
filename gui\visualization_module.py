#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块
支持3D/2D显示和用户交互界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import math
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from utils.logger import setup_logger


class ConstellationDisplay:
    """星座显示组件"""
    
    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.logger = setup_logger("ConstellationDisplay")
        
        # 显示模式
        self.display_mode = "3D"  # "3D" or "2D"
        self.map_style = "satellite"  # "satellite", "terrain", "simple"
        
        # 卫星数据
        self.satellites = []
        self.ground_stations = []
        self.inter_satellite_links = []
        
        # 创建UI组件
        self._create_widgets()
        
    def _create_widgets(self):
        """创建UI组件"""
        # 控制面板
        control_frame = ttk.Frame(self.parent_frame)
        control_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        # 显示模式切换
        ttk.Label(control_frame, text="显示模式:").pack(side=tk.LEFT, padx=5)
        self.mode_var = tk.StringVar(value=self.display_mode)
        mode_combo = ttk.Combobox(control_frame, textvariable=self.mode_var, 
                                values=["3D", "2D"], state="readonly", width=10)
        mode_combo.pack(side=tk.LEFT, padx=5)
        mode_combo.bind("<<ComboboxSelected>>", self._on_mode_change)
        
        # 地图样式切换
        ttk.Label(control_frame, text="地图样式:").pack(side=tk.LEFT, padx=5)
        self.style_var = tk.StringVar(value=self.map_style)
        style_combo = ttk.Combobox(control_frame, textvariable=self.style_var,
                                 values=["satellite", "terrain", "simple"], 
                                 state="readonly", width=10)
        style_combo.pack(side=tk.LEFT, padx=5)
        style_combo.bind("<<ComboboxSelected>>", self._on_style_change)
        
        # 显示画布
        self.canvas_frame = ttk.Frame(self.parent_frame)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.canvas = tk.Canvas(self.canvas_frame, bg="black", width=800, height=600)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self._on_canvas_click)
        self.canvas.bind("<B1-Motion>", self._on_canvas_drag)
        self.canvas.bind("<MouseWheel>", self._on_canvas_zoom)
        
        # 初始化显示
        self._init_display()
        
    def _init_display(self):
        """初始化显示"""
        self._draw_earth()
        self._draw_grid()
        
    def _draw_earth(self):
        """绘制地球"""
        canvas_width = self.canvas.winfo_width() or 800
        canvas_height = self.canvas.winfo_height() or 600
        
        center_x = canvas_width // 2
        center_y = canvas_height // 2
        earth_radius = min(canvas_width, canvas_height) // 4
        
        # 绘制地球
        self.canvas.create_oval(
            center_x - earth_radius, center_y - earth_radius,
            center_x + earth_radius, center_y + earth_radius,
            fill="blue", outline="white", width=2, tags="earth"
        )
        
        # 添加经纬线
        for i in range(-90, 91, 30):
            y = center_y + (i / 90) * earth_radius * 0.8
            self.canvas.create_line(
                center_x - earth_radius, y,
                center_x + earth_radius, y,
                fill="gray", width=1, tags="grid"
            )
        
        for i in range(-180, 181, 30):
            x = center_x + (i / 180) * earth_radius * 0.8
            self.canvas.create_line(
                x, center_y - earth_radius,
                x, center_y + earth_radius,
                fill="gray", width=1, tags="grid"
            )
    
    def _draw_grid(self):
        """绘制网格"""
        # 网格已在_draw_earth中绘制
        pass
    
    def update_satellites(self, satellites: List[Dict]):
        """更新卫星显示"""
        self.satellites = satellites
        self._redraw_satellites()
        
    def update_links(self, links: List[tuple]):
        """更新链路显示"""
        self.inter_satellite_links = links
        self._redraw_links()
        
    def _redraw_satellites(self):
        """重绘卫星"""
        # 清除旧的卫星
        self.canvas.delete("satellite")
        
        canvas_width = self.canvas.winfo_width() or 800
        canvas_height = self.canvas.winfo_height() or 600
        center_x = canvas_width // 2
        center_y = canvas_height // 2
        
        for sat in self.satellites:
            # 简化的2D投影
            pos = sat.get('position', (0, 0, 0))
            x = center_x + pos[0] / 20000 * (canvas_width // 4)
            y = center_y + pos[1] / 20000 * (canvas_height // 4)
            
            # 绘制卫星
            self.canvas.create_oval(
                x - 3, y - 3, x + 3, y + 3,
                fill="yellow", outline="white", tags="satellite"
            )
            
            # 添加标签
            self.canvas.create_text(
                x, y - 10, text=f"SAT-{sat['id']}", 
                fill="white", font=("Arial", 8), tags="satellite"
            )
    
    def _redraw_links(self):
        """重绘链路"""
        # 清除旧的链路
        self.canvas.delete("link")
        
        canvas_width = self.canvas.winfo_width() or 800
        canvas_height = self.canvas.winfo_height() or 600
        center_x = canvas_width // 2
        center_y = canvas_height // 2
        
        for source_id, dest_id in self.inter_satellite_links:
            source_sat = next((s for s in self.satellites if s['id'] == source_id), None)
            dest_sat = next((s for s in self.satellites if s['id'] == dest_id), None)
            
            if source_sat and dest_sat:
                # 计算坐标
                pos1 = source_sat.get('position', (0, 0, 0))
                pos2 = dest_sat.get('position', (0, 0, 0))
                
                x1 = center_x + pos1[0] / 20000 * (canvas_width // 4)
                y1 = center_y + pos1[1] / 20000 * (canvas_height // 4)
                x2 = center_x + pos2[0] / 20000 * (canvas_width // 4)
                y2 = center_y + pos2[1] / 20000 * (canvas_height // 4)
                
                # 绘制链路
                self.canvas.create_line(
                    x1, y1, x2, y2,
                    fill="white", width=1, tags="link"
                )
    
    def _on_mode_change(self, event):
        """显示模式改变"""
        self.display_mode = self.mode_var.get()
        self.logger.info(f"切换显示模式: {self.display_mode}")
        self._refresh_display()
    
    def _on_style_change(self, event):
        """地图样式改变"""
        self.map_style = self.style_var.get()
        self.logger.info(f"切换地图样式: {self.map_style}")
        self._refresh_display()
    
    def _on_canvas_click(self, event):
        """画布点击事件"""
        pass
    
    def _on_canvas_drag(self, event):
        """画布拖拽事件"""
        pass
    
    def _on_canvas_zoom(self, event):
        """画布缩放事件"""
        pass
    
    def _refresh_display(self):
        """刷新显示"""
        self.canvas.delete("all")
        self._init_display()
        self._redraw_satellites()
        self._redraw_links()


class SimulationControl:
    """仿真控制组件"""
    
    def __init__(self, parent_frame, simulator):
        self.parent_frame = parent_frame
        self.simulator = simulator
        self.logger = setup_logger("SimulationControl")
        
        # 仿真参数
        self.simulation_speed = 1.0
        self.simulation_running = False
        
        # 创建UI组件
        self._create_widgets()
        
    def _create_widgets(self):
        """创建UI组件"""
        # 控制按钮
        button_frame = ttk.Frame(self.parent_frame)
        button_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        self.start_button = ttk.Button(button_frame, text="开始仿真", 
                                     command=self._start_simulation)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止仿真", 
                                    command=self._stop_simulation, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.reset_button = ttk.Button(button_frame, text="重置", 
                                     command=self._reset_simulation)
        self.reset_button.pack(side=tk.LEFT, padx=5)
        
        # 速度控制
        speed_frame = ttk.Frame(self.parent_frame)
        speed_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        ttk.Label(speed_frame, text="仿真速度:").pack(side=tk.LEFT, padx=5)
        
        self.speed_var = tk.DoubleVar(value=self.simulation_speed)
        speed_scale = ttk.Scale(speed_frame, from_=0.1, to=10.0, 
                              variable=self.speed_var, orient=tk.HORIZONTAL, length=200)
        speed_scale.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        speed_scale.bind("<Motion>", self._on_speed_change)
        
        self.speed_label = ttk.Label(speed_frame, text="1.0x")
        self.speed_label.pack(side=tk.LEFT, padx=5)
        
        # 仿真信息
        info_frame = ttk.LabelFrame(self.parent_frame, text="仿真信息")
        info_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.info_text = tk.Text(info_frame, height=8, width=40)
        scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def _start_simulation(self):
        """开始仿真"""
        self.simulation_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        self.logger.info("开始仿真")
        self._update_info("仿真已开始\n")
        
    def _stop_simulation(self):
        """停止仿真"""
        self.simulation_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        self.logger.info("停止仿真")
        self._update_info("仿真已停止\n")
        
    def _reset_simulation(self):
        """重置仿真"""
        self.simulation_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        self.logger.info("重置仿真")
        self.info_text.delete(1.0, tk.END)
        self._update_info("仿真已重置\n")
        
    def _on_speed_change(self, event):
        """速度改变"""
        speed = self.speed_var.get()
        self.simulation_speed = speed
        self.speed_label.config(text=f"{speed:.1f}x")
        
    def _update_info(self, message: str):
        """更新信息显示"""
        self.info_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
        self.info_text.see(tk.END)


class StatusPanel:
    """状态面板"""
    
    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.logger = setup_logger("StatusPanel")
        
        # 状态数据
        self.system_status = {}
        
        # 创建UI组件
        self._create_widgets()
        
    def _create_widgets(self):
        """创建UI组件"""
        # 系统状态
        system_frame = ttk.LabelFrame(self.parent_frame, text="系统状态")
        system_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        self.status_labels = {}
        status_items = [
            ("卫星数量", "satellite_count"),
            ("活跃链路", "active_links"),
            ("仿真时间", "simulation_time"),
            ("CPU使用率", "cpu_usage"),
            ("内存使用率", "memory_usage")
        ]
        
        for i, (label, key) in enumerate(status_items):
            row = i // 2
            col = i % 2
            
            ttk.Label(system_frame, text=f"{label}:").grid(row=row, column=col*2, 
                                                         sticky=tk.W, padx=5, pady=2)
            value_label = ttk.Label(system_frame, text="--")
            value_label.grid(row=row, column=col*2+1, sticky=tk.W, padx=5, pady=2)
            self.status_labels[key] = value_label
        
        # 网络统计
        network_frame = ttk.LabelFrame(self.parent_frame, text="网络统计")
        network_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        self.network_labels = {}
        network_items = [
            ("数据包发送", "packets_sent"),
            ("数据包接收", "packets_received"),
            ("数据包丢失", "packets_lost"),
            ("平均延迟", "avg_delay")
        ]
        
        for i, (label, key) in enumerate(network_items):
            row = i // 2
            col = i % 2
            
            ttk.Label(network_frame, text=f"{label}:").grid(row=row, column=col*2, 
                                                          sticky=tk.W, padx=5, pady=2)
            value_label = ttk.Label(network_frame, text="--")
            value_label.grid(row=row, column=col*2+1, sticky=tk.W, padx=5, pady=2)
            self.network_labels[key] = value_label
    
    def update_status(self, status_data: Dict):
        """更新状态显示"""
        self.system_status = status_data
        
        # 更新系统状态
        for key, label in self.status_labels.items():
            value = status_data.get(key, "--")
            if isinstance(value, float):
                if key in ["cpu_usage", "memory_usage"]:
                    label.config(text=f"{value:.1f}%")
                else:
                    label.config(text=f"{value:.2f}")
            else:
                label.config(text=str(value))
        
        # 更新网络统计
        for key, label in self.network_labels.items():
            value = status_data.get(key, "--")
            if isinstance(value, (int, float)):
                label.config(text=f"{value:,}")
            else:
                label.config(text=str(value))


class VisualizationModule:
    """可视化模块主类"""
    
    def __init__(self, config, topology_controller, simulator):
        self.config = config
        self.topology_controller = topology_controller
        self.simulator = simulator
        
        self.logger = setup_logger("VisualizationModule")
        
        # 主窗口
        self.root = None
        self.running = False
        
        # UI组件
        self.constellation_display = None
        self.simulation_control = None
        self.status_panel = None
        
        # 更新线程
        self.update_thread = None
        
        self.logger.info("可视化模块初始化完成")
    
    def start(self):
        """启动可视化界面"""
        if self.running:
            return
        
        self.running = True
        self.logger.info("启动可视化界面...")
        
        # 创建主窗口
        self._create_main_window()
        
        # 启动更新线程
        self.update_thread = threading.Thread(target=self._update_loop)
        self.update_thread.daemon = True
        self.update_thread.start()
        
        # 运行主循环
        self.root.mainloop()
    
    def stop(self):
        """停止可视化界面"""
        self.running = False
        if self.root:
            self.root.quit()
        self.logger.info("可视化界面已停止")
    
    def _create_main_window(self):
        """创建主窗口"""
        self.root = tk.Tk()
        self.root.title("卫星数字孪生系统 (SDT) - 可视化界面")
        self.root.geometry("1200x800")
        
        # 创建菜单栏
        self._create_menu()
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板 - 星座显示
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.constellation_display = ConstellationDisplay(left_frame)
        
        # 右侧面板 - 控制和状态
        right_frame = ttk.Frame(main_frame, width=300)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)
        right_frame.pack_propagate(False)
        
        # 仿真控制
        self.simulation_control = SimulationControl(right_frame, self.simulator)
        
        # 状态面板
        self.status_panel = StatusPanel(right_frame)
        
        # 状态栏
        self.status_bar = ttk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def _create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开星历文件", command=self._open_ephemeris)
        file_menu.add_command(label="保存配置", command=self._save_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._quit_application)
        
        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)
        view_menu.add_command(label="3D视图", command=lambda: self._set_view_mode("3D"))
        view_menu.add_command(label="2D视图", command=lambda: self._set_view_mode("2D"))
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="生成星座", command=self._generate_constellation)
        tools_menu.add_command(label="导出数据", command=self._export_data)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _update_loop(self):
        """更新循环"""
        while self.running:
            try:
                # 获取系统状态
                status = self._get_system_status()
                
                # 更新状态面板
                if self.status_panel:
                    self.status_panel.update_status(status)
                
                # 获取拓扑数据
                topology = self.topology_controller.generate_network_topology()
                
                # 更新星座显示
                if self.constellation_display:
                    satellites = topology.get('satellites', [])
                    links = topology.get('inter_satellite_links', [])
                    
                    self.constellation_display.update_satellites(satellites)
                    self.constellation_display.update_links(links)
                
                time.sleep(1.0)  # 每秒更新一次
                
            except Exception as e:
                self.logger.error(f"更新循环错误: {e}")
                time.sleep(1.0)
    
    def _get_system_status(self) -> Dict:
        """获取系统状态"""
        try:
            return {
                'satellite_count': self.topology_controller.get_satellite_count(),
                'active_links': self.simulator.get_active_links(),
                'simulation_time': self.simulator.get_simulation_time(),
                'cpu_usage': 45.2,  # 模拟值
                'memory_usage': 62.8,  # 模拟值
                'packets_sent': 12345,
                'packets_received': 12200,
                'packets_lost': 145,
                'avg_delay': 25.6
            }
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {}
    
    def _open_ephemeris(self):
        """打开星历文件"""
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="选择星历文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.logger.info(f"打开星历文件: {filename}")
            self.topology_controller.import_ephemeris_data(filename)
    
    def _save_config(self):
        """保存配置"""
        self.logger.info("保存配置")
        messagebox.showinfo("信息", "配置已保存")
    
    def _quit_application(self):
        """退出应用"""
        self.stop()
    
    def _set_view_mode(self, mode: str):
        """设置视图模式"""
        if self.constellation_display:
            self.constellation_display.display_mode = mode
            self.constellation_display.mode_var.set(mode)
            self.constellation_display._refresh_display()
        self.logger.info(f"切换到 {mode} 视图")
    
    def _generate_constellation(self):
        """生成星座"""
        # 弹出对话框选择星座类型
        constellation_types = ["Starlink-like", "OneWeb-like", "Custom-LEO"]
        
        dialog = tk.Toplevel(self.root)
        dialog.title("生成星座")
        dialog.geometry("300x150")
        dialog.resizable(False, False)
        
        ttk.Label(dialog, text="选择星座类型:").pack(pady=10)
        
        constellation_var = tk.StringVar(value=constellation_types[0])
        combo = ttk.Combobox(dialog, textvariable=constellation_var, 
                           values=constellation_types, state="readonly")
        combo.pack(pady=5)
        
        def generate():
            selected = constellation_var.get()
            self.topology_controller.load_constellation_config()
            self.topology_controller.generate_constellation(selected)
            self.logger.info(f"生成星座: {selected}")
            dialog.destroy()
            messagebox.showinfo("成功", f"已生成 {selected} 星座")
        
        ttk.Button(dialog, text="生成", command=generate).pack(pady=10)
        ttk.Button(dialog, text="取消", command=dialog.destroy).pack()
    
    def _export_data(self):
        """导出数据"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="导出星历数据",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            exported_file = self.topology_controller.export_ephemeris_data(filename)
            if exported_file:
                messagebox.showinfo("成功", f"数据已导出到: {exported_file}")
            else:
                messagebox.showerror("错误", "导出失败")
    
    def _show_about(self):
        """显示关于对话框"""
        about_text = """
卫星数字孪生系统 (SDT) v1.0

一个高性能的卫星通信网络仿真平台

主要功能:
• 大规模卫星星座仿真
• 实时网络拓扑计算
• 多种路径算法支持
• 3D/2D可视化显示
• 半实物仿真接入

Copyright © 2024
        """
        messagebox.showinfo("关于 SDT", about_text) 