from flask import Flask
from flask_cors import CORS
from flask_jwt_extended import J<PERSON>TManager
from config import config

def create_app(config_name):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    CORS(app)
    jwt = JWTManager(app)
    
    # 注册蓝图
    from app.api import auth_bp, users_bp, issues_bp, documents_bp, products_bp
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(users_bp, url_prefix='/api/users')
    app.register_blueprint(issues_bp, url_prefix='/api/issues')
    app.register_blueprint(documents_bp, url_prefix='/api/documents')
    app.register_blueprint(products_bp, url_prefix='/api/products')
    
    return app
