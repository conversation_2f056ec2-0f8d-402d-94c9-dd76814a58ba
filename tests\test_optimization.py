import pytest
from path.optimization import <PERSON><PERSON>ptim<PERSON>, OptimizationCriteria, OptimizationResult
from path.path_planner import PathResult, PathNode, PathSegment

# A simple mock for PathNode for testing purposes
class MockPathNode:
    def __init__(self, name, x, y, z):
        self.name = name
        self.x = x
        self.y = y
        self.z = z

    def distance_to(self, other):
        return ((self.x - other.x)**2 + (self.y - other.y)**2 + (self.z - other.z)**2)**0.5

@pytest.fixture
def sample_path():
    """Create a sample path for optimization testing."""
    node1 = MockPathNode("A", 0, 0, 0)
    node2 = MockPathNode("B", 1, 0, 0) # Inefficient middle node
    node3 = MockPathNode("C", 2, 0, 0)
    
    nodes = [node1, node2, node3]
    
    seg1 = PathSegment(node1, node2, 1, 1/3e8, 0.9, 0.9e9)
    seg2 = PathSegment(node2, node3, 1, 1/3e8, 0.9, 0.9e9)
    
    segments = [seg1, seg2]
    
    return PathResult(
        nodes=nodes,
        segments=segments,
        total_distance=2,
        total_delay=2/3e8,
        total_cost=seg1.cost + seg2.cost,
        success=True
    )

def test_path_optimizer_basic(sample_path):
    """Test the basic optimization flow."""
    optimizer = PathOptimizer()
    criteria = OptimizationCriteria(minimize_hops=1.0) # Strongly prefer fewer hops
    
    # Mock the internal _construct_path_from_nodes to return a valid path
    # when the optimizer tries to create the A->C path.
    def mock_construct(nodes):
        if len(nodes) == 2 and nodes[0].name == "A" and nodes[1].name == "C":
            node_a, node_c = nodes[0], nodes[1]
            dist = node_a.distance_to(node_c)
            seg = PathSegment(node_a, node_c, dist, dist/3e8, 0.95, 0.95e9)
            return PathResult([node_a, node_c], [seg], dist, dist/3e8, seg.cost, True)
        # Fallback for other cases, though not expected in this test
        return PathResult([], [], 0, 0, 0, False)

    optimizer._construct_path_from_nodes = mock_construct

    result = optimizer.optimize_path(sample_path, criteria)

    assert result.success
    assert result.optimized_path is not None
    assert len(result.optimized_path.nodes) == 2 # Hop B should be removed
    assert result.optimized_path.nodes[0].name == "A"
    assert result.optimized_path.nodes[1].name == "C"
    assert result.improvement_percentage > 0

def test_path_optimizer_no_improvement(sample_path):
    """Test scenario where no optimization is possible."""
    optimizer = PathOptimizer()
    # Criteria that won't benefit from removing the hop
    criteria = OptimizationCriteria(minimize_hops=0.0, minimize_delay=1.0) 

    # The local optimization tries to remove node B.
    # We will make the direct A->C path have a very high delay
    # so it's not chosen.
    def mock_construct(nodes):
        if len(nodes) == 2 and nodes[0].name == "A" and nodes[1].name == "C":
            node_a, node_c = nodes[0], nodes[1]
            dist = node_a.distance_to(node_c)
            # High delay makes this path worse
            seg = PathSegment(node_a, node_c, dist, 999, 0.95, 0.95e9) 
            return PathResult([node_a, node_c], [seg], dist, 999, seg.cost, True)
        return PathResult([], [], 0, 0, 0, False)
    
    optimizer._construct_path_from_nodes = mock_construct

    result = optimizer.optimize_path(sample_path, criteria)

    assert not result.success
    assert len(result.optimized_path.nodes) == 3 # Original path is kept
    assert result.improvement_percentage <= 0 