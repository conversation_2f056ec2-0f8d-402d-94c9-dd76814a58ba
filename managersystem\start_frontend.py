#!/usr/bin/env python3
"""
启动前端开发服务器
"""

import os
import subprocess
import sys
import time
from pathlib import Path

def main():
    """主函数"""
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("错误: frontend目录不存在")
        return
    
    # 检查 node_modules 是否存在
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("依赖未安装，正在安装...")
        try:
            subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
            print("✓ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("✗ 依赖安装失败")
            print("请手动执行: cd frontend && npm install")
            return
    
    print("启动前端开发服务器...")
    print("访问地址: http://localhost:3000")
    print("按 Ctrl+C 停止服务")
    
    try:
        # 启动开发服务器
        subprocess.run(["npm", "start"], cwd=frontend_dir)
    except KeyboardInterrupt:
        print("\n前端服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"启动失败: {e}")
        print("\n请尝试以下步骤:")
        print("1. cd frontend")
        print("2. npm install")
        print("3. npm start")

if __name__ == "__main__":
    main()
