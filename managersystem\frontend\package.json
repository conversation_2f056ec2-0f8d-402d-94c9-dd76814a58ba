{"name": "sdt-manager-frontend", "version": "1.0.0", "description": "SDT管理系统前端", "main": "src/index.js", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "test": "jest", "lint": "eslint src --ext .js,.jsx"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "antd": "^5.9.0", "@reduxjs/toolkit": "^1.9.5", "react-redux": "^8.1.2", "axios": "^1.5.0", "dayjs": "^1.11.9", "@ant-design/icons": "^5.2.6"}, "devDependencies": {"webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0", "@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "style-loader": "^3.3.0", "html-webpack-plugin": "^5.5.0", "process": "^0.11.10", "eslint": "^8.47.0", "eslint-plugin-react": "^7.33.0", "jest": "^29.6.0", "@testing-library/react": "^13.4.0"}}