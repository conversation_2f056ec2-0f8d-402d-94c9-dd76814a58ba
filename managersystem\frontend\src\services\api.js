import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 认证API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  register: (userData) => api.post('/auth/register', userData),
  verify: () => api.get('/auth/verify'),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (profileData) => api.put('/auth/profile', profileData),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
  getSessions: () => api.get('/auth/sessions'),
  revokeAllSessions: () => api.delete('/auth/sessions'),
};

// 问题管理API
export const issueAPI = {
  getList: (params) => api.get('/issues', { params }),
  getById: (id) => api.get(`/issues/${id}`),
  create: (issueData) => api.post('/issues', issueData),
  update: (id, issueData) => api.put(`/issues/${id}`, issueData),
  delete: (id) => api.delete(`/issues/${id}`),
  addComment: (id, commentData) => api.post(`/issues/${id}/comments`, commentData),
  getComments: (id) => api.get(`/issues/${id}/comments`),
  getStats: () => api.get('/issues/stats'),
};

// 文档管理API
export const documentAPI = {
  getList: (params) => api.get('/documents', { params }),
  getById: (id) => api.get(`/documents/${id}`),
  create: (documentData) => api.post('/documents', documentData),
  update: (id, documentData) => api.put(`/documents/${id}`, documentData),
  delete: (id) => api.delete(`/documents/${id}`),
  download: (id) => api.get(`/documents/${id}/download`, { responseType: 'blob' }),
  upload: (formData) => api.post('/documents/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  getCategories: () => api.get('/documents/categories'),
  getStats: () => api.get('/documents/stats'),
  search: (params) => api.get('/documents/search', { params }),
};

// 产品管理API
export const productAPI = {
  getList: (params) => api.get('/products', { params }),
  getById: (id) => api.get(`/products/${id}`),
  create: (productData) => api.post('/products', productData),
  update: (id, productData) => api.put(`/products/${id}`, productData),
  delete: (id) => api.delete(`/products/${id}`),
  uploadFile: (id, formData) => api.post(`/products/${id}/files`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  downloadFile: (id, fileId) => api.get(`/products/${id}/files/${fileId}/download`, { 
    responseType: 'blob' 
  }),
  deleteFile: (id, fileId) => api.delete(`/products/${id}/files/${fileId}`),
  getStats: () => api.get('/products/stats'),
};

// 用户管理API
export const userAPI = {
  getList: (params) => api.get('/users', { params }),
  getById: (id) => api.get(`/users/${id}`),
  create: (userData) => api.post('/users', userData),
  update: (id, userData) => api.put(`/users/${id}`, userData),
  delete: (id) => api.delete(`/users/${id}`),
  getStats: () => api.get('/users/stats'),
  resetPassword: (id, passwordData) => api.post(`/users/${id}/reset-password`, passwordData),
};

// 系统API
export const systemAPI = {
  getStats: () => api.get('/system/stats'),
  getConfig: () => api.get('/system/config'),
  updateConfig: (configData) => api.put('/system/config', configData),
  getLogs: (params) => api.get('/system/logs', { params }),
};

export default api;
