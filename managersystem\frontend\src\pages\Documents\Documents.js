import React from 'react';
import { Card, Typography, Empty, Button } from 'antd';
import { FileTextOutlined, PlusOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Documents = () => {
  return (
    <div className="documents-container">
      <div className="page-header">
        <Title level={2}>文档库</Title>
      </div>

      <Card>
        <Empty
          image={<FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
          description="文档管理功能开发中..."
        >
          <Button type="primary" icon={<PlusOutlined />}>
            上传文档
          </Button>
        </Empty>
      </Card>
    </div>
  );
};

export default Documents;
