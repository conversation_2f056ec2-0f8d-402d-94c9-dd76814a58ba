#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask后端API服务器
提供SDT卫星数字孪生系统的REST API接口
"""

import os
import sys
import json
import numpy as np
from datetime import datetime, timezone
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入SDT模块
from modules.orbit_calculation import TLE, TLEParser, SGP4Propagator
from modules.digital_twin import StateVector, Satellite, NumericalPropagator
from modules.topology_simulation import TopologySimulator


# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 全局变量存储仿真状态
current_satellites = {}
current_ephemeris = {}
current_topology_results = {}


@app.route('/')
def index():
    """主页"""
    return jsonify({
        'name': 'SDT卫星数字孪生系统API',
        'version': '2.0.0',
        'description': '提供轨道计算、数字孪生建模和拓扑仿真功能',
        'endpoints': {
            'tle': '/api/v1/tle',
            'propagate': '/api/v1/propagate',
            'simulate': '/api/v1/simulate',
            'topology': '/api/v1/topology'
        }
    })


@app.route('/api/v1/tle/parse', methods=['POST'])
def parse_tle():
    """
    解析TLE数据
    POST /api/v1/tle/parse
    Body: {"tle_string": "TLE数据字符串"}
    """
    try:
        data = request.get_json()
        if not data or 'tle_string' not in data:
            return jsonify({'error': '缺少tle_string参数'}), 400
        
        tle_string = data['tle_string']
        
        # 解析TLE
        parser = TLEParser()
        tle_objects = parser.parse(tle_string)
        
        # 转换为JSON格式
        result = []
        for tle in tle_objects:
            result.append(tle.to_dict())
        
        return jsonify({
            'success': True,
            'count': len(result),
            'tle_data': result
        })
        
    except Exception as e:
        return jsonify({'error': f'TLE解析失败: {str(e)}'}), 400


@app.route('/api/v1/propagate/sgp4', methods=['POST'])
def propagate_sgp4():
    """
    SGP4轨道传播
    POST /api/v1/propagate/sgp4
    Body: {
        "tle": TLE对象,
        "start_time": "2024-01-01T00:00:00Z",
        "end_time": "2024-01-01T01:00:00Z",
        "time_step": 60
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '缺少请求数据'}), 400
        
        # 验证必需参数
        required_fields = ['tle', 'start_time', 'end_time']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少{field}参数'}), 400
        
        # 解析TLE
        tle = TLE.from_dict(data['tle'])
        
        # 解析时间
        start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
        end_time = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
        time_step = data.get('time_step', 60)  # 默认60秒
        
        # 转换为儒略日
        start_jd = _datetime_to_jd(start_time)
        end_jd = _datetime_to_jd(end_time)
        
        # 创建SGP4传播器
        propagator = SGP4Propagator()
        
        # 生成时间序列
        total_seconds = (end_jd - start_jd) * 86400
        num_steps = int(total_seconds / time_step) + 1
        
        results = []
        for i in range(num_steps):
            current_jd = start_jd + (i * time_step / 86400.0)
            
            # 传播轨道
            position, velocity, error_code = propagator.propagate(tle, current_jd)
            
            if error_code != 0:
                continue
            
            # 转换时间
            current_time = _jd_to_datetime(current_jd)
            
            results.append({
                'time': current_time.isoformat() + 'Z',
                'position': position.tolist(),
                'velocity': velocity.tolist(),
                'error_code': error_code
            })
        
        return jsonify({
            'success': True,
            'satellite_name': tle.name,
            'count': len(results),
            'ephemeris': results
        })
        
    except Exception as e:
        return jsonify({'error': f'SGP4传播失败: {str(e)}'}), 500


@app.route('/api/v1/simulate/orbit', methods=['POST'])
def simulate_orbit():
    """
    高精度轨道仿真
    POST /api/v1/simulate/orbit
    Body: {
        "initial_state": StateVector数据,
        "satellite": Satellite属性,
        "start_time": "2024-01-01T00:00:00Z",
        "end_time": "2024-01-01T01:00:00Z",
        "time_step": 60,
        "perturbations": {
            "j2": true,
            "drag": true,
            "srp": true
        }
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '缺少请求数据'}), 400
        
        # 验证必需参数
        required_fields = ['initial_state', 'satellite', 'start_time', 'end_time']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少{field}参数'}), 400
        
        # 解析初始状态
        state_data = data['initial_state']
        initial_state = StateVector(
            position=np.array(state_data['position']),
            velocity=np.array(state_data['velocity']),
            quaternion=np.array(state_data.get('quaternion', [1, 0, 0, 0])),
            angular_velocity=np.array(state_data.get('angular_velocity', [0, 0, 0]))
        )
        
        # 解析卫星属性
        satellite = Satellite.from_dict(data['satellite'])
        
        # 解析时间
        start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
        end_time = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
        time_step = data.get('time_step', 60)
        
        # 转换为儒略日
        start_jd = _datetime_to_jd(start_time)
        end_jd = _datetime_to_jd(end_time)
        
        # 解析摄动设置
        perturbations = data.get('perturbations', {})
        include_j2 = perturbations.get('j2', True)
        include_drag = perturbations.get('drag', True)
        include_srp = perturbations.get('srp', True)
        
        # 创建数值传播器
        propagator = NumericalPropagator(
            include_j2=include_j2,
            include_drag=include_drag,
            include_srp=include_srp
        )
        
        # 运行仿真
        ephemeris = propagator.propagate(
            initial_state, satellite, start_jd, end_jd, time_step
        )
        
        # 转换结果
        results = []
        time_step_days = time_step / 86400.0
        
        for i, state in enumerate(ephemeris):
            current_jd = start_jd + i * time_step_days
            current_time = _jd_to_datetime(current_jd)
            
            results.append({
                'time': current_time.isoformat() + 'Z',
                'position': state['position'].tolist(),
                'velocity': state['velocity'].tolist(),
                'quaternion': state['quaternion'].tolist(),
                'angular_velocity': state['angular_velocity'].tolist()
            })
        
        # 获取统计信息
        stats = propagator.get_statistics()
        
        return jsonify({
            'success': True,
            'satellite_name': satellite.name,
            'count': len(results),
            'ephemeris': results,
            'statistics': stats
        })
        
    except Exception as e:
        return jsonify({'error': f'轨道仿真失败: {str(e)}'}), 500


@app.route('/api/v1/topology/analyze', methods=['POST'])
def analyze_topology():
    """
    网络拓扑分析
    POST /api/v1/topology/analyze
    Body: {
        "ephemeris": [卫星星历数据],
        "time_indices": [0, 10, 20],
        "max_link_distance": 5000,
        "earth_radius": 6378.137
    }
    """
    try:
        data = request.get_json()
        if not data or 'ephemeris' not in data:
            return jsonify({'error': '缺少ephemeris参数'}), 400
        
        ephemeris_data = data['ephemeris']
        time_indices = data.get('time_indices')
        max_link_distance = data.get('max_link_distance', 5000.0)
        earth_radius = data.get('earth_radius', 6378.137)
        
        # 转换星历数据格式
        # 假设输入格式为 [{"satellite_id": 0, "ephemeris": [...]}]
        if not ephemeris_data:
            return jsonify({'error': '星历数据为空'}), 400
        
        # 创建拓扑模拟器
        simulator = TopologySimulator(
            earth_radius=earth_radius,
            max_link_distance=max_link_distance
        )
        
        # 这里需要根据实际的星历数据格式进行转换
        # 简化处理：假设已经是正确格式的numpy数组
        
        # 运行拓扑分析
        # topology_snapshots = simulator.run_simulation(ephemeris_array, time_indices)
        
        # 临时返回示例结果
        return jsonify({
            'success': True,
            'message': '拓扑分析功能正在开发中',
            'parameters': {
                'max_link_distance': max_link_distance,
                'earth_radius': earth_radius,
                'time_indices': time_indices
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'拓扑分析失败: {str(e)}'}), 500


@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'version': '2.0.0'
    })


# 辅助函数
def _datetime_to_jd(dt: datetime) -> float:
    """将datetime转换为儒略日"""
    # 简化的儒略日计算
    a = (14 - dt.month) // 12
    y = dt.year - a
    m = dt.month + 12 * a - 3
    
    jd = dt.day + (153 * m + 2) // 5 + 365 * y + y // 4 - y // 100 + y // 400 + 1721119
    
    # 添加时间部分
    time_fraction = (dt.hour + dt.minute / 60.0 + dt.second / 3600.0) / 24.0
    
    return jd + time_fraction - 0.5


def _jd_to_datetime(jd: float) -> datetime:
    """将儒略日转换为datetime"""
    # 简化的datetime计算
    jd_int = int(jd + 0.5)
    jd_frac = jd + 0.5 - jd_int
    
    a = jd_int + 32044
    b = (4 * a + 3) // 146097
    c = a - (146097 * b) // 4
    d = (4 * c + 3) // 1461
    e = c - (1461 * d) // 4
    m = (5 * e + 2) // 153
    
    day = e - (153 * m + 2) // 5 + 1
    month = m + 3 - 12 * (m // 10)
    year = 100 * b + d - 4800 + m // 10
    
    # 计算时间部分
    hours = jd_frac * 24
    hour = int(hours)
    minutes = (hours - hour) * 60
    minute = int(minutes)
    seconds = (minutes - minute) * 60
    second = int(seconds)
    microsecond = int((seconds - second) * 1000000)
    
    return datetime(year, month, day, hour, minute, second, microsecond, timezone.utc)


if __name__ == '__main__':
    # 开发模式运行
    app.run(host='0.0.0.0', port=5000, debug=True)
