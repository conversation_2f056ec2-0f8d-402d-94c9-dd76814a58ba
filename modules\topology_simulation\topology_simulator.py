#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拓扑模拟器
分析由数字孪生模块生成的星历数据，模拟网络行为
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from .line_of_sight import is_los_clear, LOSCalculator
from .routing import dijkstra, compute_network_connectivity, RoutingTable


@dataclass
class NetworkMetrics:
    """网络性能指标"""
    connectivity: float           # 连通性
    average_path_length: float   # 平均路径长度
    diameter: float              # 网络直径
    connected_pairs: int         # 连通节点对数
    total_pairs: int            # 总节点对数
    timestamp: float            # 时间戳


@dataclass
class TopologySnapshot:
    """拓扑快照"""
    timestamp: float                    # 时间戳
    adjacency_matrix: np.ndarray       # 邻接矩阵
    distance_matrix: np.ndarray        # 距离矩阵
    positions: np.ndarray              # 卫星位置
    metrics: NetworkMetrics            # 网络指标


class TopologySimulator:
    """
    拓扑模拟器类
    按照设计文档要求实现网络拓扑分析功能
    """
    
    def __init__(self, earth_radius: float = 6378.137, max_link_distance: float = 5000.0):
        """
        初始化拓扑模拟器
        
        Args:
            earth_radius: 地球半径 (km)
            max_link_distance: 最大链路距离 (km)
        """
        self.earth_radius = earth_radius
        self.max_link_distance = max_link_distance
        
        # 初始化组件
        self.los_calculator = LOSCalculator(earth_radius)
        
        # 存储仿真结果
        self.topology_history: List[TopologySnapshot] = []
        self.current_routing_table: Optional[RoutingTable] = None
        
        # 统计信息
        self.total_computations = 0
        self.total_links_computed = 0
    
    def run_simulation(self, ephemeris: np.ndarray, time_indices: Optional[List[int]] = None) -> List[TopologySnapshot]:
        """
        运行拓扑仿真
        
        Args:
            ephemeris: 星历数据，形状为 (num_satellites, num_time_steps, StateVectorDType)
            time_indices: 要分析的时间索引列表，None表示分析所有时间步
            
        Returns:
            拓扑快照列表
        """
        if ephemeris.ndim != 2:
            raise ValueError(f"星历数据维度错误: {ephemeris.shape}")
        
        num_satellites, num_time_steps = ephemeris.shape
        
        if time_indices is None:
            time_indices = list(range(num_time_steps))
        
        # 清空历史记录
        self.topology_history.clear()
        
        # 遍历每个时间步
        for time_idx in time_indices:
            if time_idx >= num_time_steps:
                continue
            
            # 提取当前时间步的所有卫星位置
            positions = np.zeros((num_satellites, 3))
            for sat_idx in range(num_satellites):
                state = ephemeris[sat_idx, time_idx]
                positions[sat_idx] = state['position']
            
            # 计算拓扑
            snapshot = self._compute_topology_snapshot(positions, float(time_idx))
            self.topology_history.append(snapshot)
            
            self.total_computations += 1
        
        return self.topology_history
    
    def _compute_topology_snapshot(self, positions: np.ndarray, timestamp: float) -> TopologySnapshot:
        """
        计算单个时间步的拓扑快照
        
        Args:
            positions: 卫星位置数组，形状为 (N, 3)
            timestamp: 时间戳
            
        Returns:
            拓扑快照
        """
        num_satellites = positions.shape[0]
        
        # 计算邻接矩阵
        adjacency_matrix = self._compute_adjacency_matrix(positions)
        
        # 计算距离矩阵
        distance_matrix = self.los_calculator.compute_distance_matrix(positions)
        
        # 计算网络指标
        connectivity_metrics = compute_network_connectivity(adjacency_matrix)
        metrics = NetworkMetrics(
            connectivity=connectivity_metrics['connectivity'],
            average_path_length=connectivity_metrics['average_path_length'],
            diameter=connectivity_metrics['diameter'],
            connected_pairs=connectivity_metrics['connected_pairs'],
            total_pairs=connectivity_metrics['total_pairs'],
            timestamp=timestamp
        )
        
        # 更新路由表
        if num_satellites > 0:
            self.current_routing_table = RoutingTable(num_satellites)
            self.current_routing_table.update_from_adjacency_matrix(adjacency_matrix)
        
        return TopologySnapshot(
            timestamp=timestamp,
            adjacency_matrix=adjacency_matrix,
            distance_matrix=distance_matrix,
            positions=positions.copy(),
            metrics=metrics
        )
    
    def _compute_adjacency_matrix(self, positions: np.ndarray) -> np.ndarray:
        """
        计算邻接矩阵
        
        Args:
            positions: 卫星位置数组，形状为 (N, 3)
            
        Returns:
            邻接矩阵，形状为 (N, N)
        """
        N = positions.shape[0]
        adj_matrix = np.zeros((N, N), dtype=int)
        
        # 使用双重循环计算所有卫星对的连通性
        for i in range(N):
            for j in range(i + 1, N):
                # 计算距离
                distance = np.linalg.norm(positions[i] - positions[j])
                
                # 检查距离限制和视线
                if (distance <= self.max_link_distance and 
                    is_los_clear(positions[i], positions[j], self.earth_radius)):
                    adj_matrix[i, j] = 1
                    adj_matrix[j, i] = 1
                
                self.total_links_computed += 1
        
        return adj_matrix
    
    def get_path_at_time(self, source: int, destination: int, time_index: int) -> List[int]:
        """
        获取指定时间的路径
        
        Args:
            source: 源卫星索引
            destination: 目标卫星索引
            time_index: 时间索引
            
        Returns:
            路径节点列表
        """
        if time_index >= len(self.topology_history):
            return []
        
        snapshot = self.topology_history[time_index]
        return dijkstra(snapshot.adjacency_matrix, source, destination)
    
    def analyze_connectivity_over_time(self) -> Dict[str, List[float]]:
        """
        分析连通性随时间的变化
        
        Returns:
            连通性指标时间序列
        """
        if not self.topology_history:
            return {}
        
        timestamps = []
        connectivity_values = []
        avg_path_lengths = []
        diameters = []
        
        for snapshot in self.topology_history:
            timestamps.append(snapshot.timestamp)
            connectivity_values.append(snapshot.metrics.connectivity)
            avg_path_lengths.append(snapshot.metrics.average_path_length)
            diameters.append(snapshot.metrics.diameter)
        
        return {
            'timestamps': timestamps,
            'connectivity': connectivity_values,
            'average_path_length': avg_path_lengths,
            'diameter': diameters
        }
    
    def find_critical_satellites(self, time_index: int) -> List[Tuple[int, float]]:
        """
        寻找关键卫星（移除后对连通性影响最大的卫星）
        
        Args:
            time_index: 时间索引
            
        Returns:
            关键卫星列表，格式为 [(卫星索引, 影响度)]
        """
        if time_index >= len(self.topology_history):
            return []
        
        snapshot = self.topology_history[time_index]
        original_connectivity = snapshot.metrics.connectivity
        
        N = snapshot.adjacency_matrix.shape[0]
        criticality_scores = []
        
        for sat_idx in range(N):
            # 创建移除该卫星后的邻接矩阵
            temp_adj = snapshot.adjacency_matrix.copy()
            temp_adj[sat_idx, :] = 0
            temp_adj[:, sat_idx] = 0
            
            # 计算新的连通性
            temp_metrics = compute_network_connectivity(temp_adj)
            new_connectivity = temp_metrics['connectivity']
            
            # 计算影响度
            impact = original_connectivity - new_connectivity
            criticality_scores.append((sat_idx, impact))
        
        # 按影响度排序
        criticality_scores.sort(key=lambda x: x[1], reverse=True)
        
        return criticality_scores
    
    def compute_link_utilization(self, traffic_matrix: np.ndarray, time_index: int) -> Dict[Tuple[int, int], float]:
        """
        计算链路利用率
        
        Args:
            traffic_matrix: 流量矩阵，形状为 (N, N)
            time_index: 时间索引
            
        Returns:
            链路利用率字典，键为 (源节点, 目标节点)
        """
        if time_index >= len(self.topology_history):
            return {}
        
        snapshot = self.topology_history[time_index]
        N = snapshot.adjacency_matrix.shape[0]
        
        # 初始化链路利用率
        link_utilization = {}
        
        # 计算每条链路的流量
        for source in range(N):
            for dest in range(N):
                if source != dest and traffic_matrix[source, dest] > 0:
                    # 找到路径
                    path = dijkstra(snapshot.adjacency_matrix, source, dest)
                    
                    if len(path) > 1:
                        # 为路径上的每条链路增加流量
                        for i in range(len(path) - 1):
                            link = (path[i], path[i + 1])
                            if link not in link_utilization:
                                link_utilization[link] = 0.0
                            link_utilization[link] += traffic_matrix[source, dest]
        
        return link_utilization
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取仿真统计信息
        
        Returns:
            统计信息字典
        """
        if not self.topology_history:
            return {}
        
        # 计算平均指标
        avg_connectivity = np.mean([s.metrics.connectivity for s in self.topology_history])
        avg_path_length = np.mean([s.metrics.average_path_length for s in self.topology_history])
        avg_diameter = np.mean([s.metrics.diameter for s in self.topology_history])
        
        return {
            'total_computations': self.total_computations,
            'total_links_computed': self.total_links_computed,
            'time_steps_analyzed': len(self.topology_history),
            'average_connectivity': avg_connectivity,
            'average_path_length': avg_path_length,
            'average_diameter': avg_diameter,
            'earth_radius': self.earth_radius,
            'max_link_distance': self.max_link_distance
        }
    
    def export_topology_data(self, filename: str, time_index: int):
        """
        导出拓扑数据
        
        Args:
            filename: 文件名
            time_index: 时间索引
        """
        if time_index >= len(self.topology_history):
            raise ValueError("时间索引超出范围")
        
        snapshot = self.topology_history[time_index]
        
        data = {
            'timestamp': snapshot.timestamp,
            'adjacency_matrix': snapshot.adjacency_matrix.tolist(),
            'distance_matrix': snapshot.distance_matrix.tolist(),
            'positions': snapshot.positions.tolist(),
            'metrics': {
                'connectivity': snapshot.metrics.connectivity,
                'average_path_length': snapshot.metrics.average_path_length,
                'diameter': snapshot.metrics.diameter,
                'connected_pairs': snapshot.metrics.connected_pairs,
                'total_pairs': snapshot.metrics.total_pairs
            }
        }
        
        import json
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
