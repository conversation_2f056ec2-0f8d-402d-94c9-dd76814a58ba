/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

/* 状态颜色 */
.status-success {
  color: #52c41a;
}

.status-warning {
  color: #faad14;
}

.status-error {
  color: #ff4d4f;
}

.status-info {
  color: #1890ff;
}

.status-default {
  color: #d9d9d9;
}

/* 优先级颜色 */
.priority-low {
  color: #52c41a;
}

.priority-medium {
  color: #faad14;
}

.priority-high {
  color: #ff7a45;
}

.priority-critical {
  color: #ff4d4f;
}

/* 卡片阴影 */
.card-shadow {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02),
    0 2px 4px rgba(0, 0, 0, 0.02);
}

.card-shadow-hover {
  transition: box-shadow 0.3s ease;
}

.card-shadow-hover:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮样式增强 */
.btn-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: #fff;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  color: #fff;
}

/* 表格样式增强 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.table-container .ant-table {
  border-radius: 8px;
}

.table-container .ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
}

.table-container .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 表单样式增强 */
.form-container {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.form-container .ant-form-item-label > label {
  font-weight: 500;
}

/* 统计卡片 */
.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-card .stat-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 8px;
}

.stat-card .stat-value {
  font-size: 30px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.2;
}

.stat-card .stat-suffix {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.45);
  margin-left: 4px;
}

/* 标签样式 */
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-container .ant-tag {
  margin: 0;
  border-radius: 4px;
}

/* 头像样式 */
.avatar-with-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.avatar-with-name .ant-avatar {
  flex-shrink: 0;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: rgba(0, 0, 0, 0.45);
}

.empty-state .empty-icon {
  font-size: 48px;
  color: rgba(0, 0, 0, 0.25);
  margin-bottom: 16px;
}

.empty-state .empty-title {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 8px;
}

.empty-state .empty-description {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48px;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 文本省略 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 自定义 Ant Design 组件样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-input,
.ant-input-affix-wrapper {
  border-radius: 6px;
}

.ant-select .ant-select-selector {
  border-radius: 6px;
}

.ant-modal .ant-modal-content {
  border-radius: 8px;
}

.ant-drawer .ant-drawer-content {
  border-radius: 8px 0 0 8px;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-after: always;
  }
}
