{"version": 3, "names": ["_isType", "require", "_index", "isImmutable", "node", "isType", "type", "isIdentifier", "name"], "sources": ["../../src/validators/isImmutable.ts"], "sourcesContent": ["import isType from \"./isType.ts\";\nimport { isIdentifier } from \"./generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Check if the input `node` is definitely immutable.\n */\nexport default function isImmutable(node: t.Node): boolean {\n  if (isType(node.type, \"Immutable\")) return true;\n\n  if (isIdentifier(node)) {\n    if (node.name === \"undefined\") {\n      // immutable!\n      return true;\n    } else {\n      // no idea...\n      return false;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAMe,SAASE,WAAWA,CAACC,IAAY,EAAW;EACzD,IAAI,IAAAC,eAAM,EAACD,IAAI,CAACE,IAAI,EAAE,WAAW,CAAC,EAAE,OAAO,IAAI;EAE/C,IAAI,IAAAC,mBAAY,EAACH,IAAI,CAAC,EAAE;IACtB,IAAIA,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;MAE7B,OAAO,IAAI;IACb,CAAC,MAAM;MAEL,OAAO,KAAK;IACd;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}