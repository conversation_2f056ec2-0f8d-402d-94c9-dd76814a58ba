#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TLE (Two-Line Element) 数据类
用于存储已解析的TLE数据
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class TLE:
    """
    TLE数据类，用于存储已解析的TLE数据
    按照设计文档要求实现所有字段
    """
    # Line 0 - 卫星名称
    name: str
    
    # Line 1 字段
    satellite_number: int           # 卫星编号
    classification: str             # 分类标识 ('U' = 未分类)
    launch_year: int               # 发射年份
    launch_number: int             # 发射序号
    launch_piece: str              # 发射片段
    epoch_year: int                # 历元年份
    epoch_day: float               # 历元日
    mean_motion_dot: float         # 平运动一阶导数
    mean_motion_ddot: float        # 平运动二阶导数
    bstar_drag: float              # B*阻力项
    ephemeris_type: int            # 星历类型
    element_set_number: int        # 元素集编号
    
    # Line 2 字段
    inclination: float             # 轨道倾角 (度)
    raan: float                    # 升交点赤经 (度)
    eccentricity: float            # 偏心率
    arg_of_perigee: float          # 近地点幅角 (度)
    mean_anomaly: float            # 平近点角 (度)
    mean_motion: float             # 平运动 (圈/天)
    rev_at_epoch: int              # 历元圈数
    
    def __post_init__(self):
        """数据验证"""
        # 验证基本数据范围
        if not (0 <= self.inclination <= 180):
            raise ValueError(f"轨道倾角必须在0-180度之间: {self.inclination}")
        
        if not (0 <= self.raan < 360):
            raise ValueError(f"升交点赤经必须在0-360度之间: {self.raan}")
            
        if not (0 <= self.eccentricity < 1):
            raise ValueError(f"偏心率必须在0-1之间: {self.eccentricity}")
            
        if not (0 <= self.arg_of_perigee < 360):
            raise ValueError(f"近地点幅角必须在0-360度之间: {self.arg_of_perigee}")
            
        if not (0 <= self.mean_anomaly < 360):
            raise ValueError(f"平近点角必须在0-360度之间: {self.mean_anomaly}")
            
        if self.mean_motion <= 0:
            raise ValueError(f"平运动必须大于0: {self.mean_motion}")
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'name': self.name,
            'satellite_number': self.satellite_number,
            'classification': self.classification,
            'launch_year': self.launch_year,
            'launch_number': self.launch_number,
            'launch_piece': self.launch_piece,
            'epoch_year': self.epoch_year,
            'epoch_day': self.epoch_day,
            'mean_motion_dot': self.mean_motion_dot,
            'mean_motion_ddot': self.mean_motion_ddot,
            'bstar_drag': self.bstar_drag,
            'ephemeris_type': self.ephemeris_type,
            'element_set_number': self.element_set_number,
            'inclination': self.inclination,
            'raan': self.raan,
            'eccentricity': self.eccentricity,
            'arg_of_perigee': self.arg_of_perigee,
            'mean_anomaly': self.mean_anomaly,
            'mean_motion': self.mean_motion,
            'rev_at_epoch': self.rev_at_epoch
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TLE':
        """从字典创建TLE对象"""
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"TLE({self.name}, SAT#{self.satellite_number}, INC={self.inclination:.2f}°)"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"TLE(name='{self.name}', satellite_number={self.satellite_number}, "
                f"inclination={self.inclination}, mean_motion={self.mean_motion})")
