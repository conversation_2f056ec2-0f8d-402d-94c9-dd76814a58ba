import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Layout, Spin } from 'antd';

import { checkAuth } from './store/authSlice';
import Login from './pages/Login/Login';
import Dashboard from './pages/Dashboard/Dashboard';
import Issues from './pages/Issues/Issues';
import Documents from './pages/Documents/Documents';
import Products from './pages/Products/Products';
import Users from './pages/Users/<USER>';
import MainLayout from './components/layout/MainLayout';

import './assets/styles/global.css';

const { Content } = Layout;

function App() {
  const dispatch = useDispatch();
  const { isAuthenticated, loading, user } = useSelector(state => state.auth);

  useEffect(() => {
    // 检查本地存储的token
    const token = localStorage.getItem('token');
    if (token) {
      dispatch(checkAuth());
    }
  }, [dispatch]);

  // 加载中状态
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // 未登录状态
  if (!isAuthenticated) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    );
  }

  // 已登录状态
  return (
    <MainLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/issues/*" element={<Issues />} />
        <Route path="/documents/*" element={<Documents />} />
        <Route path="/products/*" element={<Products />} />
        {(user?.role === 'super_admin' || user?.role === 'admin') && (
          <Route path="/users/*" element={<Users />} />
        )}
        <Route path="/login" element={<Navigate to="/dashboard" replace />} />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </MainLayout>
  );
}

export default App;
