import pytest
from engine.time_manager import TimeManager

@pytest.fixture
def tm():
    """Pytest fixture for TimeManager."""
    return TimeManager()

def test_initial_time(tm):
    """Test the initial time of the manager."""
    assert tm.get_current_time() == 0.0
    assert tm.get_elapsed_time() == 0.0

def test_advance_time(tm):
    """Test advancing the simulation time."""
    assert tm.advance_time(10.5)
    assert tm.get_current_time() == 10.5
    assert tm.get_elapsed_time() == 10.5
    
    # Test advancing again
    assert tm.advance_time(12.0)
    assert tm.get_current_time() == 12.0
    
    # Test advancing backwards (should fail)
    assert not tm.advance_time(11.0)
    assert tm.get_current_time() == 12.0

def test_pause_and_resume(tm):
    """Test pausing and resuming time."""
    tm.advance_time(5.0)
    assert not tm.is_paused()
    
    tm.pause()
    assert tm.is_paused()
    
    # Time should not advance while paused
    assert not tm.advance_time(10.0)
    assert tm.get_current_time() == 5.0
    
    tm.resume()
    assert not tm.is_paused()
    
    # Time should advance now
    assert tm.advance_time(10.0)
    assert tm.get_current_time() == 10.0

def test_reset(tm):
    """Test resetting the time manager."""
    tm.advance_time(100.0)
    tm.reset(new_start_time=50.0)
    
    assert tm.get_current_time() == 50.0
    assert tm.get_elapsed_time() == 0.0 # Elapsed is relative to new start time
    assert tm.time_advance_count == 0 