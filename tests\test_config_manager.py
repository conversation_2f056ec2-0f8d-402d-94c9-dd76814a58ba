import pytest
import os
import json
from utils.config_manager import <PERSON>fig<PERSON>anager, SystemConfig, ConstellationConfig

def test_config_manager_defaults():
    """Test that the manager initializes with default dataclass values."""
    # Initialize without a file to test in-memory defaults
    cm = ConfigManager(config_file="nonexistent_file.json")
    
    sys_cfg = cm.get_system_config()
    assert sys_cfg.system_name == "SDT"
    assert not sys_cfg.debug_mode
    
    const_cfg = cm.get_constellation_config("Default")
    assert const_cfg is not None
    assert const_cfg.altitude == 550.0

def test_config_save_and_load(tmp_path):
    """
    Test saving the configuration to a file and loading it back.
    `tmp_path` is a pytest fixture that provides a temporary directory.
    """
    config_file = tmp_path / "test_config.json"
    
    # 1. Create a manager, save the initial config
    cm1 = ConfigManager(config_file=str(config_file))
    cm1.update_system_config(debug_mode=True, log_level="DEBUG")
    assert cm1.save_config()
    
    # 2. Create a new manager and load from that file
    cm2 = ConfigManager(config_file=str(config_file))
    
    # 3. Check if the loaded config matches what was saved
    sys_cfg = cm2.get_system_config()
    assert sys_cfg.debug_mode is True
    assert sys_cfg.log_level == "DEBUG"

def test_update_and_get_config(tmp_path):
    """Test updating and retrieving configuration sections."""
    config_file = tmp_path / "update_test.json"
    cm = ConfigManager(config_file=str(config_file))

    # Update system config
    original_version = cm.get_system_config().version
    cm.update_system_config(version="2.0.0")
    assert cm.get_system_config().version == "2.0.0"
    assert cm.get_system_config().version != original_version

    # Add and remove a constellation
    new_const = ConstellationConfig(name="TestLeo", altitude=400.0, planes=5)
    cm.add_constellation_config(new_const)
    assert cm.get_constellation_config("TestLeo") is not None
    assert cm.get_constellation_config("TestLeo").altitude == 400.0
    
    assert cm.remove_constellation_config("TestLeo")
    assert cm.get_constellation_config("TestLeo") is None 