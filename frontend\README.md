# SDT卫星数字孪生系统前端界面

这是一个纯HTML/CSS/JavaScript实现的卫星数字孪生系统前端界面，无需任何框架依赖。

## 功能特性

### 🌍 地球可视化
- 3D地球渲染，包含大陆轮廓
- 实时地球自转动画
- 星空背景效果

### 🛰️ 卫星系统
- 支持多种星座选择（Kuiper_630、Starlink、OneWeb）
- 多层轨道卫星分布
- 实时卫星运动轨迹
- 3D透视效果

### 🎛️ 交互控制
- **星座选择**: 选择不同的卫星星座
- **路径计算**: 计算终端间通信路径
- **仿真控制**: 启动/停止仿真动画
- **信息显示**: 实时显示卫星信息

### ⏱️ 时间系统
- 底部时间轴显示
- 实时UTC时间更新
- 进度条动画

## 使用方法

1. **直接打开**: 在浏览器中打开 `index.html` 文件
2. **本地服务器**: 推荐使用本地HTTP服务器运行以获得最佳效果

```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx http-server
```

3. **在浏览器中访问**: `http://localhost:8000`

## 界面说明

### 左侧控制面板
- **Select Constellation**: 选择卫星星座类型
- **Path Compute**: 配置通信路径计算
- **Satellite Information**: 显示选中卫星的详细信息

### 中央视图区域
- 3D地球模型和卫星可视化
- 支持实时动画和轨道显示

### 右侧控制面板
- **Simulation/Configuration/Query**: 切换不同功能模式
- **离散事件引擎**: 控制仿真运行状态
- **Terminal 1/2**: 终端连接和命令输入

### 底部时间轴
- 显示当前仿真时间
- 进度条指示仿真进度

## 操作指南

1. **选择星座**: 
   - 在左侧下拉菜单中选择星座类型
   - 点击"Select"按钮加载星座

2. **开始仿真**:
   - 点击右侧"Run"按钮启动仿真
   - 状态将从"Stopped"变为"Running"

3. **路径计算**:
   - 在"Path Compute"区域选择源和目标
   - 点击"Compute"计算通信路径

4. **重置视图**:
   - 点击"Reset"按钮重置地球和卫星位置

## 技术实现

- **HTML5 Canvas**: 用于地球和卫星的2D/3D渲染
- **CSS3**: 深色主题界面设计
- **JavaScript ES6**: 动画逻辑和交互控制
- **无框架依赖**: 纯原生Web技术实现

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 自定义配置

可以通过修改 `script.js` 中的参数来自定义：
- 卫星数量和轨道参数
- 动画速度和渲染效果
- 界面颜色和样式

## 注意事项

- 建议在现代浏览器中运行以获得最佳性能
- 大量卫星可能影响渲染性能，可根据设备性能调整
- 界面设计为桌面端优化，移动端可能需要额外适配 