"""
Redis客户端工具类
提供Redis连接和基础操作方法
"""

import redis
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from flask import current_app


class RedisClient:
    """Redis客户端封装类"""
    
    def __init__(self, redis_url: str = None):
        """初始化Redis连接"""
        self.redis_url = redis_url or current_app.config.get('REDIS_URL', 'redis://localhost:6379/0')
        self._client = None
    
    @property
    def client(self):
        """获取Redis客户端实例"""
        if self._client is None:
            self._client = redis.from_url(self.redis_url, decode_responses=True)
        return self._client
    
    def ping(self) -> bool:
        """测试Redis连接"""
        try:
            return self.client.ping()
        except Exception:
            return False
    
    # 基础操作
    def set(self, key: str, value: Any, expire: int = None) -> bool:
        """设置键值对"""
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            return self.client.set(key, value, ex=expire)
        except Exception as e:
            current_app.logger.error(f"Redis set error: {e}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """获取值"""
        try:
            value = self.client.get(key)
            if value is None:
                return None
            
            # 尝试解析JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        except Exception as e:
            current_app.logger.error(f"Redis get error: {e}")
            return None
    
    def delete(self, *keys: str) -> int:
        """删除键"""
        try:
            return self.client.delete(*keys)
        except Exception as e:
            current_app.logger.error(f"Redis delete error: {e}")
            return 0
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return bool(self.client.exists(key))
        except Exception as e:
            current_app.logger.error(f"Redis exists error: {e}")
            return False
    
    def expire(self, key: str, seconds: int) -> bool:
        """设置过期时间"""
        try:
            return self.client.expire(key, seconds)
        except Exception as e:
            current_app.logger.error(f"Redis expire error: {e}")
            return False
    
    # 列表操作
    def lpush(self, key: str, *values) -> int:
        """从左侧推入列表"""
        try:
            serialized_values = []
            for value in values:
                if isinstance(value, (dict, list)):
                    serialized_values.append(json.dumps(value, ensure_ascii=False))
                else:
                    serialized_values.append(value)
            return self.client.lpush(key, *serialized_values)
        except Exception as e:
            current_app.logger.error(f"Redis lpush error: {e}")
            return 0
    
    def lrange(self, key: str, start: int = 0, end: int = -1) -> List[Any]:
        """获取列表范围"""
        try:
            values = self.client.lrange(key, start, end)
            result = []
            for value in values:
                try:
                    result.append(json.loads(value))
                except (json.JSONDecodeError, TypeError):
                    result.append(value)
            return result
        except Exception as e:
            current_app.logger.error(f"Redis lrange error: {e}")
            return []
    
    def llen(self, key: str) -> int:
        """获取列表长度"""
        try:
            return self.client.llen(key)
        except Exception as e:
            current_app.logger.error(f"Redis llen error: {e}")
            return 0
    
    # 集合操作
    def sadd(self, key: str, *values) -> int:
        """添加到集合"""
        try:
            return self.client.sadd(key, *values)
        except Exception as e:
            current_app.logger.error(f"Redis sadd error: {e}")
            return 0
    
    def smembers(self, key: str) -> set:
        """获取集合成员"""
        try:
            return self.client.smembers(key)
        except Exception as e:
            current_app.logger.error(f"Redis smembers error: {e}")
            return set()
    
    def srem(self, key: str, *values) -> int:
        """从集合中移除"""
        try:
            return self.client.srem(key, *values)
        except Exception as e:
            current_app.logger.error(f"Redis srem error: {e}")
            return 0
    
    # 哈希操作
    def hset(self, key: str, mapping: Dict[str, Any]) -> int:
        """设置哈希字段"""
        try:
            serialized_mapping = {}
            for field, value in mapping.items():
                if isinstance(value, (dict, list)):
                    serialized_mapping[field] = json.dumps(value, ensure_ascii=False)
                else:
                    serialized_mapping[field] = value
            return self.client.hset(key, mapping=serialized_mapping)
        except Exception as e:
            current_app.logger.error(f"Redis hset error: {e}")
            return 0
    
    def hget(self, key: str, field: str) -> Optional[Any]:
        """获取哈希字段值"""
        try:
            value = self.client.hget(key, field)
            if value is None:
                return None
            
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        except Exception as e:
            current_app.logger.error(f"Redis hget error: {e}")
            return None
    
    def hgetall(self, key: str) -> Dict[str, Any]:
        """获取所有哈希字段"""
        try:
            data = self.client.hgetall(key)
            result = {}
            for field, value in data.items():
                try:
                    result[field] = json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    result[field] = value
            return result
        except Exception as e:
            current_app.logger.error(f"Redis hgetall error: {e}")
            return {}
    
    def hdel(self, key: str, *fields) -> int:
        """删除哈希字段"""
        try:
            return self.client.hdel(key, *fields)
        except Exception as e:
            current_app.logger.error(f"Redis hdel error: {e}")
            return 0
    
    # 搜索和分页
    def scan_keys(self, pattern: str) -> List[str]:
        """扫描匹配的键"""
        try:
            keys = []
            cursor = 0
            while True:
                cursor, partial_keys = self.client.scan(cursor, match=pattern, count=100)
                keys.extend(partial_keys)
                if cursor == 0:
                    break
            return keys
        except Exception as e:
            current_app.logger.error(f"Redis scan_keys error: {e}")
            return []
    
    def get_paginated_keys(self, pattern: str, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """分页获取键"""
        try:
            all_keys = self.scan_keys(pattern)
            total = len(all_keys)
            start = (page - 1) * size
            end = start + size
            keys = all_keys[start:end]
            
            return {
                'keys': keys,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total,
                    'pages': (total + size - 1) // size
                }
            }
        except Exception as e:
            current_app.logger.error(f"Redis get_paginated_keys error: {e}")
            return {'keys': [], 'pagination': {'page': 1, 'size': size, 'total': 0, 'pages': 0}}
    
    # 工具方法
    def generate_id(self, prefix: str = '') -> str:
        """生成唯一ID"""
        return f"{prefix}{uuid.uuid4().hex[:8]}"
    
    def get_timestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.now().isoformat()
    
    def add_to_index(self, index_key: str, item_id: str, score: float = None) -> bool:
        """添加到索引（使用有序集合）"""
        try:
            if score is None:
                score = datetime.now().timestamp()
            return bool(self.client.zadd(index_key, {item_id: score}))
        except Exception as e:
            current_app.logger.error(f"Redis add_to_index error: {e}")
            return False
    
    def remove_from_index(self, index_key: str, item_id: str) -> bool:
        """从索引中移除"""
        try:
            return bool(self.client.zrem(index_key, item_id))
        except Exception as e:
            current_app.logger.error(f"Redis remove_from_index error: {e}")
            return False
    
    def get_from_index(self, index_key: str, start: int = 0, end: int = -1, reverse: bool = True) -> List[str]:
        """从索引获取项目"""
        try:
            if reverse:
                return self.client.zrevrange(index_key, start, end)
            else:
                return self.client.zrange(index_key, start, end)
        except Exception as e:
            current_app.logger.error(f"Redis get_from_index error: {e}")
            return []


# 全局Redis客户端实例
redis_client = RedisClient()


# 数据键名常量
class RedisKeys:
    """Redis键名常量"""
    
    # 用户相关
    USER = "user:{user_id}"
    USER_INDEX = "users:index"
    USER_SESSION = "session:{token}"
    USER_BY_USERNAME = "user:username:{username}"
    
    # 问题相关
    ISSUE = "issue:{issue_id}"
    ISSUE_INDEX = "issues:index"
    ISSUE_COMMENTS = "issue:{issue_id}:comments"
    ISSUE_BY_STATUS = "issues:status:{status}"
    ISSUE_BY_ASSIGNEE = "issues:assignee:{user_id}"
    
    # 文档相关
    DOCUMENT = "document:{doc_id}"
    DOCUMENT_INDEX = "documents:index"
    DOCUMENT_CATEGORIES = "document:categories"
    DOCUMENT_BY_CATEGORY = "documents:category:{category}"
    
    # 产品相关
    PRODUCT = "product:{product_id}"
    PRODUCT_INDEX = "products:index"
    PRODUCT_FILES = "product:{product_id}:files"
    PRODUCT_BY_STATUS = "products:status:{status}"
    
    # 系统相关
    SYSTEM_CONFIG = "system:config"
    SYSTEM_STATS = "system:stats"
