/**
 * SDT卫星数字孪生系统 v1.0 前端应用
 * 与重构后的Flask API和Node.js服务器交互
 */

class SDTApp {
    constructor() {
        this.apiBaseUrl = '/api/v1';
        this.wsUrl = `ws://${window.location.host}`;
        this.websocket = null;
        this.currentTLE = null;
        this.currentEphemeris = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupTabs();
        this.checkAPIStatus();
        this.initializeWebSocket();
    }
    
    setupEventListeners() {
        // TLE解析
        document.getElementById('parse-tle').addEventListener('click', () => {
            this.parseTLE();
        });
        
        // 轨道计算
        document.getElementById('calculate-orbit').addEventListener('click', () => {
            this.calculateOrbit();
        });
        
        // 数字孪生仿真
        document.getElementById('simulate-orbit').addEventListener('click', () => {
            this.simulateOrbit();
        });
        
        // 拓扑分析
        document.getElementById('analyze-topology').addEventListener('click', () => {
            this.analyzeTopology();
        });
        
        // WebSocket连接
        document.getElementById('connect-ws').addEventListener('click', () => {
            this.connectWebSocket();
        });
        
        // 关闭提示
        document.querySelectorAll('.toast-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.target.closest('.toast').classList.add('hidden');
            });
        });
    }
    
    setupTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.dataset.tab;
                
                // 更新按钮状态
                tabBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // 更新内容显示
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(targetTab).classList.add('active');
            });
        });
    }
    
    async checkAPIStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`);
            const data = await response.json();
            
            if (response.ok) {
                this.updateStatus('api-status', 'API正常', 'connected');
            } else {
                this.updateStatus('api-status', 'API错误', 'disconnected');
            }
        } catch (error) {
            this.updateStatus('api-status', 'API离线', 'disconnected');
            console.error('API状态检查失败:', error);
        }
    }
    
    updateStatus(elementId, text, statusClass) {
        const element = document.getElementById(elementId);
        element.textContent = text;
        element.className = `status-indicator ${statusClass}`;
    }
    
    showLoading(show = true) {
        const loading = document.getElementById('loading');
        if (show) {
            loading.classList.remove('hidden');
        } else {
            loading.classList.add('hidden');
        }
    }
    
    showToast(message, type = 'success') {
        const toast = document.getElementById(`${type}-toast`);
        const messageElement = document.getElementById(`${type}-message`);
        
        messageElement.textContent = message;
        toast.classList.remove('hidden');
        
        // 自动隐藏
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 5000);
    }
    
    async parseTLE() {
        const tleInput = document.getElementById('tle-input').value.trim();
        
        if (!tleInput) {
            this.showToast('请输入TLE数据', 'error');
            return;
        }
        
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/tle/parse`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    tle_string: tleInput
                })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                this.currentTLE = data.tle_data[0]; // 使用第一个TLE
                this.displayTLEInfo(this.currentTLE);
                document.getElementById('calculate-orbit').disabled = false;
                this.showToast(`成功解析${data.count}个TLE`);
            } else {
                this.showToast(data.error || 'TLE解析失败', 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
            console.error('TLE解析错误:', error);
        } finally {
            this.showLoading(false);
        }
    }
    
    displayTLEInfo(tle) {
        const tleInfo = document.getElementById('tle-info');
        tleInfo.innerHTML = `
            <h4>TLE信息</h4>
            <p><strong>卫星名称:</strong> ${tle.name}</p>
            <p><strong>卫星编号:</strong> ${tle.satellite_number}</p>
            <p><strong>轨道倾角:</strong> ${tle.inclination.toFixed(4)}°</p>
            <p><strong>升交点赤经:</strong> ${tle.raan.toFixed(4)}°</p>
            <p><strong>偏心率:</strong> ${tle.eccentricity.toFixed(6)}</p>
            <p><strong>平运动:</strong> ${tle.mean_motion.toFixed(8)} 圈/天</p>
        `;
        tleInfo.classList.remove('hidden');
    }
    
    async calculateOrbit() {
        if (!this.currentTLE) {
            this.showToast('请先解析TLE数据', 'error');
            return;
        }
        
        const startTime = document.getElementById('start-time').value;
        const endTime = document.getElementById('end-time').value;
        const timeStep = parseInt(document.getElementById('time-step').value);
        
        if (!startTime || !endTime) {
            this.showToast('请设置开始和结束时间', 'error');
            return;
        }
        
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/propagate/sgp4`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    tle: this.currentTLE,
                    start_time: startTime + ':00Z',
                    end_time: endTime + ':00Z',
                    time_step: timeStep
                })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                this.currentEphemeris = data.ephemeris;
                this.displayOrbitResults(data);
                this.updateEphemerisStatus();
                this.showToast(`成功计算${data.count}个轨道点`);
            } else {
                this.showToast(data.error || '轨道计算失败', 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
            console.error('轨道计算错误:', error);
        } finally {
            this.showLoading(false);
        }
    }
    
    displayOrbitResults(data) {
        const resultsDiv = document.getElementById('orbit-results');
        resultsDiv.innerHTML = `
            <h4>SGP4传播结果</h4>
            <p><strong>卫星:</strong> ${data.satellite_name}</p>
            <p><strong>数据点数:</strong> ${data.count}</p>
            <p><strong>时间范围:</strong> ${data.ephemeris[0].time} 至 ${data.ephemeris[data.ephemeris.length-1].time}</p>
            <div class="orbit-stats">
                <p><strong>初始位置:</strong> [${data.ephemeris[0].position.map(x => x.toFixed(3)).join(', ')}] km</p>
                <p><strong>初始速度:</strong> [${data.ephemeris[0].velocity.map(x => x.toFixed(6)).join(', ')}] km/s</p>
            </div>
        `;
        
        // 绘制轨道图表
        this.drawOrbitChart(data.ephemeris);
    }
    
    drawOrbitChart(ephemeris) {
        const canvas = document.getElementById('orbit-chart');
        const ctx = canvas.getContext('2d');
        
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 绘制地球
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const earthRadius = 50;
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, earthRadius, 0, 2 * Math.PI);
        ctx.fillStyle = '#4CAF50';
        ctx.fill();
        ctx.strokeStyle = '#2E7D32';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // 绘制轨道
        if (ephemeris.length > 1) {
            ctx.beginPath();
            ctx.strokeStyle = '#1e3c72';
            ctx.lineWidth = 2;
            
            // 计算缩放因子
            const maxDistance = Math.max(...ephemeris.map(point => 
                Math.sqrt(point.position[0]**2 + point.position[1]**2)
            ));
            const scale = (Math.min(canvas.width, canvas.height) / 2 - 80) / maxDistance;
            
            ephemeris.forEach((point, index) => {
                const x = centerX + point.position[0] * scale;
                const y = centerY + point.position[1] * scale;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
            
            // 标记起始点
            const startPoint = ephemeris[0];
            const startX = centerX + startPoint.position[0] * scale;
            const startY = centerY + startPoint.position[1] * scale;
            
            ctx.beginPath();
            ctx.arc(startX, startY, 5, 0, 2 * Math.PI);
            ctx.fillStyle = '#f44336';
            ctx.fill();
        }
    }
    
    updateEphemerisStatus() {
        const statusDiv = document.getElementById('ephemeris-status');
        if (this.currentEphemeris) {
            statusDiv.textContent = `可用星历数据: ${this.currentEphemeris.length} 个数据点`;
            statusDiv.style.color = '#4CAF50';
            document.getElementById('analyze-topology').disabled = false;
        } else {
            statusDiv.textContent = '无可用星历数据';
            statusDiv.style.color = '#f44336';
            document.getElementById('analyze-topology').disabled = true;
        }
    }
    
    async simulateOrbit() {
        // 获取输入参数
        const posX = parseFloat(document.getElementById('pos-x').value);
        const posY = parseFloat(document.getElementById('pos-y').value);
        const posZ = parseFloat(document.getElementById('pos-z').value);
        const velX = parseFloat(document.getElementById('vel-x').value);
        const velY = parseFloat(document.getElementById('vel-y').value);
        const velZ = parseFloat(document.getElementById('vel-z').value);
        
        const mass = parseFloat(document.getElementById('sat-mass').value);
        const dragArea = parseFloat(document.getElementById('sat-drag-area').value);
        const dragCoeff = parseFloat(document.getElementById('sat-drag-coeff').value);
        
        const includeJ2 = document.getElementById('include-j2').checked;
        const includeDrag = document.getElementById('include-drag').checked;
        const includeSrp = document.getElementById('include-srp').checked;
        
        const startTime = document.getElementById('twin-start-time').value;
        const endTime = document.getElementById('twin-end-time').value;
        const timeStep = parseInt(document.getElementById('twin-time-step').value);
        
        if (!startTime || !endTime) {
            this.showToast('请设置开始和结束时间', 'error');
            return;
        }
        
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/simulate/orbit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    initial_state: {
                        position: [posX, posY, posZ],
                        velocity: [velX, velY, velZ],
                        quaternion: [1, 0, 0, 0],
                        angular_velocity: [0, 0, 0]
                    },
                    satellite: {
                        mass: mass,
                        drag_area: dragArea,
                        drag_coeff: dragCoeff,
                        srp_area: dragArea,
                        srp_coeff: 1.3,
                        name: "数字孪生卫星"
                    },
                    start_time: startTime + ':00Z',
                    end_time: endTime + ':00Z',
                    time_step: timeStep,
                    perturbations: {
                        j2: includeJ2,
                        drag: includeDrag,
                        srp: includeSrp
                    }
                })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                this.currentEphemeris = data.ephemeris;
                this.displayTwinResults(data);
                this.updateEphemerisStatus();
                this.showToast(`仿真完成，生成${data.count}个数据点`);
            } else {
                this.showToast(data.error || '数字孪生仿真失败', 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
            console.error('数字孪生仿真错误:', error);
        } finally {
            this.showLoading(false);
        }
    }
    
    displayTwinResults(data) {
        const resultsDiv = document.getElementById('twin-results');
        resultsDiv.innerHTML = `
            <h4>数字孪生仿真结果</h4>
            <p><strong>卫星:</strong> ${data.satellite_name}</p>
            <p><strong>数据点数:</strong> ${data.count}</p>
            <p><strong>积分步数:</strong> ${data.statistics.integration_steps}</p>
            <p><strong>函数评估次数:</strong> ${data.statistics.function_evaluations}</p>
            <div class="perturbation-info">
                <p><strong>摄动设置:</strong></p>
                <ul>
                    <li>J2摄动: ${data.statistics.include_j2 ? '启用' : '禁用'}</li>
                    <li>大气阻力: ${data.statistics.include_drag ? '启用' : '禁用'}</li>
                    <li>太阳辐射压: ${data.statistics.include_srp ? '启用' : '禁用'}</li>
                </ul>
            </div>
        `;
        
        // 绘制仿真结果图表
        this.drawTwinChart(data.ephemeris);
    }
    
    drawTwinChart(ephemeris) {
        // 复用轨道图表绘制逻辑
        this.drawOrbitChart(ephemeris);
    }
    
    async analyzeTopology() {
        if (!this.currentEphemeris) {
            this.showToast('请先生成星历数据', 'error');
            return;
        }
        
        this.showLoading(true);
        
        try {
            // 这里调用拓扑分析API
            // 由于API还在开发中，先显示占位信息
            setTimeout(() => {
                const resultsDiv = document.getElementById('topology-results');
                resultsDiv.innerHTML = `
                    <h4>拓扑分析结果</h4>
                    <p><strong>状态:</strong> 拓扑分析功能正在开发中</p>
                    <p><strong>星历数据:</strong> ${this.currentEphemeris.length} 个数据点</p>
                    <p><strong>最大链路距离:</strong> ${document.getElementById('max-link-distance').value} km</p>
                    <p><strong>地球半径:</strong> ${document.getElementById('earth-radius').value} km</p>
                `;
                
                this.showLoading(false);
                this.showToast('拓扑分析功能正在开发中');
            }, 1000);
        } catch (error) {
            this.showToast('拓扑分析错误: ' + error.message, 'error');
            this.showLoading(false);
        }
    }
    
    initializeWebSocket() {
        this.updateStatus('connection-status', '未连接', 'disconnected');
    }
    
    connectWebSocket() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.showToast('WebSocket已连接');
            return;
        }
        
        try {
            this.websocket = new WebSocket(this.wsUrl);
            
            this.websocket.onopen = () => {
                this.updateStatus('connection-status', 'WebSocket已连接', 'connected');
                this.showToast('WebSocket连接成功');
                this.logRealTimeMessage('WebSocket连接已建立');
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.websocket.onclose = () => {
                this.updateStatus('connection-status', 'WebSocket已断开', 'disconnected');
                this.logRealTimeMessage('WebSocket连接已断开');
            };
            
            this.websocket.onerror = (error) => {
                this.updateStatus('connection-status', 'WebSocket错误', 'disconnected');
                this.showToast('WebSocket连接错误', 'error');
                console.error('WebSocket错误:', error);
            };
            
            this.updateStatus('connection-status', 'WebSocket连接中...', 'connecting');
            
        } catch (error) {
            this.showToast('WebSocket连接失败: ' + error.message, 'error');
            console.error('WebSocket连接失败:', error);
        }
    }
    
    handleWebSocketMessage(data) {
        this.logRealTimeMessage(`收到消息: ${data.type}`);
        
        switch (data.type) {
            case 'welcome':
                this.logRealTimeMessage(data.message);
                break;
            case 'system_status':
                this.logRealTimeMessage(`系统状态: Node=${data.status.node}, Flask=${data.status.flask}, 客户端=${data.status.clients}`);
                break;
            case 'simulation_completed':
                this.logRealTimeMessage('仿真完成');
                break;
            case 'error':
                this.logRealTimeMessage(`错误: ${data.message}`);
                break;
            default:
                this.logRealTimeMessage(`未知消息类型: ${data.type}`);
        }
    }
    
    logRealTimeMessage(message) {
        const logContainer = document.getElementById('realtime-log');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.textContent = `[${timestamp}] ${message}`;
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new SDTApp();
});
