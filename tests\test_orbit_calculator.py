import pytest
import math
from path.orbit_calculator import OrbitParameters, SatelliteState, OrbitCalculator
from path.coordinate_system import Vector3D

@pytest.fixture
def orbit_calc():
    return OrbitCalculator()

@pytest.fixture
def sample_leo_orbit():
    """A sample circular Low Earth Orbit."""
    return OrbitParameters(
        semi_major_axis=6878e3,  # ~500 km altitude
        eccentricity=0.0001,
        inclination=math.radians(51.6),
        raan=math.radians(0),
        argument_of_perigee=math.radians(0),
        true_anomaly=math.radians(0),
        epoch=2451545.0  # J2000.0
    )

def test_kepler_to_cartesian_and_back(orbit_calc, sample_leo_orbit):
    """
    Test the round-trip consistency between Keplerian elements and Cartesian state.
    """
    # 1. Convert original Keplerian elements to a Cartesian state vector
    initial_state = orbit_calc.kepler_to_cartesian(sample_leo_orbit)

    assert isinstance(initial_state, SatelliteState)
    assert isinstance(initial_state.position, Vector3D)
    assert isinstance(initial_state.velocity, Vector3D)
    
    # 2. Convert the Cartesian state vector back to Keplerian elements
    converted_orbit_params = orbit_calc.cartesian_to_kepler(initial_state)

    # 3. Compare the original and converted elements
    # Using approx for floating point comparisons
    assert converted_orbit_params.semi_major_axis == pytest.approx(sample_leo_orbit.semi_major_axis, rel=1e-6)
    assert converted_orbit_params.eccentricity == pytest.approx(sample_leo_orbit.eccentricity, abs=1e-6)
    assert converted_orbit_params.inclination == pytest.approx(sample_leo_orbit.inclination, abs=1e-6)
    # Angles can be tricky, especially near zero, so check with care
    assert converted_orbit_params.raan == pytest.approx(sample_leo_orbit.raan, abs=1e-6)
    assert converted_orbit_params.argument_of_perigee == pytest.approx(sample_leo_orbit.argument_of_perigee, abs=1e-6)
    assert converted_orbit_params.true_anomaly == pytest.approx(sample_leo_orbit.true_anomaly, abs=1e-6)

def test_orbit_propagation(orbit_calc, sample_leo_orbit):
    """
    Test that orbit propagation changes the satellite's state.
    """
    initial_state = orbit_calc.kepler_to_cartesian(sample_leo_orbit)
    
    # Propagate forward by a quarter of the orbit period
    period = sample_leo_orbit.period
    target_time_seconds = period / 4.0
    target_time_julian = initial_state.time + (target_time_seconds / 86400.0)
    
    propagated_state = orbit_calc.propagate_orbit(initial_state, target_time_julian)
    
    # The new position should be different from the initial one
    assert propagated_state.position.x != pytest.approx(initial_state.position.x)
    assert propagated_state.position.y != pytest.approx(initial_state.position.y)
    
    # The distance from Earth's center (magnitude) should be roughly the same for a near-circular orbit
    assert propagated_state.position.magnitude() == pytest.approx(initial_state.position.magnitude(), rel=1e-3)
    
    # The time should be updated
    assert propagated_state.time == pytest.approx(target_time_julian) 