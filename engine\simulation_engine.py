"""
仿真引擎模块

整合所有组件的主控制器，提供完整的离散事件仿真功能。
"""

import time
from typing import Dict, Any, Optional, Callable, List
from enum import Enum

from .event import Event, EventType, EventPriority
from .scheduler import EventScheduler
from .time_manager import TimeManager
from .event_handler import EventHandler


class SimulationState(Enum):
    """仿真状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"


class SimulationEngine:
    """
    仿真引擎
    
    整合事件调度器、时间管理器和事件处理器，提供完整的仿真控制功能。
    """
    
    def __init__(self, 
                 max_simulation_time: float = 1000.0,
                 max_events: int = 10000,
                 time_step: float = 0.001):
        """
        初始化仿真引擎
        
        Args:
            max_simulation_time: 最大仿真时间
            max_events: 最大事件数
            time_step: 最小时间步长
        """
        # 核心组件
        self.scheduler = EventScheduler(max_queue_size=max_events)
        self.time_manager = TimeManager()
        self.event_handler = EventHandler()
        
        # 仿真参数
        self.max_simulation_time = max_simulation_time
        self.max_events = max_events
        self.time_step = time_step
        
        # 仿真状态
        self.state = SimulationState.IDLE
        self.start_real_time = None
        self.end_real_time = None
        
        # 仿真配置
        self.config = {
            'auto_stop_on_empty_queue': True,
            'enable_real_time_sync': False,
            'real_time_factor': 1.0,
            'log_events': False,
            'event_log_file': None
        }
        
        # 统计信息
        self.simulation_stats = {
            'total_events_processed': 0,
            'total_events_scheduled': 0,
            'total_events_cancelled': 0,
            'simulation_runs': 0,
            'total_simulation_time': 0.0,
            'total_real_time': 0.0
        }
        
        # 回调函数
        self.callbacks = {
            'on_start': [],
            'on_stop': [],
            'on_pause': [],
            'on_resume': [],
            'on_event_processed': [],
            'on_time_advance': [],
            'on_error': []
        }
        
        # 注册默认事件处理器
        self._register_scheduler_handlers()
    
    def run(self, 
            start_time: float = 0.0,
            end_time: Optional[float] = None) -> bool:
        """
        运行仿真
        
        Args:
            start_time: 开始时间
            end_time: 结束时间（None表示使用max_simulation_time）
            
        Returns:
            bool: 仿真是否成功完成
        """
        if self.state == SimulationState.RUNNING:
            return False
        
        # 初始化
        end_time = end_time or self.max_simulation_time
        self.time_manager.reset(start_time)
        self.state = SimulationState.RUNNING
        self.start_real_time = time.time()
        
        # 触发开始回调
        self._trigger_callbacks('on_start')
        
        try:
            # 主仿真循环
            while (self.state == SimulationState.RUNNING and 
                   self.time_manager.get_current_time() < end_time):
                
                # 获取下一个事件
                next_event = self.scheduler.get_next_event()
                
                if not next_event:
                    if self.config['auto_stop_on_empty_queue']:
                        break
                    else:
                        # 推进时间到下一个时间步
                        new_time = self.time_manager.get_current_time() + self.time_step
                        if new_time > end_time:
                            break
                        self.time_manager.advance_time(new_time)
                        continue
                
                # 检查事件时间
                if next_event.scheduled_time > end_time:
                    break
                
                # 推进时间到事件时间
                if next_event.scheduled_time > self.time_manager.get_current_time():
                    self.time_manager.advance_time(next_event.scheduled_time)
                    self._trigger_callbacks('on_time_advance')
                
                # 处理事件
                processed_event = self.scheduler.process_next_event(
                    self.time_manager.get_current_time()
                )
                
                if processed_event:
                    self.simulation_stats['total_events_processed'] += 1
                    self._trigger_callbacks('on_event_processed', processed_event)
                    
                    # 记录事件日志
                    if self.config['log_events']:
                        self._log_event(processed_event)
                
                # 实时同步
                if self.config['enable_real_time_sync']:
                    self._sync_real_time()
            
            # 仿真完成
            self.state = SimulationState.STOPPED
            self.end_real_time = time.time()
            
            # 更新统计信息
            self._update_final_statistics()
            
            # 触发停止回调
            self._trigger_callbacks('on_stop')
            
            return True
            
        except Exception as e:
            self.state = SimulationState.ERROR
            self._trigger_callbacks('on_error', e)
            print(f"仿真出错: {e}")
            return False
    
    def pause(self):
        """暂停仿真"""
        if self.state == SimulationState.RUNNING:
            self.state = SimulationState.PAUSED
            self.time_manager.pause()
            self._trigger_callbacks('on_pause')
    
    def resume(self):
        """恢复仿真"""
        if self.state == SimulationState.PAUSED:
            self.state = SimulationState.RUNNING
            self.time_manager.resume()
            self._trigger_callbacks('on_resume')
    
    def stop(self):
        """停止仿真"""
        if self.state in [SimulationState.RUNNING, SimulationState.PAUSED]:
            self.state = SimulationState.STOPPED
            self.end_real_time = time.time()
            self._update_final_statistics()
            self._trigger_callbacks('on_stop')
    
    def reset(self):
        """重置仿真引擎"""
        self.state = SimulationState.IDLE
        self.scheduler.clear_all()
        self.time_manager.reset()
        self.event_handler.reset_statistics()
        self.start_real_time = None
        self.end_real_time = None
    
    def schedule_event(self, event: Event) -> bool:
        """调度事件"""
        success = self.scheduler.schedule_event(event)
        if success:
            self.simulation_stats['total_events_scheduled'] += 1
        return success
    
    def schedule_event_at(self,
                         event_id: str,
                         event_type: EventType,
                         scheduled_time: float,
                         priority: EventPriority = EventPriority.NORMAL,
                         data: Optional[Dict[str, Any]] = None) -> bool:
        """在指定时间调度事件"""
        return self.schedule_event(
            Event(event_id, event_type, scheduled_time, priority, data)
        )
    
    def schedule_event_after(self,
                           event_id: str,
                           event_type: EventType,
                           delay: float,
                           priority: EventPriority = EventPriority.NORMAL,
                           data: Optional[Dict[str, Any]] = None) -> bool:
        """延迟调度事件"""
        scheduled_time = self.time_manager.get_current_time() + delay
        return self.schedule_event_at(
            event_id, event_type, scheduled_time, priority, data
        )
    
    def cancel_event(self, event_id: str) -> bool:
        """取消事件"""
        success = self.scheduler.cancel_event(event_id)
        if success:
            self.simulation_stats['total_events_cancelled'] += 1
        return success
    
    def register_event_handler(self, event_type: EventType, handler: Callable):
        """注册事件处理器"""
        self.event_handler.register_handler(event_type, handler)
        self.scheduler.register_handler(event_type, self.event_handler.handle_event)
    
    def add_callback(self, event_name: str, callback: Callable):
        """添加回调函数"""
        if event_name in self.callbacks:
            self.callbacks[event_name].append(callback)
    
    def remove_callback(self, event_name: str, callback: Callable):
        """移除回调函数"""
        if event_name in self.callbacks and callback in self.callbacks[event_name]:
            self.callbacks[event_name].remove(callback)
    
    def set_config(self, key: str, value: Any):
        """设置配置参数"""
        if key in self.config:
            self.config[key] = value
    
    def get_config(self, key: str) -> Any:
        """获取配置参数"""
        return self.config.get(key)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取完整统计信息"""
        return {
            'simulation': self.simulation_stats.copy(),
            'scheduler': self.scheduler.get_statistics(),
            'time_manager': self.time_manager.get_time_statistics(),
            'event_handler': self.event_handler.get_statistics(),
            'current_state': self.state.value,
            'current_time': self.time_manager.get_current_time(),
            'queue_size': self.scheduler.get_queue_size()
        }
    
    def get_state(self) -> SimulationState:
        """获取当前仿真状态"""
        return self.state
    
    def get_current_time(self) -> float:
        """获取当前仿真时间"""
        return self.time_manager.get_current_time()
    
    def _register_scheduler_handlers(self):
        """注册调度器处理器"""
        for event_type in EventType:
            self.scheduler.register_handler(
                event_type, 
                self.event_handler.handle_event
            )
    
    def _trigger_callbacks(self, event_name: str, *args):
        """触发回调函数"""
        for callback in self.callbacks.get(event_name, []):
            try:
                callback(*args)
            except Exception as e:
                print(f"回调函数执行错误: {e}")
    
    def _sync_real_time(self):
        """实时同步"""
        if not self.start_real_time:
            return
        
        real_elapsed = time.time() - self.start_real_time
        sim_elapsed = self.time_manager.get_elapsed_time()
        target_sim_time = real_elapsed * self.config['real_time_factor']
        
        if sim_elapsed > target_sim_time:
            # 仿真时间超前，等待
            sleep_time = (sim_elapsed - target_sim_time) / self.config['real_time_factor']
            time.sleep(sleep_time)
    
    def _log_event(self, event: Event):
        """记录事件日志"""
        if self.config['event_log_file']:
            try:
                with open(self.config['event_log_file'], 'a', encoding='utf-8') as f:
                    f.write(f"{self.time_manager.get_current_time()}: {event}\n")
            except Exception as e:
                print(f"事件日志记录失败: {e}")
    
    def _update_final_statistics(self):
        """更新最终统计信息"""
        self.simulation_stats['simulation_runs'] += 1
        self.simulation_stats['total_simulation_time'] += self.time_manager.get_elapsed_time()
        
        if self.start_real_time and self.end_real_time:
            self.simulation_stats['total_real_time'] += (
                self.end_real_time - self.start_real_time
            )
    
    def __str__(self):
        return (f"SimulationEngine(state={self.state.value}, "
                f"time={self.time_manager.get_current_time():.6f}, "
                f"events={self.scheduler.get_queue_size()})")
    
    def __repr__(self):
        return self.__str__() 