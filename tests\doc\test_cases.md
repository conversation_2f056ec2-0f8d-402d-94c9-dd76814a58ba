# SDT卫星数字孪生系统 测试用例 (V1.0)

*本文档遵循《软件测试制度V1.0》进行编写。*

## 1. 单元测试用例 (Unit Test Cases)

### 1.1. `utils/config_manager.py`

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-UNIT-UTIL-001 | `ConfigManager` | 测试加载有效的JSON配置文件 | 高 | 创建一个有效的`config.json`文件，内容为`{"sat_count": 5}` | 1. 实例化`ConfigManager`。 2. 调用`get_config()`方法。 | 返回一个包含`{"sat_count": 5}`的字典。 |
| TC-UNIT-UTIL-002 | `ConfigManager` | 测试加载不存在的配置文件 | 高 | 确保`config.json`文件不存在 | 1. 实例化`ConfigManager`。 | 抛出`FileNotFoundError`异常。 |
| TC-UNIT-UTIL-003 | `ConfigManager` | 测试加载格式错误的JSON文件 | 中 | 创建一个语法错误的`config.json`，内容为`{"key":,}` | 1. 实例化`ConfigManager`。 | 抛出`json.JSONDecodeError`异常。 |

### 1.2. `core/satellite_path_calculator.py`

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-UNIT-CORE-001 | `SatellitePathCalculator` | 测试标准轨道参数下的路径计算 | 高 | 提供一组标准的卫星轨道六要素（TLE）。 | 1. 实例化`SatellitePathCalculator`。 2. 输入TLE数据。 3. 计算未来10分钟的轨道点。 | 生成一系列符合物理规律的坐标点，无错误或异常。 |
| TC-UNIT-CORE-002 | `SatellitePathCalculator` | 测试无效轨道参数 | 中 | 提供一组无效的TLE数据（如轨道周期为负）。 | 1. 实例化`SatellitePathCalculator`。 2. 输入无效的TLE数据。 | 应该能处理异常并返回错误信息或抛出特定异常。 |
| TC-UNIT-CORE-003 | `SatellitePathCalculator` | 测试零时间间隔的计算 | 低 | 设置时间步长为0。 | 1. 实例化`SatellitePathCalculator`。 2. 计算路径。 | 返回单个轨道点或空列表，程序不应崩溃。 |

### 1.3. `utils/logger.py` (新增)

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-UNIT-UTIL-004 | `Logger` | 测试日志是否能正确写入文件 | 高 | 配置了有效的日志文件路径。 | 1. 初始化Logger。 2. 调用`info()`方法记录一条信息。 | 日志文件中出现对应的INFO级别日志。 |
| TC-UNIT-UTIL-005 | `Logger` | 测试不同日志级别是否生效 | 中 | 无 | 1. 设置日志级别为WARNING。 2. 分别调用`info()`和`warning()`。 | 日志文件中只记录WARNING级别日志，不记录INFO级别。 |

---

## 2. 集成测试用例 (Integration Test Cases)

### 2.1. `satellite_simulator` 与 `satellite_path_calculator`

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-INT-CORE-001 | 模拟器与路径计算器 | 测试模拟器能否正确使用路径计算器的结果 | 高 | `config.json`配置了有效的卫星参数。 | 1. 启动`sdt_main.py`。 2. 运行仿真。 3. `satellite_simulator`模块请求轨道数据。 | `satellite_simulator`获取`satellite_path_calculator`计算出的轨道点，并在模拟循环中正确更新卫星位置。 |

### 2.2. `communication_module` 与 `physical_layer_simulator`

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-INT-COMM-001 | 通信与物理层 | 测试消息通过物理层模拟后的收发 | 高 | 两颗卫星进入通信范围。 | 1. 卫星A向卫星B发送消息。 2. `communication_module`调用`physical_layer_simulator`模拟信号传输（包括延迟、丢包等）。 | 卫星B能接收到经过物理层影响（可能延迟或失真）的消息。 |

### 2.3. `core` 模块与 `gui` 模块集成 (新增)

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-INT-CORE-GUI-001 | 核心模块与GUI | 测试仿真数据能否驱动GUI更新 | 高 | GUI已启动，仿真正在运行。 | 1. `satellite_simulator`更新一颗卫星的位置。 2. `visualization_module`获取更新后的数据。 | GUI窗口中对应的卫星图标移动到新位置。 |

---

## 3. 系统测试用例 (System Test Cases)

### 3.1. 端到端仿真流程

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-SYS-E2E-001 | 全系统 | 完整仿真场景测试 | 高 | `config.json`配置了包含3颗以上卫星的场景，并设置了仿真时长。 | 1. 从命令行运行 `python sdt_main.py`。 2. 让仿真完整运行。 3. 观察日志输出和最终结果。 | 1. 系统无崩溃或严重错误。 2. 日志文件`logs/sdt_*.log`被创建并记录了仿真过程。 3. 如果有可视化，应能看到卫星运动轨迹。 |

### 3.2. 配置变更测试

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-SYS-CONF-001 | 全系统 | 修改`config.json`中的卫星数量 | 中 | 无 | 1. 复制`config.json`为`config_more.json`。 2. 修改`config_more.json`，将卫星数量增加到10。 3. 使用 `python sdt_main.py --config config_more.json` 启动程序。 | 系统应根据新配置初始化10颗卫星进行仿真，而不是默认数量。 |

### 3.3. 性能测试 (新增)

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-SYS-PERF-001 | 全系统 | 100颗卫星规模下长时间运行稳定性 | 高 | `config.json`配置100颗卫星。 | 1. 启动仿真。 2. 持续运行24小时。 3. 监控内存和CPU使用率。 | 系统不崩溃，内存占用无明显泄露。 |

---

## 4. UI/GUI 测试用例 (UI/GUI Test Cases)

### 4.1. `gui/visualization_module.py`

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-GUI-VIS-001 | GUI可视化 | 卫星可视化显示 | 高 | 仿真正在运行，且至少有一颗卫星。 | 1. 启动带GUI的仿真。 2. 观察可视化窗口。 | 窗口中应能看到代表卫星的图标，并且图标会根据仿真数据移动。 |
| TC-GUI-VIS-002 | GUI可视化 | GUI窗口缩放功能 | 中 | GUI窗口已打开。 | 1. 用鼠标拖动窗口边缘，放大或缩小窗口。 | 窗口内的2D/3D视图和控件应能自适应缩放，布局不混乱。 |

### 4.2. `frontend/index.html`

| 用例ID | 所属模块 | 用例标题/目的 | 优先级 | 前置条件 | 操作步骤与输入数据 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| TC-FRONT-DISP-001 | Web前端 | Web前端页面加载 | 高 | 启动支持Web前端的后端服务。 | 1. 在浏览器中打开`frontend/index.html`。 | 页面能正常加载，显示标题、画布区域和控制按钮，无乱码或布局错误。 |
| TC-FRONT-CTRL-001 | Web前端 | "开始"按钮功能 | 高 | 页面加载完成。 | 1. 点击"开始仿真"按钮。 | 后端应收到开始仿真的请求，并且前端界面开始显示仿真数据（如卫星移动）。 |
| TC-FRONT-CTRL-002 | Web前端 | "暂停"按钮功能 | 高 | 仿真正在运行。 | 1. 点击"暂停仿真"按钮。 | 后端仿真暂停，前端界面上的卫星停止移动。 |
| TC-FRONT-CTRL-003 | Web前端 | "停止"按钮功能 | 高 | 仿真正在运行。 | 1. 点击"停止仿真"按钮。 | 后端仿真停止，前端界面恢复到初始状态。 |