import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Typography,
  message,
  Modal,
  Form,
  Tooltip,
  Avatar,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UserOutlined
} from '@ant-design/icons';

import { issueAPI } from '../../services/api';

const { Title } = Typography;
const { Option } = Select;

const Issues = () => {
  const [loading, setLoading] = useState(false);
  const [issues, setIssues] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [filters, setFilters] = useState({
    keyword: '',
    type: '',
    priority: '',
    status: '',
    assignee: ''
  });

  useEffect(() => {
    loadIssues();
  }, [pagination.current, pagination.pageSize, filters]);

  const loadIssues = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        size: pagination.pageSize,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value)
        )
      };

      const response = await issueAPI.getList(params);
      if (response.data.success) {
        const { issues: issueList, pagination: paginationData } = response.data.data;
        setIssues(issueList);
        setPagination(prev => ({
          ...prev,
          total: paginationData.total
        }));
      }
    } catch (error) {
      message.error('加载问题列表失败');
      console.error('Load issues error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (paginationData) => {
    setPagination(prev => ({
      ...prev,
      current: paginationData.current,
      pageSize: paginationData.pageSize
    }));
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
  };

  const handleDelete = async (id) => {
    try {
      const response = await issueAPI.delete(id);
      if (response.data.success) {
        message.success('删除成功');
        loadIssues();
      }
    } catch (error) {
      message.error('删除失败');
      console.error('Delete issue error:', error);
    }
  };

  const getStatusColor = (status) => {
    const colorMap = {
      'open': 'blue',
      'in_progress': 'orange',
      'resolved': 'green',
      'closed': 'default',
      'rejected': 'red'
    };
    return colorMap[status] || 'default';
  };

  const getPriorityColor = (priority) => {
    const colorMap = {
      'low': 'green',
      'medium': 'blue',
      'high': 'orange',
      'critical': 'red'
    };
    return colorMap[priority] || 'default';
  };

  const getTypeColor = (type) => {
    const colorMap = {
      'bug': 'red',
      'feature': 'blue',
      'task': 'green',
      'improvement': 'purple',
      'question': 'orange'
    };
    return colorMap[type] || 'default';
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      render: (text) => (
        <span style={{ fontFamily: 'monospace' }}>
          {text.slice(-8)}
        </span>
      )
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (text, record) => (
        <Tooltip title={text}>
          <a onClick={() => {/* TODO: 查看详情 */}}>
            {text}
          </a>
        </Tooltip>
      )
    },
    {
      title: '类型',
      dataIndex: 'type_name',
      key: 'type',
      width: 80,
      render: (text, record) => (
        <Tag color={getTypeColor(record.type)}>
          {text}
        </Tag>
      )
    },
    {
      title: '优先级',
      dataIndex: 'priority_name',
      key: 'priority',
      width: 80,
      render: (text, record) => (
        <Tag color={getPriorityColor(record.priority)}>
          {text}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status_name',
      key: 'status',
      width: 80,
      render: (text, record) => (
        <Tag color={getStatusColor(record.status)}>
          {text}
        </Tag>
      )
    },
    {
      title: '分配人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 100,
      render: (assignee) => (
        assignee ? (
          <div className="avatar-with-name">
            <Avatar size="small" icon={<UserOutlined />} />
            <span>{assignee}</span>
          </div>
        ) : (
          <span style={{ color: '#999' }}>未分配</span>
        )
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (text) => (
        text ? new Date(text).toLocaleDateString() : '-'
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {/* TODO: 查看详情 */}}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {/* TODO: 编辑 */}}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个问题吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="issues-container">
      <div className="page-header">
        <Title level={2}>问题管理</Title>
      </div>

      <Card className="mb-16">
        <Space wrap style={{ marginBottom: 16 }}>
          <Input
            placeholder="搜索问题..."
            prefix={<SearchOutlined />}
            value={filters.keyword}
            onChange={(e) => handleFilterChange('keyword', e.target.value)}
            style={{ width: 200 }}
            allowClear
          />
          
          <Select
            placeholder="类型"
            value={filters.type}
            onChange={(value) => handleFilterChange('type', value)}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="bug">缺陷</Option>
            <Option value="feature">功能需求</Option>
            <Option value="task">任务</Option>
            <Option value="improvement">改进</Option>
            <Option value="question">疑问</Option>
          </Select>

          <Select
            placeholder="优先级"
            value={filters.priority}
            onChange={(value) => handleFilterChange('priority', value)}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="low">低</Option>
            <Option value="medium">中</Option>
            <Option value="high">高</Option>
            <Option value="critical">紧急</Option>
          </Select>

          <Select
            placeholder="状态"
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="open">待处理</Option>
            <Option value="in_progress">处理中</Option>
            <Option value="resolved">已解决</Option>
            <Option value="closed">已关闭</Option>
            <Option value="rejected">已拒绝</Option>
          </Select>

          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {/* TODO: 创建问题 */}}
          >
            创建问题
          </Button>
        </Space>

        <Table
          columns={columns}
          dataSource={issues}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          onChange={handleTableChange}
          className="table-container"
        />
      </Card>
    </div>
  );
};

export default Issues;
