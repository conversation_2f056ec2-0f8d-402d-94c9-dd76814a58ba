import pytest
from path.link_manager import LinkManager, LinkState, LinkQuality

@pytest.fixture
def lm():
    """Pytest fixture for LinkManager."""
    return LinkManager()

def test_link_creation_and_destruction(lm):
    """Test creating and destroying a link."""
    assert len(lm.links) == 0
    
    # Create a link
    link_id = lm.create_link("nodeA", "nodeB", "satellite_to_satellite")
    assert link_id is not None
    assert link_id in lm.links
    assert len(lm.links) == 1
    assert "nodeA" in lm.node_links
    assert link_id in lm.node_links["nodeA"]
    
    link = lm.links[link_id]
    assert link.state == LinkState.ESTABLISHING
    
    # Destroy the link
    assert lm.destroy_link(link_id)
    assert link_id not in lm.links
    assert len(lm.links) == 0
    assert link_id not in lm.node_links["nodeA"]
    assert lm.stats['total_links_created'] == 1
    assert lm.stats['total_links_destroyed'] == 1

def test_get_link_helpers(lm):
    """Test helper functions for retrieving links."""
    lm.create_link("node1", "node2", "t1")
    lm.create_link("node1", "node3", "t2")
    
    assert len(lm.get_node_links("node1")) == 2
    assert len(lm.get_node_links("node2")) == 1
    assert len(lm.get_node_links("node4")) == 0
    
    link = lm.get_link_between_nodes("node2", "node1") # Order shouldn't matter
    assert link is not None
    assert link.link_id == "node1_node2" # ID is deterministic

def test_link_quality_score():
    """Test the quality score calculation."""
    # A good quality link
    good_quality = LinkQuality(
        signal_strength=-60.0,
        snr=25.0,
        bit_error_rate=1e-7,
        latency=20.0,
        packet_loss=0.01,
        bandwidth=1e9
    )
    assert 0.8 < good_quality.quality_score <= 1.0

    # A bad quality link
    bad_quality = LinkQuality(
        signal_strength=-110.0,
        snr=5.0,
        bit_error_rate=1e-3,
        latency=800.0,
        packet_loss=8.0,
        bandwidth=1e6
    )
    assert 0.0 <= bad_quality.quality_score < 0.3 