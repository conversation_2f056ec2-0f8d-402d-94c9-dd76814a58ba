#!/usr/bin/env python3
"""
SDT管理系统项目初始化脚本
用于快速创建项目目录结构和基础文件
"""

import os
import json
from pathlib import Path

def create_directory_structure():
    """创建项目目录结构"""
    directories = [
        # 后端目录
        "backend/app/models",
        "backend/app/api",
        "backend/app/services",
        "backend/app/utils",
        "backend/app/auth",
        "backend/tests",
        
        # 前端目录
        "frontend/public",
        "frontend/src/components/common",
        "frontend/src/components/layout",
        "frontend/src/components/forms",
        "frontend/src/pages/Login",
        "frontend/src/pages/Dashboard",
        "frontend/src/pages/Issues",
        "frontend/src/pages/Documents",
        "frontend/src/pages/Products",
        "frontend/src/pages/Users",
        "frontend/src/services",
        "frontend/src/store",
        "frontend/src/utils",
        "frontend/src/assets/images",
        "frontend/src/assets/styles",
        
        # 存储目录
        "storage/documents",
        "storage/attachments/issues",
        "storage/attachments/comments",
        "storage/products/releases",
        "storage/products/patches",
        "storage/temp/uploads",
        
        # 文档目录
        "docs/api",
        "docs/deployment",
        "docs/user_guide",
        
        # 配置目录
        "config",
        
        # 日志目录
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")

def create_backend_files():
    """创建后端基础文件"""
    
    # requirements.txt
    requirements_content = """# Flask核心框架
Flask==2.3.3
Flask-CORS==4.0.0
Flask-JWT-Extended==4.5.3

# Redis客户端
redis==5.0.1

# 数据序列化
marshmallow==3.20.1

# 文件处理
Werkzeug==2.3.7

# 定时任务
APScheduler==3.10.4

# 开发工具
python-dotenv==1.0.0

# 测试工具
pytest==7.4.2
pytest-flask==1.2.0
"""
    
    with open("backend/requirements.txt", "w", encoding="utf-8") as f:
        f.write(requirements_content)
    
    # config.py
    config_content = """import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Redis配置
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    UPLOAD_FOLDER = 'storage'
    ALLOWED_EXTENSIONS = {
        'documents': {'pdf', 'doc', 'docx', 'txt', 'md'},
        'images': {'png', 'jpg', 'jpeg', 'gif'},
        'archives': {'zip', 'tar', 'gz', '7z'}
    }
    
    # 分页配置
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False
    
class TestingConfig(Config):
    TESTING = True
    REDIS_URL = 'redis://localhost:6379/1'  # 使用不同的数据库

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
"""
    
    with open("backend/config.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    # run.py
    run_content = """from app import create_app
import os

app = create_app(os.getenv('FLASK_ENV') or 'default')

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
"""
    
    with open("backend/run.py", "w", encoding="utf-8") as f:
        f.write(run_content)
    
    # app/__init__.py
    app_init_content = """from flask import Flask
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from config import config

def create_app(config_name):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    CORS(app)
    jwt = JWTManager(app)
    
    # 注册蓝图
    from app.api import auth_bp, users_bp, issues_bp, documents_bp, products_bp
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(users_bp, url_prefix='/api/users')
    app.register_blueprint(issues_bp, url_prefix='/api/issues')
    app.register_blueprint(documents_bp, url_prefix='/api/documents')
    app.register_blueprint(products_bp, url_prefix='/api/products')
    
    return app
"""
    
    with open("backend/app/__init__.py", "w", encoding="utf-8") as f:
        f.write(app_init_content)
    
    print("Created backend files")

def create_frontend_files():
    """创建前端基础文件"""
    
    # package.json
    package_json = {
        "name": "sdt-manager-frontend",
        "version": "1.0.0",
        "description": "SDT管理系统前端",
        "main": "src/index.js",
        "scripts": {
            "start": "webpack serve --mode development",
            "build": "webpack --mode production",
            "test": "jest",
            "lint": "eslint src --ext .js,.jsx"
        },
        "dependencies": {
            "react": "^18.2.0",
            "react-dom": "^18.2.0",
            "react-router-dom": "^6.15.0",
            "antd": "^5.9.0",
            "@reduxjs/toolkit": "^1.9.5",
            "react-redux": "^8.1.2",
            "axios": "^1.5.0",
            "dayjs": "^1.11.9",
            "@ant-design/icons": "^5.2.6"
        },
        "devDependencies": {
            "webpack": "^5.88.0",
            "webpack-cli": "^5.1.0",
            "webpack-dev-server": "^4.15.0",
            "@babel/core": "^7.22.0",
            "@babel/preset-env": "^7.22.0",
            "@babel/preset-react": "^7.22.0",
            "babel-loader": "^9.1.0",
            "css-loader": "^6.8.0",
            "style-loader": "^3.3.0",
            "html-webpack-plugin": "^5.5.0",
            "eslint": "^8.47.0",
            "eslint-plugin-react": "^7.33.0",
            "jest": "^29.6.0",
            "@testing-library/react": "^13.4.0"
        }
    }
    
    with open("frontend/package.json", "w", encoding="utf-8") as f:
        json.dump(package_json, f, indent=2, ensure_ascii=False)
    
    # webpack.config.js
    webpack_config = """const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'bundle.js',
    publicPath: '/'
  },
  module: {
    rules: [
      {
        test: /\\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react']
          }
        }
      },
      {
        test: /\\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/index.html'
    })
  ],
  devServer: {
    historyApiFallback: true,
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true
      }
    }
  }
};
"""
    
    with open("frontend/webpack.config.js", "w", encoding="utf-8") as f:
        f.write(webpack_config)
    
    # public/index.html
    html_content = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDT管理系统</title>
</head>
<body>
    <div id="root"></div>
</body>
</html>
"""
    
    with open("frontend/public/index.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    # src/index.js
    index_js = """import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { store } from './store';
import App from './App';
import 'antd/dist/reset.css';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <ConfigProvider locale={zhCN}>
          <App />
        </ConfigProvider>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>
);
"""
    
    with open("frontend/src/index.js", "w", encoding="utf-8") as f:
        f.write(index_js)
    
    print("Created frontend files")

def create_docker_files():
    """创建Docker配置文件"""
    
    # docker-compose.yml
    docker_compose = """version: '3.8'

services:
  redis:
    image: redis:6-alpine
    container_name: sdt-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  backend:
    build: ./backend
    container_name: sdt-backend
    ports:
      - "5000:5000"
    depends_on:
      - redis
    environment:
      - FLASK_ENV=production
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs

  frontend:
    build: ./frontend
    container_name: sdt-frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  redis_data:
"""
    
    with open("docker-compose.yml", "w", encoding="utf-8") as f:
        f.write(docker_compose)
    
    # backend/Dockerfile
    backend_dockerfile = """FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p storage logs

EXPOSE 5000

CMD ["python", "run.py"]
"""
    
    with open("backend/Dockerfile", "w", encoding="utf-8") as f:
        f.write(backend_dockerfile)
    
    # frontend/Dockerfile
    frontend_dockerfile = """# 构建阶段
FROM node:16-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
"""
    
    with open("frontend/Dockerfile", "w", encoding="utf-8") as f:
        f.write(frontend_dockerfile)
    
    print("Created Docker files")

def create_readme():
    """创建README文件"""
    readme_content = """# SDT管理系统

基于Flask + React + Redis的管理系统，用于缺陷问题管理、文档库管理、产品库管理。

## 快速开始

### 开发环境要求
- Python 3.8+
- Node.js 16+
- Redis 6.0+

### 安装和运行

1. **后端启动**
```bash
cd backend
pip install -r requirements.txt
python run.py
```

2. **前端启动**
```bash
cd frontend
npm install
npm start
```

3. **Redis启动**
```bash
redis-server
```

### Docker部署

```bash
docker-compose up -d
```

## 项目结构

```
managersystem/
├── backend/           # Flask后端
├── frontend/          # React前端
├── storage/           # 文件存储
├── docs/             # 项目文档
└── docker-compose.yml # Docker配置
```

## 功能模块

- **缺陷问题管理**: Bug跟踪、任务分配、状态管理
- **文档库管理**: 文档上传、分类、版本管理
- **产品库管理**: 产品版本、发布管理
- **用户权限管理**: 用户认证、角色权限

## 技术栈

- **后端**: Flask, Redis, JWT
- **前端**: React, Ant Design, Redux
- **部署**: Docker, Nginx

## 开发指南

详细的开发指南请参考：
- [系统设计方案](SYSTEM_DESIGN.md)
- [实现计划](IMPLEMENTATION_PLAN.md)

## 许可证

仅供学习和研究使用。
"""
    
    with open("README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("Created README.md")

def main():
    """主函数"""
    print("开始初始化SDT管理系统项目...")
    
    try:
        create_directory_structure()
        create_backend_files()
        create_frontend_files()
        create_docker_files()
        create_readme()
        
        print("\\n项目初始化完成！")
        print("\\n下一步操作：")
        print("1. cd backend && pip install -r requirements.txt")
        print("2. cd frontend && npm install")
        print("3. 启动Redis服务")
        print("4. python backend/run.py (启动后端)")
        print("5. npm start (在frontend目录下启动前端)")
        print("\\n或者使用Docker：")
        print("docker-compose up -d")
        
    except Exception as e:
        print(f"初始化过程中出现错误: {e}")

if __name__ == "__main__":
    main()
