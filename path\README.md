# SDT卫星数字孪生系统 - 路径控制模块

这是一个完全使用纯Python实现的卫星路径控制模块，专为SDT卫星数字孪生系统设计，不依赖任何外部库。

## 主要功能

- **坐标系统转换**: 支持各种坐标系统间的转换（ECEF、ECI、地理坐标等）
- **轨道计算**: 卫星轨道预测、传播和分析
- **路径规划**: 多节点路径规划，支持A*、Dijkstra等算法
- **链路管理**: 动态链路质量监控和管理
- **路径优化**: 多目标路径优化算法
- **路径预测**: 基于轨道力学的网络状态预测

## 模块结构

```
path/
├── __init__.py              # 模块初始化
├── coordinate_system.py     # 坐标系统和向量运算
├── orbit_calculator.py      # 轨道计算器
├── path_planner.py         # 路径规划器
├── link_manager.py         # 链路管理器
├── optimization.py         # 路径优化器
├── path_predictor.py       # 路径预测器
├── examples.py             # 使用示例
├── test_path.py           # 测试脚本
└── README.md              # 说明文档
```

## 快速开始

### 基本使用

```python
from path import (
    CoordinateSystem, Vector3D, LatLonAlt,
    OrbitCalculator, OrbitParameters,
    PathPlanner, LinkManager,
    PathOptimizer, PathPredictor
)

# 1. 坐标转换
beijing = LatLonAlt(39.9042, 116.4074, 50.0)
beijing_ecef = CoordinateSystem.lla_to_ecef(beijing)

# 2. 轨道计算
calc = OrbitCalculator()
orbit = OrbitParameters(
    semi_major_axis=6778137.0,  # 400km高度
    eccentricity=0.001,
    inclination=math.radians(98.7),
    raan=0.0,
    argument_of_perigee=0.0,
    true_anomaly=0.0,
    epoch=2460000.0
)
satellite_state = calc.kepler_to_cartesian(orbit)

# 3. 路径规划
planner = PathPlanner()
# ... 添加节点和连接
path = planner.plan_path("start_node", "end_node", "astar")

# 4. 链路管理
link_mgr = LinkManager()
link_id = link_mgr.create_link("sat1", "sat2", "satellite_to_satellite")
link_mgr.update_link_quality(link_id, pos1, pos2)
```

### 运行示例

```bash
# 运行所有示例
python -m path.examples

# 运行测试
python -m path.test_path
```

## 核心组件

### 1. 坐标系统 (coordinate_system.py)

提供三维向量运算和坐标系统转换功能：

```python
# 向量运算
v1 = Vector3D(1000, 2000, 3000)
v2 = Vector3D(500, 1500, 2500)
distance = v1.distance_to(v2)
angle = v1.angle_to(v2)

# 坐标转换
lla = LatLonAlt(39.9042, 116.4074, 50.0)  # 北京
ecef = CoordinateSystem.lla_to_ecef(lla)   # 转ECEF
back = CoordinateSystem.ecef_to_lla(ecef)  # 反向转换
```

### 2. 轨道计算器 (orbit_calculator.py)

卫星轨道计算和预测：

```python
calc = OrbitCalculator()

# 轨道参数
orbit = OrbitParameters(
    semi_major_axis=6778137.0,
    eccentricity=0.001,
    inclination=math.radians(98.7),
    # ... 其他参数
)

# 计算卫星状态
state = calc.kepler_to_cartesian(orbit)
print(f"高度: {state.altitude/1000:.1f} km")
print(f"速度: {state.speed/1000:.2f} km/s")

# 轨道传播
future_time = orbit.epoch + 90/1440  # 90分钟后
future_state = calc.propagate_orbit(state, future_time)

# 地面轨迹
ground_track = calc.calculate_ground_track(orbit, 3600, 300)

# 可见性分析
ground_station = LatLonAlt(39.9042, 116.4074, 50)
visible, elevation, azimuth = calc.calculate_visibility(
    state, ground_station, min_elevation=10.0
)
```

### 3. 路径规划器 (path_planner.py)

多节点路径规划：

```python
planner = PathPlanner()

# 更新网络拓扑
planner.update_topology(satellite_states, ground_stations)

# 路径规划
path = planner.plan_path("sat_0", "gs_0", "astar")

if path.success:
    print(f"跳数: {path.hop_count}")
    print(f"总距离: {path.total_distance/1000:.1f} km")
    print(f"总延迟: {path.total_delay*1000:.2f} ms")
    print(f"平均质量: {path.average_quality:.3f}")

# 多路径搜索
multiple_paths = planner.find_multiple_paths("sat_0", "gs_0", k=3)
```

### 4. 链路管理器 (link_manager.py)

动态链路质量管理：

```python
link_mgr = LinkManager()

# 创建链路
link_id = link_mgr.create_link("sat1", "sat2", "satellite_to_satellite")

# 更新链路质量
link_mgr.update_link_quality(link_id, sat1_pos, sat2_pos)

# 获取链路信息
link = link_mgr.links[link_id]
print(f"信号强度: {link.quality.signal_strength:.1f} dBm")
print(f"信噪比: {link.quality.snr:.1f} dB")
print(f"延迟: {link.quality.latency:.2f} ms")
print(f"带宽: {link.quality.bandwidth/1e9:.2f} Gbps")
print(f"质量评分: {link.quality.quality_score:.3f}")

# 获取活跃链路
active_links = link_mgr.get_active_links()

# 统计信息
stats = link_mgr.get_statistics()
```

### 5. 路径优化器 (optimization.py)

多目标路径优化：

```python
optimizer = PathOptimizer()

# 定义优化准则
criteria = OptimizationCriteria(
    minimize_delay=1.0,
    maximize_bandwidth=0.8,
    minimize_hops=1.2,
    maximize_reliability=0.9,
    minimize_cost=1.1
)

# 执行优化
result = optimizer.optimize_path(original_path, criteria)

if result.success:
    print(f"改进: {result.improvement_percentage:.2f}%")
    print(f"原始延迟: {result.original_path.total_delay*1000:.2f} ms")
    print(f"优化延迟: {result.optimized_path.total_delay*1000:.2f} ms")
```

### 6. 路径预测器 (path_predictor.py)

网络状态预测：

```python
predictor = PathPredictor()

# 卫星位置预测
future_time = current_time + 30/1440  # 30分钟后
predicted_states = predictor.predict_satellite_positions(
    satellite_states, future_time
)

# 链路质量预测
predicted_quality = predictor.predict_link_quality(
    distance=1000000, link_type="satellite_to_satellite"
)

# 拓扑变化预测
topology_changes = predictor.predict_topology_changes(
    satellite_states, ground_stations, future_time
)
```

## 性能特征

- **零外部依赖**: 完全使用Python标准库实现
- **高效算法**: A*路径搜索 O(log n)，堆优化的事件调度
- **实时性能**: 支持大规模卫星网络的实时计算
- **数值精度**: 使用高精度算法，满足航天应用需求

## 主要算法

- **轨道力学**: 开普勒轨道要素转换、二体问题轨道传播
- **路径搜索**: A*、Dijkstra、广度优先搜索
- **信号模型**: 自由空间路径损耗、Shannon容量公式
- **优化算法**: 局部搜索、贪心优化
- **坐标转换**: WGS84椭球模型、精确的地理坐标转换

## 测试和验证

### 运行测试

```bash
# 运行完整测试套件
python -m path.test_path

# 运行性能测试
python -c "from path.test_path import run_performance_test; run_performance_test()"
```

### 测试覆盖

- 单元测试: 每个模块的核心功能
- 集成测试: 模块间协作测试
- 性能测试: 大规模数据处理能力
- 精度测试: 数值计算精度验证

## 应用场景

- **卫星通信网络**: 动态路由和链路管理
- **卫星导航系统**: 星座优化和覆盖分析
- **空间任务规划**: 轨道设计和可见性分析
- **网络仿真**: 大规模卫星网络性能分析

## 扩展功能

模块设计具有良好的可扩展性，可以轻松添加：

- 更复杂的轨道扰动模型
- 高级优化算法（遗传算法、粒子群等）
- 多层网络拓扑支持
- 实时数据接口
- 可视化功能

## 注意事项

1. **坐标系统**: 所有计算基于WGS84地球模型
2. **时间系统**: 使用儒略日表示时间
3. **单位约定**: 距离(米)、时间(秒)、角度(弧度)
4. **数值精度**: 位置精度约1米，时间精度约1秒

## 许可证

本模块为SDT卫星数字孪生系统的一部分，版权归开发团队所有。

## 开发信息

- **版本**: 1.0.0
- **开发团队**: SDT Development Team
- **开发语言**: Python 3.7+
- **代码行数**: 约3000行
- **文档**: 完整的API文档和示例

## 联系方式

如有问题或建议，请联系开发团队。 