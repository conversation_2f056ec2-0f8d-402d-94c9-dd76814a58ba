#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数字孪生模块测试
测试状态向量、卫星属性和数值传播功能
"""

import pytest
import numpy as np
from datetime import datetime, timezone

from modules.digital_twin import (
    StateVector, StateVectorDType, Satellite, NumericalPropagator,
    J2Perturbation, AtmosphericDrag, SolarRadiationPressure
)


class TestStateVector:
    """状态向量测试"""
    
    def test_state_vector_creation(self):
        """测试状态向量创建"""
        position = np.array([7000.0, 0.0, 0.0])
        velocity = np.array([0.0, 7.5, 0.0])
        quaternion = np.array([1.0, 0.0, 0.0, 0.0])
        angular_velocity = np.array([0.01, 0.02, 0.03])
        
        state = StateVector(position, velocity, quaternion, angular_velocity)
        
        assert np.allclose(state.position, position)
        assert np.allclose(state.velocity, velocity)
        assert np.allclose(state.quaternion, quaternion)
        assert np.allclose(state.angular_velocity, angular_velocity)
    
    def test_state_vector_validation(self):
        """测试状态向量验证"""
        # 测试四元数自动归一化
        position = np.array([7000.0, 0.0, 0.0])
        velocity = np.array([0.0, 7.5, 0.0])
        quaternion = np.array([2.0, 0.0, 0.0, 0.0])  # 未归一化
        
        state = StateVector(position, velocity, quaternion)
        
        # 四元数应该被自动归一化
        assert abs(np.linalg.norm(state.quaternion) - 1.0) < 1e-10
    
    def test_state_vector_array_conversion(self):
        """测试状态向量数组转换"""
        state = StateVector(
            position=np.array([7000.0, 0.0, 0.0]),
            velocity=np.array([0.0, 7.5, 0.0]),
            quaternion=np.array([1.0, 0.0, 0.0, 0.0]),
            angular_velocity=np.array([0.01, 0.02, 0.03])
        )
        
        # 转换为一维数组
        flat_array = state.to_flat_array()
        assert len(flat_array) == 13
        assert np.allclose(flat_array[:3], state.position)
        assert np.allclose(flat_array[3:6], state.velocity)
        
        # 从一维数组创建
        state2 = StateVector.from_flat_array(flat_array)
        assert np.allclose(state2.position, state.position)
        assert np.allclose(state2.velocity, state.velocity)
    
    def test_orbital_elements_calculation(self):
        """测试轨道要素计算"""
        # ISS轨道参数
        state = StateVector(
            position=np.array([6678.0, 0.0, 0.0]),  # km
            velocity=np.array([0.0, 7.727, 0.0])    # km/s
        )
        
        elements = state.get_orbital_elements()
        
        # 检查半长轴（应该接近6678km）
        assert abs(elements['semi_major_axis'] - 6678.0) < 10.0
        
        # 检查偏心率（应该接近0）
        assert elements['eccentricity'] < 0.01
        
        # 检查轨道周期（应该约90分钟）
        assert 5000 < elements['orbital_period'] < 6000  # 秒


class TestSatellite:
    """卫星类测试"""
    
    def test_satellite_creation(self):
        """测试卫星对象创建"""
        satellite = Satellite(
            mass=1000.0,
            drag_area=10.0,
            drag_coeff=2.2,
            srp_area=8.0,
            srp_coeff=1.3,
            name="Test Satellite"
        )
        
        assert satellite.mass == 1000.0
        assert satellite.drag_area == 10.0
        assert satellite.name == "Test Satellite"
    
    def test_satellite_validation(self):
        """测试卫星属性验证"""
        # 测试负质量
        with pytest.raises(ValueError, match="卫星质量必须大于0"):
            Satellite(mass=-100.0, drag_area=10.0)
        
        # 测试负面积
        with pytest.raises(ValueError, match="阻力面积不能为负"):
            Satellite(mass=1000.0, drag_area=-10.0)
    
    def test_satellite_properties(self):
        """测试卫星属性计算"""
        satellite = Satellite(
            mass=1000.0,
            drag_area=10.0,
            drag_coeff=2.2
        )
        
        # 弹道系数
        bc = satellite.get_ballistic_coefficient()
        expected_bc = 1000.0 / (2.2 * 10.0)
        assert abs(bc - expected_bc) < 1e-10
        
        # 面积质量比
        amr = satellite.get_area_to_mass_ratio()
        expected_amr = 10.0 / 1000.0
        assert abs(amr - expected_amr) < 1e-10
    
    def test_satellite_mass_update(self):
        """测试卫星质量更新"""
        satellite = Satellite(mass=1000.0, drag_area=10.0)
        original_inertia = satellite.inertia_tensor.copy()
        
        # 更新质量
        satellite.update_mass(800.0)
        
        assert satellite.mass == 800.0
        
        # 惯量张量应该按比例缩放
        expected_inertia = original_inertia * (800.0 / 1000.0)
        assert np.allclose(satellite.inertia_tensor, expected_inertia)
    
    def test_cubesat_creation(self):
        """测试CubeSat创建"""
        cubesat = Satellite.create_cubesat(mass=1.33, side_length=0.1, name="1U CubeSat")
        
        assert cubesat.mass == 1.33
        assert cubesat.drag_area == 0.01  # 0.1 * 0.1
        assert cubesat.name == "1U CubeSat"
        
        # 检查惯量张量
        expected_I = 1.33 * 0.1**2 / 6.0
        assert abs(cubesat.inertia_tensor[0, 0] - expected_I) < 1e-10
    
    def test_spherical_satellite_creation(self):
        """测试球形卫星创建"""
        sphere_sat = Satellite.create_spherical_satellite(mass=100.0, radius=0.5, name="Sphere Sat")
        
        assert sphere_sat.mass == 100.0
        assert abs(sphere_sat.drag_area - np.pi * 0.5**2) < 1e-10
        assert sphere_sat.name == "Sphere Sat"


class TestPerturbations:
    """摄动力测试"""
    
    def test_j2_perturbation(self):
        """测试J2摄动计算"""
        # ISS轨道位置
        position = np.array([6678.0, 0.0, 0.0])  # km
        
        a_j2 = J2Perturbation.calculate(position)
        
        # J2摄动应该是非零的
        assert np.linalg.norm(a_j2) > 0
        
        # 对于赤道轨道，Z方向摄动应该为0
        assert abs(a_j2[2]) < 1e-10
    
    def test_atmospheric_drag(self):
        """测试大气阻力计算"""
        satellite = Satellite(mass=1000.0, drag_area=10.0, drag_coeff=2.2)
        
        # 低轨道位置和速度
        position = np.array([6678.0, 0.0, 0.0])  # km
        velocity = np.array([0.0, 7.727, 0.0])   # km/s
        jd_utc = 2459945.5  # 2023-01-01
        
        a_drag = AtmosphericDrag.calculate(position, velocity, satellite, jd_utc)
        
        # 阻力应该与速度方向相反
        v_norm = velocity / np.linalg.norm(velocity)
        a_drag_norm = a_drag / np.linalg.norm(a_drag)
        
        # 阻力方向应该与速度方向相反（点积为负）
        assert np.dot(v_norm, a_drag_norm) < 0
    
    def test_solar_radiation_pressure(self):
        """测试太阳辐射压计算"""
        satellite = Satellite(mass=1000.0, drag_area=10.0, srp_area=10.0, srp_coeff=1.3)
        
        # 日照侧位置
        position = np.array([6678.0, 0.0, 0.0])  # km
        jd_utc = 2459945.5  # 2023-01-01
        
        a_srp = SolarRadiationPressure.calculate(position, satellite, jd_utc)
        
        # 太阳辐射压应该是非零的
        assert np.linalg.norm(a_srp) > 0
    
    def test_perturbations_high_altitude(self):
        """测试高轨道摄动"""
        satellite = Satellite(mass=1000.0, drag_area=10.0)
        
        # 地球同步轨道高度
        position = np.array([42164.0, 0.0, 0.0])  # km
        velocity = np.array([0.0, 3.074, 0.0])    # km/s
        jd_utc = 2459945.5
        
        # 高轨道应该没有大气阻力
        a_drag = AtmosphericDrag.calculate(position, velocity, satellite, jd_utc)
        assert np.linalg.norm(a_drag) < 1e-15
        
        # 但应该有J2摄动
        a_j2 = J2Perturbation.calculate(position)
        assert np.linalg.norm(a_j2) > 0


class TestNumericalPropagator:
    """数值传播器测试"""
    
    def setup_method(self):
        """测试设置"""
        self.satellite = Satellite(mass=1000.0, drag_area=10.0, drag_coeff=2.2)
        self.propagator = NumericalPropagator()
        
        # ISS轨道初始状态
        self.initial_state = StateVector(
            position=np.array([6678.0, 0.0, 0.0]),
            velocity=np.array([0.0, 7.727, 0.0]),
            quaternion=np.array([1.0, 0.0, 0.0, 0.0]),
            angular_velocity=np.array([0.0, 0.0, 0.0])
        )
    
    def test_propagator_initialization(self):
        """测试传播器初始化"""
        propagator = NumericalPropagator(
            include_j2=True,
            include_drag=False,
            include_srp=False
        )
        
        assert propagator.include_j2 == True
        assert propagator.include_drag == False
        assert propagator.include_srp == False
    
    def test_short_propagation(self):
        """测试短时间传播"""
        start_jd = 2459945.5  # 2023-01-01 00:00:00
        end_jd = start_jd + 1.0/24.0  # 1小时后
        
        ephemeris = self.propagator.propagate(
            self.initial_state,
            self.satellite,
            start_jd,
            end_jd,
            time_step_sec=60.0
        )
        
        # 应该有61个数据点（0-60分钟，每分钟一个点）
        assert len(ephemeris) == 61
        
        # 检查能量守恒（近似）
        initial_energy = self.initial_state.get_orbital_energy()
        
        final_state = StateVector.from_structured_array(ephemeris[-1])
        final_energy = final_state.get_orbital_energy()
        
        # 能量变化应该很小（考虑摄动）
        energy_change = abs(final_energy - initial_energy) / abs(initial_energy)
        assert energy_change < 0.01  # 1%以内
    
    def test_orbit_decay_detection(self):
        """测试轨道衰减检测"""
        # 创建一个低轨道状态（容易衰减）
        low_orbit_state = StateVector(
            position=np.array([6400.0, 0.0, 0.0]),  # 很低的轨道
            velocity=np.array([0.0, 8.0, 0.0])
        )
        
        # 高阻力卫星
        high_drag_satellite = Satellite(mass=100.0, drag_area=50.0, drag_coeff=3.0)
        
        start_jd = 2459945.5
        end_jd = start_jd + 10.0  # 10天
        
        ephemeris = self.propagator.propagate(
            low_orbit_state,
            high_drag_satellite,
            start_jd,
            end_jd,
            time_step_sec=3600.0  # 1小时步长
        )
        
        # 轨道应该衰减（星历长度小于预期）
        expected_length = int(10 * 24) + 1  # 10天 * 24小时 + 1
        assert len(ephemeris) < expected_length
    
    def test_propagator_statistics(self):
        """测试传播器统计信息"""
        start_jd = 2459945.5
        end_jd = start_jd + 1.0/24.0  # 1小时
        
        self.propagator.propagate(
            self.initial_state,
            self.satellite,
            start_jd,
            end_jd,
            time_step_sec=300.0  # 5分钟步长
        )
        
        stats = self.propagator.get_statistics()
        
        assert 'integration_steps' in stats
        assert 'function_evaluations' in stats
        assert stats['integration_steps'] > 0
        assert stats['function_evaluations'] > 0
    
    def test_perturbation_effects(self):
        """测试摄动效应"""
        start_jd = 2459945.5
        end_jd = start_jd + 1.0  # 1天
        
        # 只有重力的传播
        prop_gravity_only = NumericalPropagator(
            include_j2=False,
            include_drag=False,
            include_srp=False
        )
        
        # 包含J2摄动的传播
        prop_with_j2 = NumericalPropagator(
            include_j2=True,
            include_drag=False,
            include_srp=False
        )
        
        eph_gravity = prop_gravity_only.propagate(
            self.initial_state, self.satellite, start_jd, end_jd, 3600.0
        )
        
        eph_j2 = prop_with_j2.propagate(
            self.initial_state, self.satellite, start_jd, end_jd, 3600.0
        )
        
        # J2摄动应该导致轨道差异
        final_pos_gravity = eph_gravity[-1]['position']
        final_pos_j2 = eph_j2[-1]['position']
        
        position_diff = np.linalg.norm(final_pos_j2 - final_pos_gravity)
        assert position_diff > 1.0  # 至少1km差异


if __name__ == '__main__':
    pytest.main([__file__])
