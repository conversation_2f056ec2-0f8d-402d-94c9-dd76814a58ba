#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视线算法
判断两个卫星之间的视线是否被地球遮挡
"""

import math
import numpy as np
from typing import Tuple, Optional


def is_los_clear(p1: np.n<PERSON><PERSON>, p2: np.n<PERSON><PERSON>, earth_radius: float = 6378.137) -> bool:
    """
    判断两点间的视线是否被地球遮挡
    
    Args:
        p1: 卫星1的位置向量 [x, y, z] (km)
        p2: 卫星2的位置向量 [x, y, z] (km)
        earth_radius: 地球半径 (km，默认6378.137)
        
    Returns:
        True表示视线通畅，False表示被地球遮挡
    """
    # 输入验证
    if p1.shape != (3,) or p2.shape != (3,):
        raise ValueError("位置向量必须是3维")
    
    # 检查两点是否相同
    if np.allclose(p1, p2, atol=1e-6):
        return True  # 同一点，视线通畅
    
    # 建立二次方程 a*d² + b*d + c = 0
    # 其中d是参数，直线方程为 r(d) = p1 + d*(p2-p1)，d∈[0,1]
    
    v = p2 - p1  # 方向向量
    
    # 二次方程系数
    a = np.dot(v, v)
    b = 2.0 * np.dot(v, p1)
    c = np.dot(p1, p1) - earth_radius * earth_radius
    
    # 计算判别式
    delta = b * b - 4.0 * a * c
    
    # 如果判别式小于0，直线与球体不相交
    if delta < 0:
        return True  # 视线通畅
    
    # 计算两个根
    sqrt_delta = math.sqrt(delta)
    d1 = (-b - sqrt_delta) / (2.0 * a)
    d2 = (-b + sqrt_delta) / (2.0 * a)
    
    # 检查根是否在[0,1]区间内
    # 如果任一根在此区间内，说明线段与球体相交
    
    # 确保d1 <= d2
    if d1 > d2:
        d1, d2 = d2, d1
    
    # 检查交点是否在线段上
    if d2 < 0 or d1 > 1:
        # 所有交点都在线段外
        return True  # 视线通畅
    
    # 至少有一个交点在线段上
    return False  # 视线被遮挡


def compute_los_distance(p1: np.ndarray, p2: np.ndarray, earth_radius: float = 6378.137) -> Optional[float]:
    """
    计算视线距离（如果视线通畅）
    
    Args:
        p1: 卫星1的位置向量 [x, y, z] (km)
        p2: 卫星2的位置向量 [x, y, z] (km)
        earth_radius: 地球半径 (km)
        
    Returns:
        视线距离 (km)，如果被遮挡则返回None
    """
    if is_los_clear(p1, p2, earth_radius):
        return np.linalg.norm(p2 - p1)
    else:
        return None


def compute_elevation_angle(satellite_pos: np.ndarray, ground_station_pos: np.ndarray) -> float:
    """
    计算地面站对卫星的仰角
    
    Args:
        satellite_pos: 卫星位置向量 [x, y, z] (km)
        ground_station_pos: 地面站位置向量 [x, y, z] (km)
        
    Returns:
        仰角 (度)
    """
    # 从地面站指向卫星的向量
    sat_vector = satellite_pos - ground_station_pos
    
    # 地面站的地心向量（指向天顶）
    zenith_vector = ground_station_pos / np.linalg.norm(ground_station_pos)
    
    # 计算仰角
    cos_elevation = np.dot(sat_vector, zenith_vector) / np.linalg.norm(sat_vector)
    
    # 限制在[-1, 1]范围内，避免数值误差
    cos_elevation = np.clip(cos_elevation, -1.0, 1.0)
    
    # 计算仰角（弧度转度）
    elevation_rad = math.asin(cos_elevation)
    elevation_deg = math.degrees(elevation_rad)
    
    return elevation_deg


def compute_azimuth_angle(satellite_pos: np.ndarray, ground_station_pos: np.ndarray,
                         ground_station_lat: float, ground_station_lon: float) -> float:
    """
    计算地面站对卫星的方位角
    
    Args:
        satellite_pos: 卫星位置向量 [x, y, z] (km)
        ground_station_pos: 地面站位置向量 [x, y, z] (km)
        ground_station_lat: 地面站纬度 (度)
        ground_station_lon: 地面站经度 (度)
        
    Returns:
        方位角 (度，从北向东测量)
    """
    # 转换为弧度
    lat_rad = math.radians(ground_station_lat)
    lon_rad = math.radians(ground_station_lon)
    
    # 从地面站指向卫星的向量
    sat_vector = satellite_pos - ground_station_pos
    
    # 构建地面站的本地坐标系
    # 东向单位向量
    east = np.array([-math.sin(lon_rad), math.cos(lon_rad), 0.0])
    
    # 北向单位向量
    north = np.array([
        -math.cos(lon_rad) * math.sin(lat_rad),
        -math.sin(lon_rad) * math.sin(lat_rad),
        math.cos(lat_rad)
    ])
    
    # 计算卫星向量在东向和北向的投影
    east_component = np.dot(sat_vector, east)
    north_component = np.dot(sat_vector, north)
    
    # 计算方位角
    azimuth_rad = math.atan2(east_component, north_component)
    azimuth_deg = math.degrees(azimuth_rad)
    
    # 确保方位角在[0, 360)范围内
    if azimuth_deg < 0:
        azimuth_deg += 360.0
    
    return azimuth_deg


def is_above_horizon(satellite_pos: np.ndarray, ground_station_pos: np.ndarray,
                    min_elevation: float = 5.0) -> bool:
    """
    判断卫星是否在地面站的地平线以上
    
    Args:
        satellite_pos: 卫星位置向量 [x, y, z] (km)
        ground_station_pos: 地面站位置向量 [x, y, z] (km)
        min_elevation: 最小仰角 (度，默认5度)
        
    Returns:
        True表示在地平线以上
    """
    elevation = compute_elevation_angle(satellite_pos, ground_station_pos)
    return elevation >= min_elevation


def compute_range_rate(satellite_pos: np.ndarray, satellite_vel: np.ndarray,
                      ground_station_pos: np.ndarray) -> float:
    """
    计算距离变化率（径向速度）
    
    Args:
        satellite_pos: 卫星位置向量 [x, y, z] (km)
        satellite_vel: 卫星速度向量 [vx, vy, vz] (km/s)
        ground_station_pos: 地面站位置向量 [x, y, z] (km)
        
    Returns:
        距离变化率 (km/s，正值表示远离，负值表示接近)
    """
    # 从地面站指向卫星的向量
    range_vector = satellite_pos - ground_station_pos
    range_distance = np.linalg.norm(range_vector)
    
    if range_distance < 1e-10:
        return 0.0
    
    # 单位距离向量
    unit_range = range_vector / range_distance
    
    # 径向速度分量
    range_rate = np.dot(satellite_vel, unit_range)
    
    return range_rate


def compute_doppler_shift(satellite_pos: np.ndarray, satellite_vel: np.ndarray,
                         ground_station_pos: np.ndarray, frequency: float) -> float:
    """
    计算多普勒频移
    
    Args:
        satellite_pos: 卫星位置向量 [x, y, z] (km)
        satellite_vel: 卫星速度向量 [vx, vy, vz] (km/s)
        ground_station_pos: 地面站位置向量 [x, y, z] (km)
        frequency: 载波频率 (Hz)
        
    Returns:
        多普勒频移 (Hz)
    """
    # 光速 (km/s)
    c = 299792.458
    
    # 计算径向速度
    range_rate = compute_range_rate(satellite_pos, satellite_vel, ground_station_pos)
    
    # 多普勒频移公式: Δf = -f * (v_r / c)
    doppler_shift = -frequency * range_rate / c
    
    return doppler_shift


class LOSCalculator:
    """
    视线计算器类
    提供批量视线计算功能
    """
    
    def __init__(self, earth_radius: float = 6378.137):
        """
        初始化视线计算器
        
        Args:
            earth_radius: 地球半径 (km)
        """
        self.earth_radius = earth_radius
    
    def compute_visibility_matrix(self, positions: np.ndarray) -> np.ndarray:
        """
        计算可见性矩阵
        
        Args:
            positions: 位置数组，形状为 (N, 3)，N为卫星数量
            
        Returns:
            可见性矩阵，形状为 (N, N)，1表示可见，0表示不可见
        """
        N = positions.shape[0]
        visibility_matrix = np.zeros((N, N), dtype=int)
        
        for i in range(N):
            for j in range(i + 1, N):
                if is_los_clear(positions[i], positions[j], self.earth_radius):
                    visibility_matrix[i, j] = 1
                    visibility_matrix[j, i] = 1
        
        # 对角线设为1（自己对自己可见）
        np.fill_diagonal(visibility_matrix, 1)
        
        return visibility_matrix
    
    def compute_distance_matrix(self, positions: np.ndarray) -> np.ndarray:
        """
        计算距离矩阵
        
        Args:
            positions: 位置数组，形状为 (N, 3)
            
        Returns:
            距离矩阵，形状为 (N, N)，单位为km
        """
        N = positions.shape[0]
        distance_matrix = np.zeros((N, N))
        
        for i in range(N):
            for j in range(i + 1, N):
                distance = np.linalg.norm(positions[i] - positions[j])
                distance_matrix[i, j] = distance
                distance_matrix[j, i] = distance
        
        return distance_matrix
