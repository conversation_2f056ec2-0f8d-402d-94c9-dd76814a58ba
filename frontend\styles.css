* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
}

/* 顶部工具栏 */
.toolbar {
    background-color: #2a2a2a;
    height: 30px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 10px;
    border-bottom: 1px solid #404040;
}

.toolbar-icons {
    display: flex;
    gap: 8px;
}

.icon {
    font-size: 14px;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 2px;
}

.icon:hover {
    background-color: #404040;
}

/* 主标题 */
.main-title {
    background-color: #2a2a2a;
    color: #ffffff;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 8px 0;
    border-bottom: 1px solid #404040;
}

/* 主容器 */
.main-container {
    display: flex;
    height: calc(100vh - 130px);
}

/* 左侧面板 */
.left-panel {
    width: 300px;
    background-color: #2a2a2a;
    border-right: 1px solid #404040;
    padding: 15px;
    overflow-y: auto;
}

/* 右侧面板 */
.right-panel {
    width: 300px;
    background-color: #2a2a2a;
    border-left: 1px solid #404040;
    padding: 15px;
    overflow-y: auto;
}

/* 中间视图区域 */
.center-view {
    flex: 1;
    background-color: #000000;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

#earthCanvas {
    border: 1px solid #404040;
    border-radius: 8px;
    background: radial-gradient(circle at 30% 30%, #001122, #000000);
}

/* 控制组 */
.control-group {
    background-color: #3a3a3a;
    border: 1px solid #505050;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 15px;
}

.control-group h3 {
    color: #ffffff;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
}

.control-group h4 {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 8px;
}

/* 按钮样式 */
.btn {
    background-color: #4a4a4a;
    color: #ffffff;
    border: 1px solid #606060;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn:hover {
    background-color: #5a5a5a;
    border-color: #707070;
}

.btn-primary {
    background-color: #0066cc;
    border-color: #0066cc;
}

.btn-primary:hover {
    background-color: #0077dd;
    border-color: #0077dd;
}

.btn-secondary {
    background-color: #404040;
    border-color: #505050;
}

.btn-run {
    background-color: #00aa00;
    border-color: #00aa00;
    color: white;
    font-weight: bold;
}

.btn-run:hover {
    background-color: #00bb00;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
}

/* 按钮组 */
.button-group {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.button-row {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

/* 下拉选择框 */
select {
    background-color: #4a4a4a;
    color: #ffffff;
    border: 1px solid #606060;
    border-radius: 4px;
    padding: 6px;
    font-size: 12px;
    width: 100%;
    margin-bottom: 8px;
}

.constellation-select {
    width: 100%;
}

.dropdown-row {
    display: flex;
    gap: 8px;
}

.path-select {
    flex: 1;
    margin-bottom: 0;
}

/* 顶部标签按钮 */
.top-buttons {
    display: flex;
    margin-bottom: 15px;
}

.tab-btn {
    flex: 1;
    background-color: #404040;
    color: #cccccc;
    border: 1px solid #505050;
    padding: 8px 4px;
    font-size: 12px;
    cursor: pointer;
    border-radius: 0;
}

.tab-btn:first-child {
    border-radius: 4px 0 0 4px;
}

.tab-btn:last-child {
    border-radius: 0 4px 4px 0;
}

.tab-btn.active {
    background-color: #0066cc;
    color: #ffffff;
    border-color: #0066cc;
}

.tab-btn:hover:not(.active) {
    background-color: #505050;
}

/* 状态行 */
.status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
}

.status-value {
    color: #ff6666;
    font-weight: bold;
}

/* 终端相关 */
.terminal-row {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;
}

.terminal-select {
    flex: 1;
    margin-bottom: 0;
}

.terminal-output {
    background-color: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 8px;
    min-height: 60px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
}

.output-text {
    color: #888888;
    font-style: italic;
}

/* 信息区域 */
.info-area {
    background-color: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 8px;
    min-height: 100px;
    margin-bottom: 8px;
}

/* 底部时间轴 */
.timeline {
    background-color: #2a2a2a;
    border-top: 1px solid #404040;
    padding: 8px 15px;
    height: 50px;
}

.time-markers {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #cccccc;
    margin-bottom: 5px;
}

.time-progress {
    background-color: #404040;
    height: 4px;
    border-radius: 2px;
    position: relative;
}

.progress-bar {
    background-color: #0066cc;
    height: 100%;
    width: 30%;
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2a2a2a;
}

::-webkit-scrollbar-thumb {
    background: #505050;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #606060;
} 