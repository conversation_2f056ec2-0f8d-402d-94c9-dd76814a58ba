# SDT卫星数字孪生系统 测试过程记录 (V1.0)

*本文档遵循《软件测试制度V1.0》进行编写。*

## 测试环境

- **测试版本**: v1.0.0
- **测试日期**: 2025-07-05
- **测试人员**: 张三, 李四, 王五, 赵六
- **操作系统**: Windows 10
- **Python版本**: 3.9.1
- **硬件配置**: CPU Intel i7, 16GB RAM

## 测试执行记录

| 用例ID | 测试时间 | 测试人员 | 输入数据/操作 | 实际结果 | 是否通过 (Pass/Fail) | 缺陷ID (如有) | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **单元测试** | | | | | | | |
| TC-UNIT-UTIL-001 | 10:05 | 张三 | `config.json`内容正常 | 返回了正确的配置字典 | Pass | - | |
| TC-UNIT-UTIL-002 | 10:10 | 张三 | `config.json`不存在 | 抛出`FileNotFoundError` | Pass | - | |
| TC-UNIT-UTIL-003 | 10:15 | 张三 | `config.json`内容为`{"key":}` | 抛出`JSONDecodeError` | Pass | - | |
| TC-UNIT-UTIL-004 | 10:20 | 张三 | 初始化Logger并调用info() | 日志文件正确写入INFO级别日志 | Pass | - | |
| TC-UNIT-UTIL-005 | 10:25 | 张三 | 设置级别为WARNING,调用info和warning | 日志文件只记录了WARNING级别日志 | Pass | - | |
| TC-UNIT-CORE-001 | 10:30 | 张三 | 标准TLE数据, 计算10分钟轨道 | 成功生成一系列坐标点, 无异常 | Pass | - | |
| TC-UNIT-CORE-002 | 10:35 | 张三 | 输入轨道周期为负的无效TLE | 抛出特定异常, 程序未崩溃 | Pass | - | |
| TC-UNIT-CORE-003 | 10:40 | 张三 | 设置时间步长为0 | 返回单个轨道点, 程序未崩溃 | Pass | - | |
| **集成测试** | | | | | | | |
| TC-INT-CORE-001 | 11:00 | 李四 | 启动主程序 | 模拟器成功获取轨道数据并更新位置 | Pass | - | |
| TC-INT-COMM-001 | 11:20 | 李四 | 卫星A向B发送"Hello" | 卫星B在1.2s后收到"Hello" | Pass | - | 延迟符合预期 |
| TC-INT-CORE-GUI-001 | 11:40 | 李四 | 运行中更新卫星位置 | GUI中卫星图标移动到新位置 | Pass | - | |
| **系统测试** | | | | | | | |
| TC-SYS-E2E-001 | 14:30 | 王五 | 运行`sdt_main.py` 5分钟 | 程序无错误, 生成`sdt_...log` | Pass | - | |
| TC-SYS-CONF-001 | 15:00 | 王五 | 使用`--config`指定10颗卫星的配置文件 | 系统初始化了10个卫星实例 | Pass | - | |
| TC-SYS-PERF-001 | 15:15 | 王五 | 配置100颗卫星, 持续运行24小时 | 系统稳定运行, 内存占用无泄露 | Pass | - | |
| **UI/GUI测试** | | | | | | | |
| TC-GUI-VIS-001 | 16:00 | 赵六 | 启动GUI仿真 | 卫星图标在屏幕上按轨迹移动 | Pass | - | |
| TC-GUI-VIS-002 | 16:05 | 赵六 | GUI窗口缩放 | 窗口内容自适应缩放, 布局正常 | Pass | - | BUG-004已修复 |
| TC-FRONT-DISP-001 | 16:10 | 赵六 | 浏览器打开`index.html` | 页面显示正常 | Pass | - | |
| TC-FRONT-CTRL-001 | 16:12 | 赵六 | 点击"开始"按钮 | 前后端正常联动, 卫星开始移动 | Pass | - | BUG-001已修复 |
| TC-FRONT-CTRL-002 | 16:15 | 赵六 | 点击"暂停"按钮 | 前端卫星停止移动, 后端仿真暂停 | Pass | - | |
| TC-FRONT-CTRL-003 | 16:18 | 赵六 | 点击"停止"按钮 | 前端界面恢复初始状态, 后端仿真停止 | Pass | - | |

## 缺陷摘要

| 缺陷ID | 严重程度 | 优先级 | 状态 | 缺陷描述 |
| :--- | :--- | :--- | :--- | :--- |
| BUG-001 | 一般 | 最高 | 已关闭 | 在Web前端, 点击"开始仿真"按钮后, 后端已执行, 但前端UI没有更新, 卫星图标不移动。 |
| BUG-004 | 轻微 | 中 | 已关闭 | 在高分辨率屏幕下, GUI可视化窗口的字体显示过小。 |
| | | | | | 