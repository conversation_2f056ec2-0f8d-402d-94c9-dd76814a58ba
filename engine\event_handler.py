"""
事件处理器模块

定义事件处理的基础框架和默认处理器。
"""

from typing import Dict, Callable, Any, Optional
from .event import Event, EventType


class EventHandler:
    """
    事件处理器基类
    
    提供事件处理的基础框架和默认实现。
    """
    
    def __init__(self):
        """初始化事件处理器"""
        self.handlers = {}
        self.statistics = {
            'processed_events': 0,
            'failed_events': 0,
            'events_by_type': {}
        }
        
        # 注册默认处理器
        self._register_default_handlers()
    
    def handle_event(self, event: Event) -> bool:
        """
        处理事件
        
        Args:
            event: 要处理的事件
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 查找处理器
            handler = self.handlers.get(event.event_type)
            
            if handler:
                result = handler(event)
                success = result is not False
            else:
                # 使用默认处理器
                success = self._default_handler(event)
            
            # 更新统计
            self._update_statistics(event, success)
            
            return success
            
        except Exception as e:
            print(f"事件处理异常: {e}")
            self._update_statistics(event, False)
            return False
    
    def register_handler(self, event_type: EventType, handler: Callable[[Event], bool]):
        """
        注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理器函数
        """
        self.handlers[event_type] = handler
    
    def unregister_handler(self, event_type: EventType):
        """注销事件处理器"""
        if event_type in self.handlers:
            del self.handlers[event_type]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return self.statistics.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.statistics = {
            'processed_events': 0,
            'failed_events': 0,
            'events_by_type': {}
        }
    
    def _register_default_handlers(self):
        """注册默认处理器"""
        self.handlers[EventType.PACKET_ARRIVAL] = self._handle_packet_arrival
        self.handlers[EventType.PACKET_DEPARTURE] = self._handle_packet_departure
        self.handlers[EventType.PACKET_DROP] = self._handle_packet_drop
        self.handlers[EventType.LINK_UP] = self._handle_link_up
        self.handlers[EventType.LINK_DOWN] = self._handle_link_down
        self.handlers[EventType.ROUTE_UPDATE] = self._handle_route_update
        self.handlers[EventType.TIMEOUT] = self._handle_timeout
        self.handlers[EventType.SIMULATION_END] = self._handle_simulation_end
    
    def _default_handler(self, event: Event) -> bool:
        """默认事件处理器"""
        print(f"处理事件: {event}")
        return True
    
    def _handle_packet_arrival(self, event: Event) -> bool:
        """处理数据包到达事件"""
        print(f"数据包到达: {event.get_data('packet_id')} "
              f"从 {event.source_node} 到 {event.destination_node}")
        return True
    
    def _handle_packet_departure(self, event: Event) -> bool:
        """处理数据包发送事件"""
        print(f"数据包发送: {event.get_data('packet_id')} "
              f"从 {event.source_node} 到 {event.destination_node}")
        return True
    
    def _handle_packet_drop(self, event: Event) -> bool:
        """处理数据包丢弃事件"""
        print(f"数据包丢弃: {event.get_data('packet_id')} "
              f"在节点 {event.source_node}")
        return True
    
    def _handle_link_up(self, event: Event) -> bool:
        """处理链路上线事件"""
        link_id = event.get_data('link_id')
        print(f"链路上线: {link_id} ({event.source_node} - {event.destination_node})")
        return True
    
    def _handle_link_down(self, event: Event) -> bool:
        """处理链路下线事件"""
        link_id = event.get_data('link_id')
        print(f"链路下线: {link_id} ({event.source_node} - {event.destination_node})")
        return True
    
    def _handle_route_update(self, event: Event) -> bool:
        """处理路由更新事件"""
        next_hop = event.get_data('next_hop')
        metric = event.get_data('metric')
        print(f"路由更新: 节点 {event.source_node} 到 {event.destination_node} "
              f"下一跳 {next_hop}, 代价 {metric}")
        return True
    
    def _handle_timeout(self, event: Event) -> bool:
        """处理超时事件"""
        timeout_type = event.get_data('timeout_type', 'unknown')
        print(f"超时事件: {timeout_type} 在节点 {event.source_node}")
        return True
    
    def _handle_simulation_end(self, event: Event) -> bool:
        """处理仿真结束事件"""
        print(f"仿真结束，时间: {event.scheduled_time}")
        return True
    
    def _update_statistics(self, event: Event, success: bool):
        """更新处理统计信息"""
        if success:
            self.statistics['processed_events'] += 1
        else:
            self.statistics['failed_events'] += 1
        
        # 按类型统计
        event_type_name = event.event_type.value
        if event_type_name not in self.statistics['events_by_type']:
            self.statistics['events_by_type'][event_type_name] = {
                'processed': 0,
                'failed': 0
            }
        
        if success:
            self.statistics['events_by_type'][event_type_name]['processed'] += 1
        else:
            self.statistics['events_by_type'][event_type_name]['failed'] += 1 