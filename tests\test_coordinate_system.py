import pytest
import math
from path.coordinate_system import Vector3D, LatLonAlt, CoordinateSystem

@pytest.fixture
def vec1():
    return Vector3D(1, 2, 3)

@pytest.fixture
def vec2():
    return Vector3D(4, 5, 6)

def test_vector_operations(vec1, vec2):
    """Test basic 3D vector operations."""
    # Addition and Subtraction
    assert (vec1 + vec2) == Vector3D(5, 7, 9)
    assert (vec2 - vec1) == Vector3D(3, 3, 3)
    
    # Scalar multiplication and division
    assert (vec1 * 2) == Vector3D(2, 4, 6)
    assert (vec2 / 2) == Vector3D(2, 2.5, 3)
    
    # Magnitude
    assert vec1.magnitude() == pytest.approx(math.sqrt(14))
    
    # Dot product
    assert vec1.dot(vec2) == 32 # 1*4 + 2*5 + 3*6
    
    # Cross product
    assert vec1.cross(vec2) == Vector3D(-3, 6, -3)

def test_lla_to_ecef_to_lla_consistency():
    """Test that converting LLA to ECEF and back is consistent."""
    # A sample location (e.g., somewhere in Paris)
    original_lla = LatLonAlt(latitude=48.8584, longitude=2.2945, altitude=33.0)
    
    # Convert to ECEF
    ecef_vector = CoordinateSystem.lla_to_ecef(original_lla)
    
    # Convert back to LLA
    converted_lla = CoordinateSystem.ecef_to_lla(ecef_vector)
    
    # Check if the values are close (allowing for floating point inaccuracies)
    assert converted_lla.latitude == pytest.approx(original_lla.latitude, abs=1e-6)
    assert converted_lla.longitude == pytest.approx(original_lla.longitude, abs=1e-6)
    assert converted_lla.altitude == pytest.approx(original_lla.altitude, abs=1e-3) # Altitude is less precise

def test_eci_to_ecef_to_eci_consistency():
    """Test that converting ECI to ECEF and back is consistent."""
    # A sample ECI vector and time
    eci_vector = Vector3D(5000e3, 2000e3, 1000e3)
    julian_date = 2451545.0  # J2000.0 epoch
    gst = CoordinateSystem.calculate_greenwich_sidereal_time(julian_date)
    
    # Convert to ECEF
    ecef_vector = CoordinateSystem.eci_to_ecef(eci_vector, gst)
    
    # Convert back to ECI
    converted_eci = CoordinateSystem.ecef_to_eci(ecef_vector, gst)
    
    assert converted_eci.x == pytest.approx(eci_vector.x, abs=1e-6)
    assert converted_eci.y == pytest.approx(eci_vector.y, abs=1e-6)
    assert converted_eci.z == pytest.approx(eci_vector.z, abs=1e-6) 