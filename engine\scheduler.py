"""
事件调度器模块

负责离散事件的调度和管理，包括事件队列、事件执行、事件取消等功能。
"""

import heapq
from typing import List, Dict, Set, Optional, Callable, Any
from .event import Event, EventType, EventPriority


class EventScheduler:
    """
    事件调度器
    
    管理事件队列，负责事件的调度、执行顺序控制和事件生命周期管理。
    """
    
    def __init__(self, max_queue_size: int = 10000):
        """
        初始化事件调度器
        
        Args:
            max_queue_size: 事件队列最大大小
        """
        self.event_queue = []  # 优先队列，使用堆实现
        self.event_registry = {}  # 事件ID -> 事件对象的映射
        self.processed_events = set()  # 已处理事件ID集合
        self.cancelled_events = set()  # 已取消事件ID集合
        
        self.max_queue_size = max_queue_size
        self.total_scheduled = 0
        self.total_processed = 0
        self.total_cancelled = 0
        
        # 事件处理器映射
        self.event_handlers = {}  # EventType -> handler function
        
        # 事件监听器
        self.event_listeners = {
            'before_schedule': [],
            'after_schedule': [],
            'before_process': [],
            'after_process': [],
            'on_cancel': []
        }
        
        # 调度统计
        self.scheduling_stats = {
            'events_by_type': {},
            'events_by_priority': {},
            'queue_size_history': [],
            'processing_times': []
        }
    
    def schedule_event(self, event: Event) -> bool:
        """
        调度事件
        
        Args:
            event: 要调度的事件
            
        Returns:
            bool: 调度是否成功
        """
        # 检查队列大小限制
        if len(self.event_queue) >= self.max_queue_size:
            return False
        
        # 检查事件是否已存在
        if event.event_id in self.event_registry:
            return False
        
        # 触发调度前监听器
        self._notify_listeners('before_schedule', event)
        
        # 添加到队列和注册表
        heapq.heappush(self.event_queue, event)
        self.event_registry[event.event_id] = event
        
        # 更新统计信息
        self.total_scheduled += 1
        self._update_scheduling_stats(event)
        
        # 触发调度后监听器
        self._notify_listeners('after_schedule', event)
        
        return True
    
    def schedule_event_at(self, 
                         event_id: str,
                         event_type: EventType,
                         scheduled_time: float,
                         priority: EventPriority = EventPriority.NORMAL,
                         data: Optional[Dict[str, Any]] = None) -> bool:
        """
        在指定时间调度事件
        
        Args:
            event_id: 事件ID
            event_type: 事件类型
            scheduled_time: 调度时间
            priority: 事件优先级
            data: 事件数据
            
        Returns:
            bool: 调度是否成功
        """
        event = Event(event_id, event_type, scheduled_time, priority, data)
        return self.schedule_event(event)
    
    def schedule_event_after(self,
                           event_id: str,
                           event_type: EventType,
                           delay: float,
                           current_time: float,
                           priority: EventPriority = EventPriority.NORMAL,
                           data: Optional[Dict[str, Any]] = None) -> bool:
        """
        在当前时间后延迟调度事件
        
        Args:
            event_id: 事件ID
            event_type: 事件类型
            delay: 延迟时间
            current_time: 当前仿真时间
            priority: 事件优先级
            data: 事件数据
            
        Returns:
            bool: 调度是否成功
        """
        scheduled_time = current_time + delay
        return self.schedule_event_at(event_id, event_type, scheduled_time, priority, data)
    
    def get_next_event(self) -> Optional[Event]:
        """
        获取下一个要处理的事件（不移除）
        
        Returns:
            下一个事件或None
        """
        while self.event_queue:
            event = self.event_queue[0]
            
            # 跳过已取消的事件
            if event.event_id in self.cancelled_events:
                heapq.heappop(self.event_queue)
                continue
            
            return event
        
        return None
    
    def pop_next_event(self) -> Optional[Event]:
        """
        弹出下一个要处理的事件
        
        Returns:
            下一个事件或None
        """
        while self.event_queue:
            event = heapq.heappop(self.event_queue)
            
            # 跳过已取消的事件
            if event.event_id in self.cancelled_events:
                continue
            
            return event
        
        return None
    
    def process_next_event(self, current_time: float) -> Optional[Event]:
        """
        处理下一个事件
        
        Args:
            current_time: 当前仿真时间
            
        Returns:
            处理的事件或None
        """
        event = self.pop_next_event()
        if not event:
            return None
        
        # 检查事件时间
        if event.scheduled_time > current_time:
            # 事件时间还未到，重新放回队列
            heapq.heappush(self.event_queue, event)
            return None
        
        # 触发处理前监听器
        self._notify_listeners('before_process', event)
        
        # 处理事件
        success = self._process_event(event)
        
        if success:
            # 标记为已处理
            event.mark_processed()
            self.processed_events.add(event.event_id)
            self.total_processed += 1
            
            # 从注册表中移除
            if event.event_id in self.event_registry:
                del self.event_registry[event.event_id]
        
        # 触发处理后监听器
        self._notify_listeners('after_process', event)
        
        return event if success else None
    
    def cancel_event(self, event_id: str) -> bool:
        """
        取消事件
        
        Args:
            event_id: 要取消的事件ID
            
        Returns:
            bool: 取消是否成功
        """
        if event_id in self.cancelled_events or event_id in self.processed_events:
            return False
        
        if event_id not in self.event_registry:
            return False
        
        # 标记为已取消
        event = self.event_registry[event_id]
        event.cancel()
        self.cancelled_events.add(event_id)
        self.total_cancelled += 1
        
        # 触发取消监听器
        self._notify_listeners('on_cancel', event)
        
        return True
    
    def cancel_events_by_type(self, event_type: EventType) -> int:
        """
        取消指定类型的所有事件
        
        Args:
            event_type: 事件类型
            
        Returns:
            int: 取消的事件数量
        """
        cancelled_count = 0
        
        for event_id, event in list(self.event_registry.items()):
            if (event.event_type == event_type and 
                event_id not in self.cancelled_events and 
                event_id not in self.processed_events):
                
                if self.cancel_event(event_id):
                    cancelled_count += 1
        
        return cancelled_count
    
    def register_handler(self, event_type: EventType, handler: Callable):
        """
        注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理器函数
        """
        self.event_handlers[event_type] = handler
    
    def unregister_handler(self, event_type: EventType):
        """
        注销事件处理器
        
        Args:
            event_type: 事件类型
        """
        if event_type in self.event_handlers:
            del self.event_handlers[event_type]
    
    def add_listener(self, event_name: str, listener: Callable):
        """
        添加事件监听器
        
        Args:
            event_name: 监听的事件名称
            listener: 监听器函数
        """
        if event_name in self.event_listeners:
            self.event_listeners[event_name].append(listener)
    
    def remove_listener(self, event_name: str, listener: Callable):
        """
        移除事件监听器
        
        Args:
            event_name: 监听的事件名称
            listener: 要移除的监听器函数
        """
        if event_name in self.event_listeners:
            if listener in self.event_listeners[event_name]:
                self.event_listeners[event_name].remove(listener)
    
    def get_queue_size(self) -> int:
        """获取当前队列大小"""
        return len(self.event_queue)
    
    def get_pending_events_count(self) -> int:
        """获取待处理事件数量"""
        return len([e for e in self.event_queue 
                   if e.event_id not in self.cancelled_events])
    
    def get_events_by_type(self, event_type: EventType) -> List[Event]:
        """
        获取指定类型的所有待处理事件
        
        Args:
            event_type: 事件类型
            
        Returns:
            事件列表
        """
        events = []
        for event in self.event_queue:
            if (event.event_type == event_type and 
                event.event_id not in self.cancelled_events):
                events.append(event)
        return events
    
    def clear_processed_events(self):
        """清理已处理事件的记录"""
        self.processed_events.clear()
        self.cancelled_events.clear()
    
    def clear_all(self):
        """清空所有事件"""
        self.event_queue.clear()
        self.event_registry.clear()
        self.processed_events.clear()
        self.cancelled_events.clear()
        
        # 重置统计
        self.total_scheduled = 0
        self.total_processed = 0
        self.total_cancelled = 0
    
    def get_statistics(self) -> Dict:
        """获取调度统计信息"""
        return {
            'total_scheduled': self.total_scheduled,
            'total_processed': self.total_processed,
            'total_cancelled': self.total_cancelled,
            'current_queue_size': self.get_queue_size(),
            'pending_events': self.get_pending_events_count(),
            'events_by_type': self.scheduling_stats['events_by_type'].copy(),
            'events_by_priority': self.scheduling_stats['events_by_priority'].copy()
        }
    
    def _process_event(self, event: Event) -> bool:
        """
        处理单个事件
        
        Args:
            event: 要处理的事件
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 查找对应的处理器
            handler = self.event_handlers.get(event.event_type)
            
            if handler:
                # 调用处理器
                result = handler(event)
                return result is not False  # None也认为是成功
            else:
                # 没有注册处理器，默认成功
                return True
                
        except Exception as e:
            # 处理器执行出错
            print(f"事件处理错误: {e}")
            return False
    
    def _update_scheduling_stats(self, event: Event):
        """更新调度统计信息"""
        # 按类型统计
        event_type_name = event.event_type.value
        if event_type_name not in self.scheduling_stats['events_by_type']:
            self.scheduling_stats['events_by_type'][event_type_name] = 0
        self.scheduling_stats['events_by_type'][event_type_name] += 1
        
        # 按优先级统计
        priority_name = event.priority.name
        if priority_name not in self.scheduling_stats['events_by_priority']:
            self.scheduling_stats['events_by_priority'][priority_name] = 0
        self.scheduling_stats['events_by_priority'][priority_name] += 1
        
        # 记录队列大小历史
        current_size = len(self.event_queue)
        self.scheduling_stats['queue_size_history'].append(current_size)
        
        # 限制历史记录大小
        if len(self.scheduling_stats['queue_size_history']) > 1000:
            self.scheduling_stats['queue_size_history'] = \
                self.scheduling_stats['queue_size_history'][-500:]
    
    def _notify_listeners(self, event_name: str, event: Event):
        """通知监听器"""
        for listener in self.event_listeners.get(event_name, []):
            try:
                listener(event)
            except Exception as e:
                # 忽略监听器错误
                pass
    
    def __str__(self):
        return (f"EventScheduler(queue_size={self.get_queue_size()}, "
                f"scheduled={self.total_scheduled}, "
                f"processed={self.total_processed})")
    
    def __repr__(self):
        return self.__str__() 