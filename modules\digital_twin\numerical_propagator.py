#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数值传播器
实现高精度数值积分进行轨道传播
"""

import numpy as np
from typing import List, Optional, Tuple
from .state_vector import StateVector, StateVectorDType, state_to_flat_array, flat_array_to_state
from .satellite import Satellite
from .perturbations import J2Perturbation, AtmosphericDrag, SolarRadiationPressure


# 儒略日数据类型
JulianDateDType = np.float64


class NumericalPropagatorError(Exception):
    """数值传播器错误"""
    pass


class NumericalPropagator:
    """
    数值传播器类
    使用四阶龙格-库塔方法进行高精度轨道传播
    """
    
    # 地球物理常数
    EARTH_MU = 3.986004418e14      # m³/s² 地球引力参数
    EARTH_RADIUS = 6378.137        # km 地球半径
    
    def __init__(self, include_j2: bool = True, include_drag: bool = True, 
                 include_srp: bool = True, f107: float = 150.0, ap: float = 15.0):
        """
        初始化数值传播器
        
        Args:
            include_j2: 是否包含J2摄动
            include_drag: 是否包含大气阻力
            include_srp: 是否包含太阳辐射压
            f107: 太阳F10.7通量
            ap: 地磁Ap指数
        """
        self.include_j2 = include_j2
        self.include_drag = include_drag
        self.include_srp = include_srp
        self.f107 = f107
        self.ap = ap
        
        # 统计信息
        self.integration_steps = 0
        self.function_evaluations = 0
    
    def propagate(self, initial_state: StateVector, satellite: Satellite,
                 start_jd: JulianDateDType, end_jd: JulianDateDType,
                 time_step_sec: float = 60.0) -> np.ndarray:
        """
        数值传播轨道
        
        Args:
            initial_state: 初始状态向量
            satellite: 卫星物理属性
            start_jd: 起始儒略日
            end_jd: 结束儒略日
            time_step_sec: 积分步长（秒）
            
        Returns:
            星历数组（StateVectorDType）
            
        Raises:
            NumericalPropagatorError: 传播计算错误
        """
        try:
            # 重置统计信息
            self.integration_steps = 0
            self.function_evaluations = 0
            
            # 计算总时间和步数
            total_time_sec = (end_jd - start_jd) * 86400.0  # 转换为秒
            num_steps = int(total_time_sec / time_step_sec) + 1
            
            if num_steps <= 0:
                raise NumericalPropagatorError("时间步数必须大于0")
            
            # 初始化星历数组
            ephemeris = np.zeros(num_steps, dtype=StateVectorDType)
            
            # 设置初始状态
            current_state = initial_state.copy()
            current_jd = start_jd
            ephemeris[0] = current_state.to_structured_array()
            
            # 时间步长（天）
            dt_days = time_step_sec / 86400.0
            
            # 主积分循环
            for i in range(1, num_steps):
                # RK4积分步
                current_state = self._rk4_step(
                    current_state, satellite, current_jd, dt_days
                )
                
                # 更新时间
                current_jd += dt_days
                
                # 存储状态
                ephemeris[i] = current_state.to_structured_array()
                
                # 检查轨道是否衰减
                r_norm = np.linalg.norm(current_state.position)
                if r_norm < self.EARTH_RADIUS + 80.0:  # 80km最低高度
                    # 轨道衰减，截断星历
                    return ephemeris[:i+1]
                
                self.integration_steps += 1
            
            return ephemeris
            
        except Exception as e:
            raise NumericalPropagatorError(f"数值传播失败: {str(e)}")
    
    def _rk4_step(self, state: StateVector, satellite: Satellite,
                 jd: JulianDateDType, dt: float) -> StateVector:
        """
        四阶龙格-库塔积分步
        
        Args:
            state: 当前状态向量
            satellite: 卫星对象
            jd: 当前儒略日
            dt: 时间步长（天）
            
        Returns:
            下一步状态向量
        """
        # 转换为一维数组
        y0 = state.to_flat_array()
        
        # 转换时间步长为秒
        dt_sec = dt * 86400.0
        
        # RK4步骤
        k1 = dt_sec * self._state_derivative(jd, y0, satellite)
        k2 = dt_sec * self._state_derivative(jd + dt/2, y0 + 0.5*k1, satellite)
        k3 = dt_sec * self._state_derivative(jd + dt/2, y0 + 0.5*k2, satellite)
        k4 = dt_sec * self._state_derivative(jd + dt, y0 + k3, satellite)
        
        # 计算下一步状态
        y1 = y0 + (k1 + 2*k2 + 2*k3 + k4) / 6.0
        
        # 转换回StateVector
        next_state = StateVector.from_flat_array(y1)
        
        return next_state
    
    def _state_derivative(self, jd: JulianDateDType, state_array: np.ndarray,
                         satellite: Satellite) -> np.ndarray:
        """
        计算状态向量的时间导数
        
        Args:
            jd: 儒略日时间
            state_array: 状态数组 [x,y,z,vx,vy,vz,q0,q1,q2,q3,wx,wy,wz]
            satellite: 卫星对象
            
        Returns:
            状态导数数组
        """
        self.function_evaluations += 1
        
        # 解包状态向量
        r = state_array[0:3]      # 位置 (km)
        v = state_array[3:6]      # 速度 (km/s)
        q = state_array[6:10]     # 四元数
        omega = state_array[10:13] # 角速度 (rad/s)
        
        # 计算合加速度
        a_total = self._compute_total_acceleration(r, v, satellite, jd)
        
        # 计算姿态运动学（四元数导数）
        q_dot = self._compute_quaternion_derivative(q, omega)
        
        # 计算姿态动力学（角速度导数）
        omega_dot = self._compute_angular_acceleration(omega, satellite)
        
        # 组合导数向量
        state_dot = np.concatenate([
            v,          # 位置导数 = 速度
            a_total,    # 速度导数 = 加速度
            q_dot,      # 四元数导数
            omega_dot   # 角速度导数
        ])
        
        return state_dot
    
    def _compute_total_acceleration(self, r: np.ndarray, v: np.ndarray,
                                  satellite: Satellite, jd: JulianDateDType) -> np.ndarray:
        """
        计算总加速度
        
        Args:
            r: 位置向量 (km)
            v: 速度向量 (km/s)
            satellite: 卫星对象
            jd: 儒略日时间
            
        Returns:
            总加速度向量 (km/s²)
        """
        # 重力加速度
        r_norm = np.linalg.norm(r)
        if r_norm < 1e-10:
            return np.zeros(3)
        
        # 转换为米进行计算
        r_m = r * 1000.0
        r_norm_m = r_norm * 1000.0
        
        # 中心引力
        a_gravity = -self.EARTH_MU * r_m / (r_norm_m**3)
        a_gravity = a_gravity / 1000.0  # 转换回km/s²
        
        # 初始化总加速度
        a_total = a_gravity.copy()
        
        # J2摄动
        if self.include_j2:
            a_j2 = J2Perturbation.calculate(r)
            a_total += a_j2
        
        # 大气阻力
        if self.include_drag:
            a_drag = AtmosphericDrag.calculate(r, v, satellite, jd, self.f107, self.ap)
            a_total += a_drag
        
        # 太阳辐射压
        if self.include_srp:
            a_srp = SolarRadiationPressure.calculate(r, satellite, jd)
            a_total += a_srp
        
        return a_total
    
    def _compute_quaternion_derivative(self, q: np.ndarray, omega: np.ndarray) -> np.ndarray:
        """
        计算四元数导数
        
        Args:
            q: 四元数 [q0, q1, q2, q3]
            omega: 角速度向量 [wx, wy, wz] (rad/s)
            
        Returns:
            四元数导数
        """
        # 四元数运动学方程: q_dot = 0.5 * Ω(ω) * q
        # 其中 Ω(ω) 是角速度的四元数矩阵
        
        q0, q1, q2, q3 = q[0], q[1], q[2], q[3]
        wx, wy, wz = omega[0], omega[1], omega[2]
        
        # 四元数导数
        q0_dot = 0.5 * (-q1*wx - q2*wy - q3*wz)
        q1_dot = 0.5 * ( q0*wx - q3*wy + q2*wz)
        q2_dot = 0.5 * ( q3*wx + q0*wy - q1*wz)
        q3_dot = 0.5 * (-q2*wx + q1*wy + q0*wz)
        
        return np.array([q0_dot, q1_dot, q2_dot, q3_dot])
    
    def _compute_angular_acceleration(self, omega: np.ndarray, satellite: Satellite) -> np.ndarray:
        """
        计算角加速度
        
        Args:
            omega: 角速度向量 (rad/s)
            satellite: 卫星对象
            
        Returns:
            角加速度向量 (rad/s²)
        """
        # 欧拉刚体动力学方程: I * ω_dot = -ω × (I * ω) + M_ext
        # 假设无外力矩 (M_ext = 0)
        
        I = satellite.inertia_tensor
        I_omega = np.dot(I, omega)
        
        # 计算 ω × (I * ω)
        cross_product = np.cross(omega, I_omega)
        
        # 求解 I * ω_dot = -cross_product
        try:
            omega_dot = np.linalg.solve(I, -cross_product)
        except np.linalg.LinAlgError:
            # 如果惯量张量奇异，返回零角加速度
            omega_dot = np.zeros(3)
        
        return omega_dot
    
    def get_statistics(self) -> dict:
        """
        获取传播统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'integration_steps': self.integration_steps,
            'function_evaluations': self.function_evaluations,
            'include_j2': self.include_j2,
            'include_drag': self.include_drag,
            'include_srp': self.include_srp,
            'f107': self.f107,
            'ap': self.ap
        }
