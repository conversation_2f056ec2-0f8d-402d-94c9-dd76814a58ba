"""
网络离散事件引擎使用示例

展示如何使用引擎进行网络仿真。
"""

import random
from .simulation_engine import SimulationEngine
from .event import EventType, EventPriority
from .network_events import *


def simple_packet_simulation():
    """简单的数据包传输仿真示例"""
    print("=== 简单数据包传输仿真 ===")
    
    # 创建仿真引擎
    engine = SimulationEngine(max_simulation_time=10.0)
    
    # 调度一些数据包事件
    for i in range(5):
        # 数据包到达事件
        arrival_event = create_packet_arrival(
            event_id=f"packet_{i}_arrival",
            scheduled_time=i * 2.0,
            source="node_A",
            destination="node_B",
            packet_id=f"packet_{i}",
            packet_size=1024,
            protocol="TCP"
        )
        engine.schedule_event(arrival_event)
        
        # 数据包发送事件（稍后）
        departure_event = create_packet_departure(
            event_id=f"packet_{i}_departure",
            scheduled_time=i * 2.0 + 0.5,
            source="node_B",
            destination="node_C",
            packet_id=f"packet_{i}",
            packet_size=1024,
            protocol="TCP"
        )
        engine.schedule_event(departure_event)
    
    # 运行仿真
    success = engine.run()
    
    if success:
        print(f"仿真完成！总时间: {engine.get_current_time():.2f}")
        stats = engine.get_statistics()
        print(f"处理的事件数: {stats['simulation']['total_events_processed']}")
    else:
        print("仿真失败！")


def network_topology_simulation():
    """网络拓扑变化仿真示例"""
    print("\n=== 网络拓扑变化仿真 ===")
    
    engine = SimulationEngine(max_simulation_time=20.0)
    
    # 初始化网络链路
    links = [
        ("link_1", "router_A", "router_B"),
        ("link_2", "router_B", "router_C"),
        ("link_3", "router_C", "router_D")
    ]
    
    # 调度链路状态变化事件
    for i, (link_id, node_a, node_b) in enumerate(links):
        # 链路在仿真开始时上线
        link_up_event = create_link_up(
            event_id=f"{link_id}_up_start",
            scheduled_time=0.0,
            link_id=link_id,
            node_a=node_a,
            node_b=node_b
        )
        engine.schedule_event(link_up_event)
        
        # 链路在中途下线
        link_down_event = create_link_down(
            event_id=f"{link_id}_down",
            scheduled_time=5.0 + i * 2.0,
            link_id=link_id,
            node_a=node_a,
            node_b=node_b
        )
        engine.schedule_event(link_down_event)
        
        # 链路重新上线
        link_up_event2 = create_link_up(
            event_id=f"{link_id}_up_recovery",
            scheduled_time=12.0 + i * 1.5,
            link_id=link_id,
            node_a=node_a,
            node_b=node_b
        )
        engine.schedule_event(link_up_event2)
    
    # 添加路由更新事件
    for i in range(3):
        route_event = create_route_update(
            event_id=f"route_update_{i}",
            scheduled_time=8.0 + i * 2.0,
            node_id="router_A",
            destination="router_D",
            next_hop=f"router_{chr(66+i)}",  # router_B, router_C, router_D
            metric=float(i + 1)
        )
        engine.schedule_event(route_event)
    
    # 运行仿真
    success = engine.run()
    
    if success:
        print(f"网络拓扑仿真完成！")
        stats = engine.get_statistics()
        print(f"总事件数: {stats['simulation']['total_events_processed']}")


def custom_event_handler_example():
    """自定义事件处理器示例"""
    print("\n=== 自定义事件处理器示例 ===")
    
    engine = SimulationEngine(max_simulation_time=15.0)
    
    # 网络统计
    network_stats = {
        'packets_received': 0,
        'packets_sent': 0,
        'links_up': 0,
        'links_down': 0
    }
    
    def custom_packet_arrival_handler(event):
        """自定义数据包到达处理器"""
        network_stats['packets_received'] += 1
        print(f"[{event.scheduled_time:.2f}] 接收数据包 {event.packet_id} "
              f"({event.packet_size} bytes) 从 {event.source_node}")
        return True
    
    def custom_packet_departure_handler(event):
        """自定义数据包发送处理器"""
        network_stats['packets_sent'] += 1
        print(f"[{event.scheduled_time:.2f}] 发送数据包 {event.packet_id} "
              f"到 {event.destination_node}")
        return True
    
    def custom_link_state_handler(event):
        """自定义链路状态处理器"""
        if event.event_type == EventType.LINK_UP:
            network_stats['links_up'] += 1
            state = "上线"
        else:
            network_stats['links_down'] += 1
            state = "下线"
        
        link_id = event.get_data('link_id')
        print(f"[{event.scheduled_time:.2f}] 链路 {link_id} {state}")
        return True
    
    # 注册自定义处理器
    engine.register_event_handler(EventType.PACKET_ARRIVAL, custom_packet_arrival_handler)
    engine.register_event_handler(EventType.PACKET_DEPARTURE, custom_packet_departure_handler)
    engine.register_event_handler(EventType.LINK_UP, custom_link_state_handler)
    engine.register_event_handler(EventType.LINK_DOWN, custom_link_state_handler)
    
    # 生成随机事件
    random.seed(42)
    
    for i in range(10):
        # 随机数据包事件
        packet_time = random.uniform(0, 10)
        packet_event = create_packet_arrival(
            event_id=f"random_packet_{i}",
            scheduled_time=packet_time,
            source=f"node_{random.randint(1, 5)}",
            destination=f"node_{random.randint(1, 5)}",
            packet_id=f"random_packet_{i}",
            packet_size=random.randint(64, 1500),
            protocol=random.choice(["TCP", "UDP"])
        )
        engine.schedule_event(packet_event)
        
        # 随机发送事件
        send_time = packet_time + random.uniform(0.1, 1.0)
        send_event = create_packet_departure(
            event_id=f"random_send_{i}",
            scheduled_time=send_time,
            source=f"node_{random.randint(1, 5)}",
            destination=f"node_{random.randint(1, 5)}",
            packet_id=f"random_packet_{i}",
            packet_size=random.randint(64, 1500),
            protocol=random.choice(["TCP", "UDP"])
        )
        engine.schedule_event(send_event)
    
    # 随机链路事件
    for i in range(5):
        link_time = random.uniform(0, 12)
        link_event = create_link_down(
            event_id=f"random_link_down_{i}",
            scheduled_time=link_time,
            link_id=f"link_{i}",
            node_a=f"router_{i}",
            node_b=f"router_{i+1}"
        )
        engine.schedule_event(link_event)
        
        # 链路恢复
        recovery_time = link_time + random.uniform(1, 3)
        recovery_event = create_link_up(
            event_id=f"random_link_up_{i}",
            scheduled_time=recovery_time,
            link_id=f"link_{i}",
            node_a=f"router_{i}",
            node_b=f"router_{i+1}"
        )
        engine.schedule_event(recovery_event)
    
    # 运行仿真
    success = engine.run()
    
    if success:
        print(f"\n自定义处理器仿真完成！")
        print(f"网络统计:")
        print(f"  接收数据包: {network_stats['packets_received']}")
        print(f"  发送数据包: {network_stats['packets_sent']}")
        print(f"  链路上线: {network_stats['links_up']}")
        print(f"  链路下线: {network_stats['links_down']}")


def run_all_examples():
    """运行所有示例"""
    print("网络离散事件引擎示例")
    print("=" * 50)
    
    simple_packet_simulation()
    network_topology_simulation()
    custom_event_handler_example()
    
    print("\n" + "=" * 50)
    print("所有示例执行完成！")


if __name__ == "__main__":
    run_all_examples() 