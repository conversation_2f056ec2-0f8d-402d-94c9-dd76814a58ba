"""
JWT认证处理器
处理JWT token的生成、验证和管理
"""

import jwt
from datetime import datetime, timedelta
from functools import wraps
from typing import Dict, Optional, Any
from flask import current_app, request, jsonify
from flask_jwt_extended import get_jwt_identity, jwt_required

from app.utils.redis_client import redis_client, RedisKeys
from app.models.user import User


class JWTHandler:
    """JWT处理器类"""
    
    @staticmethod
    def generate_token(user: User) -> Dict[str, Any]:
        """生成JWT token"""
        try:
            # 生成访问token
            payload = {
                'user_id': user.id,
                'username': user.username,
                'role': user.role,
                'exp': datetime.utcnow() + current_app.config['JWT_ACCESS_TOKEN_EXPIRES'],
                'iat': datetime.utcnow()
            }
            
            access_token = jwt.encode(
                payload,
                current_app.config['JWT_SECRET_KEY'],
                algorithm='HS256'
            )
            
            # 将token存储到Redis中，用于会话管理
            session_key = RedisKeys.USER_SESSION.format(token=access_token)
            session_data = {
                'user_id': user.id,
                'username': user.username,
                'role': user.role,
                'created_at': datetime.utcnow().isoformat(),
                'expires_at': payload['exp'].isoformat()
            }
            
            # 设置会话过期时间（秒）
            expire_seconds = int(current_app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds())
            redis_client.set(session_key, session_data, expire=expire_seconds)
            
            return {
                'access_token': access_token,
                'token_type': 'Bearer',
                'expires_in': expire_seconds,
                'user': user.to_dict()
            }
            
        except Exception as e:
            current_app.logger.error(f"Error generating token: {e}")
            return None
    
    @staticmethod
    def verify_token(token: str) -> Optional[Dict[str, Any]]:
        """验证JWT token"""
        try:
            # 解码token
            payload = jwt.decode(
                token,
                current_app.config['JWT_SECRET_KEY'],
                algorithms=['HS256']
            )
            
            # 检查Redis中的会话
            session_key = RedisKeys.USER_SESSION.format(token=token)
            session_data = redis_client.get(session_key)
            
            if not session_data:
                return None
            
            # 验证用户是否仍然存在且状态正常
            user = User.get_by_id(payload['user_id'])
            if not user or user.status != 'active':
                # 清除无效会话
                redis_client.delete(session_key)
                return None
            
            return {
                'user_id': payload['user_id'],
                'username': payload['username'],
                'role': payload['role'],
                'user': user
            }
            
        except jwt.ExpiredSignatureError:
            # Token已过期，清除会话
            try:
                session_key = RedisKeys.USER_SESSION.format(token=token)
                redis_client.delete(session_key)
            except:
                pass
            return None
        except jwt.InvalidTokenError:
            return None
        except Exception as e:
            current_app.logger.error(f"Error verifying token: {e}")
            return None
    
    @staticmethod
    def revoke_token(token: str) -> bool:
        """撤销token"""
        try:
            session_key = RedisKeys.USER_SESSION.format(token=token)
            return redis_client.delete(session_key) > 0
        except Exception as e:
            current_app.logger.error(f"Error revoking token: {e}")
            return False
    
    @staticmethod
    def get_user_sessions(user_id: str) -> list:
        """获取用户的所有会话"""
        try:
            pattern = RedisKeys.USER_SESSION.format(token='*')
            session_keys = redis_client.scan_keys(pattern)
            
            user_sessions = []
            for session_key in session_keys:
                session_data = redis_client.get(session_key)
                if session_data and session_data.get('user_id') == user_id:
                    user_sessions.append({
                        'token': session_key.split(':')[-1],
                        'created_at': session_data.get('created_at'),
                        'expires_at': session_data.get('expires_at')
                    })
            
            return user_sessions
        except Exception as e:
            current_app.logger.error(f"Error getting user sessions: {e}")
            return []
    
    @staticmethod
    def revoke_user_sessions(user_id: str) -> int:
        """撤销用户的所有会话"""
        try:
            sessions = JWTHandler.get_user_sessions(user_id)
            revoked_count = 0
            
            for session in sessions:
                session_key = RedisKeys.USER_SESSION.format(token=session['token'])
                if redis_client.delete(session_key):
                    revoked_count += 1
            
            return revoked_count
        except Exception as e:
            current_app.logger.error(f"Error revoking user sessions: {e}")
            return 0


def token_required(f):
    """Token验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # 从请求头获取token
        auth_header = request.headers.get('Authorization')
        if auth_header:
            try:
                token = auth_header.split(' ')[1]  # Bearer <token>
            except IndexError:
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'INVALID_TOKEN_FORMAT',
                        'message': 'Token格式无效'
                    }
                }), 401
        
        if not token:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'TOKEN_MISSING',
                    'message': '缺少认证token'
                }
            }), 401
        
        # 验证token
        token_data = JWTHandler.verify_token(token)
        if not token_data:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'TOKEN_INVALID',
                    'message': 'Token无效或已过期'
                }
            }), 401
        
        # 将用户信息添加到请求上下文
        request.current_user = token_data['user']
        request.current_token = token
        
        return f(*args, **kwargs)
    
    return decorated


def permission_required(permission: str):
    """权限验证装饰器"""
    def decorator(f):
        @wraps(f)
        @token_required
        def decorated(*args, **kwargs):
            user = request.current_user
            
            if not user.has_permission(permission):
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'PERMISSION_DENIED',
                        'message': f'缺少权限: {permission}'
                    }
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated
    return decorator


def role_required(*roles):
    """角色验证装饰器"""
    def decorator(f):
        @wraps(f)
        @token_required
        def decorated(*args, **kwargs):
            user = request.current_user
            
            if user.role not in roles:
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'ROLE_REQUIRED',
                        'message': f'需要以下角色之一: {", ".join(roles)}'
                    }
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated
    return decorator


def get_current_user() -> Optional[User]:
    """获取当前用户"""
    return getattr(request, 'current_user', None)


def get_current_token() -> Optional[str]:
    """获取当前token"""
    return getattr(request, 'current_token', None)
