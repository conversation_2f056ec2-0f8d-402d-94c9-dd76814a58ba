import pytest
import time
from engine.event import Event, EventType, EventPriority
from engine.scheduler import EventScheduler

@pytest.fixture
def scheduler():
    """Pytest fixture for EventScheduler."""
    return EventScheduler()

def test_schedule_and_get_next(scheduler):
    """Test scheduling an event and peeking at it."""
    assert scheduler.get_next_event() is None
    
    event = Event("e1", EventType.TIMEOUT, time.time() + 10)
    assert scheduler.schedule_event(event)
    
    next_event = scheduler.get_next_event()
    assert next_event is not None
    assert next_event.event_id == "e1"
    
    # Ensure get_next_event does not remove the event
    assert scheduler.get_queue_size() == 1

def test_pop_next_event(scheduler):
    """Test popping the next event from the queue."""
    t = time.time()
    event1 = Event("e1", EventType.TIMEOUT, t + 10)
    event2 = Event("e2", EventType.TIMEOUT, t + 5)
    scheduler.schedule_event(event1)
    scheduler.schedule_event(event2)
    
    # event2 should come first
    popped_event = scheduler.pop_next_event()
    assert popped_event.event_id == "e2"
    assert scheduler.get_queue_size() == 1
    
    popped_event = scheduler.pop_next_event()
    assert popped_event.event_id == "e1"
    assert scheduler.get_queue_size() == 0

def test_cancel_event(scheduler):
    """Test cancelling a scheduled event."""
    event_id = "e_to_cancel"
    event = Event(event_id, EventType.TIMEOUT, time.time() + 30)
    scheduler.schedule_event(event)
    
    assert scheduler.cancel_event(event_id)
    # The event is still in the queue but marked as cancelled
    # pop_next_event should skip it.
    assert scheduler.pop_next_event() is None
    assert scheduler.get_queue_size() == 0 # It gets removed when popped
    assert scheduler.total_cancelled == 1

def test_process_next_event(scheduler):
    """Test the full process_next_event logic."""
    # Register a simple handler
    handler_called = False
    def my_handler(event):
        nonlocal handler_called
        handler_called = True
    
    scheduler.register_handler(EventType.PERIODIC_UPDATE, my_handler)
    
    # Schedule an event in the past so it's ready to be processed
    event = Event("e_proc", EventType.PERIODIC_UPDATE, time.time() - 1)
    scheduler.schedule_event(event)
    
    processed_event = scheduler.process_next_event(time.time())
    
    assert processed_event is not None
    assert processed_event.is_processed()
    assert handler_called
    assert scheduler.total_processed == 1
    assert scheduler.get_queue_size() == 0 