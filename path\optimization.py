"""
路径优化器模块

提供路径优化功能，包括：
- 多目标路径优化
- 负载均衡优化
- 延迟优化
- 带宽优化
- 动态路径调整
"""

import math
import random
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from .path_planner import PathResult, PathNode, PathSegment


@dataclass
class OptimizationCriteria:
    """优化准则"""
    minimize_delay: float = 1.0      # 最小化延迟权重
    maximize_bandwidth: float = 1.0   # 最大化带宽权重
    minimize_hops: float = 0.5       # 最小化跳数权重
    maximize_reliability: float = 1.0 # 最大化可靠性权重
    minimize_cost: float = 0.8       # 最小化成本权重


@dataclass
class OptimizationResult:
    """优化结果"""
    original_path: PathResult
    optimized_path: PathResult
    improvement_percentage: float
    optimization_type: str
    metrics: Dict[str, float]
    success: bool


class PathOptimizer:
    """路径优化器"""
    
    def __init__(self):
        """初始化路径优化器"""
        self.optimization_count = 0
        self.optimization_history = []
    
    def optimize_path(self, path: PathResult, criteria: OptimizationCriteria) -> OptimizationResult:
        """
        优化路径
        
        Args:
            path: 原始路径
            criteria: 优化准则
            
        Returns:
            优化结果
        """
        self.optimization_count += 1
        
        if not path.success or len(path.nodes) < 2:
            return OptimizationResult(
                original_path=path,
                optimized_path=path,
                improvement_percentage=0.0,
                optimization_type="none",
                metrics={},
                success=False
            )
        
        original_score = self._calculate_path_score(path, criteria)
        optimized_path = self._local_optimization(path, criteria)
        optimized_score = self._calculate_path_score(optimized_path, criteria)
        
        improvement = ((original_score - optimized_score) / original_score) * 100 if original_score > 0 else 0
        
        metrics = {
            'original_score': original_score,
            'optimized_score': optimized_score,
            'original_delay': path.total_delay,
            'optimized_delay': optimized_path.total_delay,
            'original_hops': path.hop_count,
            'optimized_hops': optimized_path.hop_count
        }
        
        result = OptimizationResult(
            original_path=path,
            optimized_path=optimized_path,
            improvement_percentage=improvement,
            optimization_type="local",
            metrics=metrics,
            success=improvement > 0
        )
        
        self.optimization_history.append(result)
        return result
    
    def _calculate_path_score(self, path: PathResult, criteria: OptimizationCriteria) -> float:
        """计算路径评分（越低越好）"""
        if not path.success or len(path.segments) == 0:
            return float('inf')
        
        # 延迟分数
        delay_score = path.total_delay * criteria.minimize_delay
        
        # 跳数分数
        hop_score = path.hop_count * criteria.minimize_hops * 100
        
        # 成本分数
        cost_score = path.total_cost * criteria.minimize_cost / 1000000
        
        # 带宽分数（带宽越高分数越低）
        min_bandwidth = min(seg.bandwidth for seg in path.segments) if path.segments else 0
        bandwidth_score = (1e9 - min_bandwidth) / 1e9 * criteria.maximize_bandwidth * 1000
        
        # 可靠性分数
        avg_quality = path.average_quality
        reliability_score = (1.0 - avg_quality) * criteria.maximize_reliability * 500
        
        return delay_score + hop_score + cost_score + bandwidth_score + reliability_score
    
    def _local_optimization(self, path: PathResult, criteria: OptimizationCriteria) -> PathResult:
        """局部优化"""
        if len(path.nodes) <= 2:
            return path
        
        best_path = path
        best_score = self._calculate_path_score(path, criteria)
        
        # 尝试移除中间节点
        for i in range(1, len(path.nodes) - 1):
            new_nodes = path.nodes[:i] + path.nodes[i+1:]
            new_path = self._construct_path_from_nodes(new_nodes)
            
            if new_path.success:
                new_score = self._calculate_path_score(new_path, criteria)
                if new_score < best_score:
                    best_path = new_path
                    best_score = new_score
        
        return best_path
    
    def _construct_path_from_nodes(self, nodes: List[PathNode]) -> PathResult:
        """从节点列表构造路径"""
        if len(nodes) < 2:
            return PathResult([], [], 0.0, 0.0, float('inf'), False)
        
        segments = []
        total_distance = 0.0
        total_delay = 0.0
        total_cost = 0.0
        
        for i in range(len(nodes) - 1):
            node1, node2 = nodes[i], nodes[i + 1]
            distance = node1.distance_to(node2)
            delay = distance / 299792458
            quality = max(0.1, 1.0 - distance / 2000000)
            bandwidth = 1e9 * quality
            
            segment = PathSegment(node1, node2, distance, delay, quality, bandwidth)
            segments.append(segment)
            
            total_distance += distance
            total_delay += delay
            total_cost += segment.cost
        
        return PathResult(
            nodes=nodes,
            segments=segments,
            total_distance=total_distance,
            total_delay=total_delay,
            total_cost=total_cost,
            success=True
        )
    
    def get_optimization_statistics(self) -> Dict:
        """获取优化统计信息"""
        if not self.optimization_history:
            return {
                'total_optimizations': 0,
                'average_improvement': 0.0,
                'success_rate': 0.0
            }
        
        successful = [r for r in self.optimization_history if r.success]
        
        return {
            'total_optimizations': len(self.optimization_history),
            'successful_optimizations': len(successful),
            'success_rate': len(successful) / len(self.optimization_history) * 100,
            'average_improvement': sum(r.improvement_percentage for r in successful) / len(successful) if successful else 0.0
        } 