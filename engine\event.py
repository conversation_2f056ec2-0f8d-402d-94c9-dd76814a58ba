"""
事件定义模块

定义了事件系统的基础类和网络相关的事件类型。
"""

import time
from typing import Any, Dict, Optional
from enum import Enum


class EventType(Enum):
    """事件类型枚举"""
    # 网络事件
    PACKET_ARRIVAL = "packet_arrival"
    PACKET_DEPARTURE = "packet_departure"
    PACKET_DROP = "packet_drop"
    LINK_UP = "link_up"
    LINK_DOWN = "link_down"
    
    # 路由事件
    ROUTE_UPDATE = "route_update"
    ROUTE_DISCOVERY = "route_discovery"
    
    # 传输事件
    TRANSMISSION_START = "transmission_start"
    TRANSMISSION_END = "transmission_end"
    
    # 系统事件
    TIMEOUT = "timeout"
    PERIODIC_UPDATE = "periodic_update"
    SIMULATION_END = "simulation_end"


class EventPriority(Enum):
    """事件优先级"""
    CRITICAL = 0    # 最高优先级
    HIGH = 1
    NORMAL = 2
    LOW = 3


class Event:
    """
    事件基类
    
    所有离散事件的基础类，包含事件的基本属性和方法。
    """
    
    def __init__(self, 
                 event_id: str,
                 event_type: EventType,
                 scheduled_time: float,
                 priority: EventPriority = EventPriority.NORMAL,
                 data: Optional[Dict[str, Any]] = None):
        """
        初始化事件
        
        Args:
            event_id: 事件唯一标识符
            event_type: 事件类型
            scheduled_time: 事件调度时间
            priority: 事件优先级
            data: 事件携带的数据
        """
        self.event_id = event_id
        self.event_type = event_type
        self.scheduled_time = scheduled_time
        self.creation_time = time.time()
        self.priority = priority
        self.data = data or {}
        self.processed = False
        self.cancelled = False
    
    def __lt__(self, other):
        """用于优先队列排序：先按时间，再按优先级"""
        if self.scheduled_time != other.scheduled_time:
            return self.scheduled_time < other.scheduled_time
        return self.priority.value < other.priority.value
    
    def __eq__(self, other):
        """事件相等比较"""
        return (self.event_id == other.event_id and 
                self.scheduled_time == other.scheduled_time)
    
    def __hash__(self):
        """用于哈希表存储"""
        return hash((self.event_id, self.scheduled_time))
    
    def cancel(self):
        """取消事件"""
        self.cancelled = True
    
    def is_cancelled(self) -> bool:
        """检查事件是否已取消"""
        return self.cancelled
    
    def is_processed(self) -> bool:
        """检查事件是否已处理"""
        return self.processed
    
    def mark_processed(self):
        """标记事件为已处理"""
        self.processed = True
    
    def get_data(self, key: str, default=None) -> Any:
        """获取事件数据"""
        return self.data.get(key, default)
    
    def set_data(self, key: str, value: Any):
        """设置事件数据"""
        self.data[key] = value
    
    def __str__(self):
        return (f"Event(id={self.event_id}, type={self.event_type.value}, "
                f"time={self.scheduled_time}, priority={self.priority.value})")
    
    def __repr__(self):
        return self.__str__()


class NetworkEvent(Event):
    """
    网络事件类
    
    专门用于网络相关事件的扩展类，包含网络特定的属性。
    """
    
    def __init__(self,
                 event_id: str,
                 event_type: EventType,
                 scheduled_time: float,
                 source_node: Optional[str] = None,
                 destination_node: Optional[str] = None,
                 packet_id: Optional[str] = None,
                 packet_size: int = 0,
                 protocol: str = "unknown",
                 priority: EventPriority = EventPriority.NORMAL,
                 data: Optional[Dict[str, Any]] = None):
        """
        初始化网络事件
        
        Args:
            event_id: 事件唯一标识符
            event_type: 事件类型
            scheduled_time: 事件调度时间
            source_node: 源节点ID
            destination_node: 目标节点ID
            packet_id: 数据包ID
            packet_size: 数据包大小（字节）
            protocol: 协议类型
            priority: 事件优先级
            data: 事件携带的数据
        """
        super().__init__(event_id, event_type, scheduled_time, priority, data)
        
        self.source_node = source_node
        self.destination_node = destination_node
        self.packet_id = packet_id
        self.packet_size = packet_size
        self.protocol = protocol
        
        # 网络相关的时间戳
        self.transmission_start_time = None
        self.transmission_end_time = None
        self.propagation_delay = 0.0
        self.processing_delay = 0.0
    
    def set_transmission_times(self, start_time: float, end_time: float):
        """设置传输时间"""
        self.transmission_start_time = start_time
        self.transmission_end_time = end_time
    
    def set_delays(self, propagation_delay: float, processing_delay: float = 0.0):
        """设置延迟参数"""
        self.propagation_delay = propagation_delay
        self.processing_delay = processing_delay
    
    def get_total_delay(self) -> float:
        """获取总延迟"""
        return self.propagation_delay + self.processing_delay
    
    def __str__(self):
        base_str = super().__str__()
        network_info = (f", src={self.source_node}, dst={self.destination_node}, "
                       f"packet={self.packet_id}, size={self.packet_size}")
        return base_str[:-1] + network_info + ")" 