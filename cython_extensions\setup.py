#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cython扩展模块构建脚本
用于编译性能关键函数的Cython实现
"""

from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy as np
import os

# 编译器选项
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'cdivision': True,
    'nonecheck': False,
    'profile': False,
    'linetrace': False
}

# 包含目录
include_dirs = [
    np.get_include(),
    '.'
]

# 扩展模块定义
extensions = [
    Extension(
        name="cython_extensions.rk4_integrator",
        sources=["rk4_integrator.pyx"],
        include_dirs=include_dirs,
        extra_compile_args=['-O3', '-ffast-math'],
        extra_link_args=['-O3']
    ),
    Extension(
        name="cython_extensions.perturbations_fast",
        sources=["perturbations_fast.pyx"],
        include_dirs=include_dirs,
        extra_compile_args=['-O3', '-ffast-math'],
        extra_link_args=['-O3']
    ),
    Extension(
        name="cython_extensions.topology_matrix",
        sources=["topology_matrix.pyx"],
        include_dirs=include_dirs,
        extra_compile_args=['-O3', '-ffast-math'],
        extra_link_args=['-O3']
    )
]

# 构建配置
setup(
    name="sdt_cython_extensions",
    version="1.0.0",
    description="SDT卫星数字孪生系统Cython优化扩展",
    author="SDT Development Team",
    ext_modules=cythonize(
        extensions,
        compiler_directives=compiler_directives,
        annotate=True  # 生成HTML注释文件
    ),
    zip_safe=False,
    python_requires='>=3.8'
)
