#!/usr/bin/env python3
"""
修复前端依赖和配置问题
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, cwd=None):
    """运行命令并显示输出"""
    try:
        print(f"执行命令: {command}")
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True
        )
        
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(result.stderr)
            
        return result.returncode == 0
    except Exception as e:
        print(f"命令执行失败: {e}")
        return False

def main():
    """主函数"""
    print("修复前端配置问题...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("错误: frontend目录不存在")
        return
    
    # 删除 node_modules 和 package-lock.json
    print("清理旧的依赖...")
    node_modules = frontend_dir / "node_modules"
    package_lock = frontend_dir / "package-lock.json"
    
    if node_modules.exists():
        print("删除 node_modules...")
        if os.name == 'nt':  # Windows
            run_command("rmdir /s /q node_modules", cwd=frontend_dir)
        else:  # Linux/Mac
            run_command("rm -rf node_modules", cwd=frontend_dir)
    
    if package_lock.exists():
        print("删除 package-lock.json...")
        package_lock.unlink()
    
    # 重新安装依赖
    print("重新安装依赖...")
    if not run_command("npm install", cwd=frontend_dir):
        print("npm install 失败，尝试使用 yarn...")
        if not run_command("yarn install", cwd=frontend_dir):
            print("依赖安装失败，请手动执行:")
            print("cd frontend")
            print("npm install")
            return
    
    print("✓ 前端依赖安装完成")
    print("\n现在可以启动前端服务:")
    print("cd frontend")
    print("npm start")
    print("\n或者直接运行:")
    print("python start_frontend.py")

if __name__ == "__main__":
    main()
