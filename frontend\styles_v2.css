/* SDT卫星数字孪生系统 v1.0 样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

header h1 {
    color: #1e3c72;
    font-size: 2.5em;
    margin-bottom: 10px;
}

header p {
    color: #666;
    font-size: 1.1em;
    margin-bottom: 15px;
}

.system-status {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.status-indicator {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: bold;
}

.status-indicator.connected {
    background: #4CAF50;
    color: white;
}

.status-indicator.disconnected {
    background: #f44336;
    color: white;
}

.status-indicator.connecting {
    background: #ff9800;
    color: white;
}

/* 导航标签 */
.tab-nav {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    color: #666;
    font-size: 1em;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background: rgba(30, 60, 114, 0.1);
    color: #1e3c72;
}

.tab-btn.active {
    background: #1e3c72;
    color: white;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.3);
}

/* 内容区域 */
.tab-content {
    display: none;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tab-content.active {
    display: block;
}

.module-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e0e0e0;
}

.module-header h2 {
    color: #1e3c72;
    font-size: 2em;
    margin-bottom: 10px;
}

.module-header p {
    color: #666;
    font-size: 1.1em;
}

/* 输入区域 */
.input-section {
    margin-bottom: 30px;
}

.input-section h3 {
    color: #1e3c72;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.tle-input-container {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

#tle-input {
    flex: 1;
    height: 120px;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    resize: vertical;
}

#tle-input:focus {
    outline: none;
    border-color: #1e3c72;
    box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
}

.tle-info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.tle-info.hidden {
    display: none;
}

.time-inputs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.time-inputs label {
    display: flex;
    flex-direction: column;
    font-weight: 500;
    color: #555;
}

.time-inputs input {
    margin-top: 5px;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1em;
}

.time-inputs input:focus {
    outline: none;
    border-color: #1e3c72;
    box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
}

/* 状态和卫星输入 */
.state-inputs, .satellite-inputs {
    margin-bottom: 20px;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.input-group label {
    min-width: 120px;
    font-weight: 500;
    color: #555;
}

.input-group input {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1em;
}

.input-group input:focus {
    outline: none;
    border-color: #1e3c72;
    box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
}

/* 摄动设置 */
.perturbation-settings {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.perturbation-settings label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #555;
    cursor: pointer;
}

.perturbation-settings input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #1e3c72;
}

/* 按钮样式 */
.btn-primary, .btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #1e3c72;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2a5298;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 60, 114, 0.3);
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* 结果区域 */
.results-section {
    margin-top: 30px;
}

.results-section h3 {
    color: #1e3c72;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.chart-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    text-align: center;
}

#orbit-results, #twin-results, #topology-results {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    min-height: 100px;
}

/* 拓扑和实时监控 */
.topology-inputs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.ephemeris-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.status-info {
    padding: 10px 15px;
    background: #e9ecef;
    border-radius: 6px;
    font-weight: 500;
    color: #495057;
}

.monitoring-section {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
}

.connection-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.log-container {
    background: #000;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    height: 400px;
    overflow-y: auto;
    padding: 15px;
    border-radius: 8px;
    border: 2px solid #333;
}

/* 加载和提示 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading.hidden {
    display: none;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #1e3c72;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: white;
    font-size: 1.2em;
    margin-top: 20px;
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: 10px;
    max-width: 400px;
}

.toast.hidden {
    display: none;
}

.toast.error {
    background: #f44336;
}

.toast.success {
    background: #4CAF50;
}

.toast-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5em;
    cursor: pointer;
    padding: 0;
    margin-left: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2em;
    }

    .tab-nav {
        flex-direction: column;
    }

    .time-inputs {
        grid-template-columns: 1fr;
    }

    .monitoring-section {
        grid-template-columns: 1fr;
    }

    .system-status {
        flex-direction: column;
        gap: 10px;
    }
}
