#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卫星通信模块
负责处理系统内各模块间的通信
"""

import socket
import threading
import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import queue
import asyncio

from utils.logger import setup_logger


class MessageType(Enum):
    """消息类型枚举"""
    TOPOLOGY_REQUEST = "topology_request"
    TOPOLOGY_RESPONSE = "topology_response"
    ROUTE_REQUEST = "route_request"
    ROUTE_RESPONSE = "route_response"
    STATUS_REQUEST = "status_request"
    STATUS_RESPONSE = "status_response"
    CONTROL_COMMAND = "control_command"
    DATA_PACKET = "data_packet"


@dataclass
class Message:
    """通信消息数据结构"""
    message_id: str
    message_type: MessageType
    source: str
    destination: str
    payload: Dict[str, Any]
    timestamp: float
    priority: int = 0


class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.logger = setup_logger("SessionManager")
        self.sessions: Dict[str, Dict] = {}
        self.session_lock = threading.RLock()
        
    def create_session(self, session_id: str, client_info: Dict) -> bool:
        """创建会话"""
        with self.session_lock:
            if session_id in self.sessions:
                return False
                
            self.sessions[session_id] = {
                'id': session_id,
                'client_info': client_info,
                'created_time': time.time(),
                'last_activity': time.time(),
                'message_count': 0,
                'active': True
            }
            
            self.logger.info(f"创建会话: {session_id}")
            return True
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """获取会话信息"""
        with self.session_lock:
            return self.sessions.get(session_id)
    
    def update_session_activity(self, session_id: str):
        """更新会话活动时间"""
        with self.session_lock:
            if session_id in self.sessions:
                self.sessions[session_id]['last_activity'] = time.time()
                self.sessions[session_id]['message_count'] += 1
    
    def close_session(self, session_id: str):
        """关闭会话"""
        with self.session_lock:
            if session_id in self.sessions:
                self.sessions[session_id]['active'] = False
                self.logger.info(f"关闭会话: {session_id}")
    
    def cleanup_expired_sessions(self, timeout: float = 300.0):
        """清理过期会话"""
        current_time = time.time()
        expired_sessions = []
        
        with self.session_lock:
            for session_id, session in self.sessions.items():
                if current_time - session['last_activity'] > timeout:
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                del self.sessions[session_id]
                self.logger.info(f"清理过期会话: {session_id}")


class RequestReceiver:
    """请求接收模块"""
    
    def __init__(self, port: int = 8888):
        self.port = port
        self.logger = setup_logger("RequestReceiver")
        self.socket = None
        self.running = False
        self.accept_thread = None
        self.client_handlers = []
        
    def start(self):
        """启动接收器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind(('localhost', self.port))
            self.socket.listen(10)
            
            self.running = True
            self.accept_thread = threading.Thread(target=self._accept_connections)
            self.accept_thread.daemon = True
            self.accept_thread.start()
            
            self.logger.info(f"请求接收器启动，监听端口: {self.port}")
            
        except Exception as e:
            self.logger.error(f"启动请求接收器失败: {e}")
    
    def stop(self):
        """停止接收器"""
        self.running = False
        
        if self.socket:
            self.socket.close()
            
        if self.accept_thread:
            self.accept_thread.join()
            
        for handler in self.client_handlers:
            handler.join()
            
        self.logger.info("请求接收器已停止")
    
    def _accept_connections(self):
        """接受连接"""
        while self.running:
            try:
                client_socket, client_address = self.socket.accept()
                self.logger.info(f"接受新连接: {client_address}")
                
                handler = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket, client_address)
                )
                handler.daemon = True
                handler.start()
                self.client_handlers.append(handler)
                
            except Exception as e:
                if self.running:
                    self.logger.error(f"接受连接失败: {e}")
    
    def _handle_client(self, client_socket, client_address):
        """处理客户端连接"""
        try:
            while self.running:
                data = client_socket.recv(4096)
                if not data:
                    break
                
                # 处理接收到的数据
                self._process_received_data(data, client_address)
                
        except Exception as e:
            self.logger.error(f"处理客户端连接失败: {e}")
        finally:
            client_socket.close()
    
    def _process_received_data(self, data: bytes, client_address):
        """处理接收到的数据"""
        try:
            message_data = json.loads(data.decode('utf-8'))
            self.logger.debug(f"接收到消息: {message_data} from {client_address}")
            
        except Exception as e:
            self.logger.error(f"处理接收数据失败: {e}")


class MessageProcessor:
    """消息处理转发模块"""
    
    def __init__(self):
        self.logger = setup_logger("MessageProcessor")
        self.message_queue = queue.Queue()
        self.processors: Dict[MessageType, List] = {}
        self.processing_thread = None
        self.running = False
        
    def start(self):
        """启动消息处理器"""
        self.running = True
        self.processing_thread = threading.Thread(target=self._process_messages)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
        self.logger.info("消息处理器已启动")
    
    def stop(self):
        """停止消息处理器"""
        self.running = False
        if self.processing_thread:
            self.processing_thread.join()
        self.logger.info("消息处理器已停止")
    
    def register_processor(self, message_type: MessageType, processor_func):
        """注册消息处理器"""
        if message_type not in self.processors:
            self.processors[message_type] = []
        self.processors[message_type].append(processor_func)
        
    def send_message(self, message: Message):
        """发送消息到处理队列"""
        self.message_queue.put(message)
    
    def _process_messages(self):
        """处理消息循环"""
        while self.running:
            try:
                message = self.message_queue.get(timeout=1.0)
                self._handle_message(message)
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"处理消息失败: {e}")
    
    def _handle_message(self, message: Message):
        """处理单个消息"""
        processors = self.processors.get(message.message_type, [])
        
        for processor in processors:
            try:
                processor(message)
            except Exception as e:
                self.logger.error(f"消息处理器执行失败: {e}")


class NonBlockingOptimizer:
    """非阻塞通信优化模块"""
    
    def __init__(self):
        self.logger = setup_logger("NonBlockingOptimizer")
        self.connection_pool = {}
        self.pool_lock = threading.RLock()
        
    def get_connection(self, target: str):
        """获取连接"""
        with self.pool_lock:
            if target not in self.connection_pool:
                self.connection_pool[target] = self._create_connection(target)
            return self.connection_pool[target]
    
    def _create_connection(self, target: str):
        """创建连接"""
        # 模拟连接创建
        return {'target': target, 'created_time': time.time()}
    
    def optimize_transmission(self, data: bytes, target: str) -> bool:
        """优化传输"""
        try:
            connection = self.get_connection(target)
            # 模拟优化传输
            self.logger.debug(f"优化传输到 {target}, 数据大小: {len(data)}")
            return True
            
        except Exception as e:
            self.logger.error(f"优化传输失败: {e}")
            return False


class CommunicationModule:
    """通信模块主类"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("CommunicationModule")
        
        # 核心组件
        self.session_manager = SessionManager()
        self.request_receiver = RequestReceiver(
            port=config.get('communication_port', 8888)
        )
        self.message_processor = MessageProcessor()
        self.optimizer = NonBlockingOptimizer()
        
        # 统计信息
        self.stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'active_sessions': 0,
            'total_sessions': 0
        }
        
        # 注册消息处理器
        self._register_message_processors()
        
        self.logger.info("通信模块初始化完成")
    
    def _register_message_processors(self):
        """注册消息处理器"""
        self.message_processor.register_processor(
            MessageType.TOPOLOGY_REQUEST, 
            self._handle_topology_request
        )
        self.message_processor.register_processor(
            MessageType.ROUTE_REQUEST, 
            self._handle_route_request
        )
        self.message_processor.register_processor(
            MessageType.STATUS_REQUEST, 
            self._handle_status_request
        )
    
    def start(self):
        """启动通信模块"""
        try:
            self.request_receiver.start()
            self.message_processor.start()
            self.logger.info("通信模块已启动")
            
        except Exception as e:
            self.logger.error(f"启动通信模块失败: {e}")
    
    def stop(self):
        """停止通信模块"""
        self.request_receiver.stop()
        self.message_processor.stop()
        self.logger.info("通信模块已停止")
    
    def send_message(self, message: Message) -> bool:
        """发送消息"""
        try:
            self.message_processor.send_message(message)
            self.stats['messages_sent'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    def create_session(self, client_info: Dict) -> str:
        """创建通信会话"""
        session_id = f"session_{int(time.time() * 1000)}"
        
        if self.session_manager.create_session(session_id, client_info):
            self.stats['total_sessions'] += 1
            self.stats['active_sessions'] += 1
            return session_id
        
        return ""
    
    def close_session(self, session_id: str):
        """关闭会话"""
        self.session_manager.close_session(session_id)
        self.stats['active_sessions'] = max(0, self.stats['active_sessions'] - 1)
    
    def _handle_topology_request(self, message: Message):
        """处理拓扑请求"""
        self.logger.info(f"处理拓扑请求: {message.message_id}")
        
        # 模拟拓扑响应
        response = Message(
            message_id=f"resp_{message.message_id}",
            message_type=MessageType.TOPOLOGY_RESPONSE,
            source="topology_controller",
            destination=message.source,
            payload={'topology': 'mock_topology_data'},
            timestamp=time.time()
        )
        
        self.send_message(response)
    
    def _handle_route_request(self, message: Message):
        """处理路由请求"""
        self.logger.info(f"处理路由请求: {message.message_id}")
        
        # 模拟路由响应
        response = Message(
            message_id=f"resp_{message.message_id}",
            message_type=MessageType.ROUTE_RESPONSE,
            source="path_calculator",
            destination=message.source,
            payload={'route': 'mock_route_data'},
            timestamp=time.time()
        )
        
        self.send_message(response)
    
    def _handle_status_request(self, message: Message):
        """处理状态请求"""
        self.logger.info(f"处理状态请求: {message.message_id}")
        
        # 模拟状态响应
        response = Message(
            message_id=f"resp_{message.message_id}",
            message_type=MessageType.STATUS_RESPONSE,
            source="communication_module",
            destination=message.source,
            payload={'status': self.get_statistics()},
            timestamp=time.time()
        )
        
        self.send_message(response)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'messages_sent': self.stats['messages_sent'],
            'messages_received': self.stats['messages_received'],
            'active_sessions': self.stats['active_sessions'],
            'total_sessions': self.stats['total_sessions'],
            'queue_size': self.message_processor.message_queue.qsize()
        }
    
    def broadcast_message(self, message: Message):
        """广播消息"""
        self.logger.info(f"广播消息: {message.message_id}")
        # 实现广播逻辑
        
    def send_to_module(self, target_module: str, data: Dict[str, Any]) -> bool:
        """发送消息到指定模块"""
        message = Message(
            message_id=f"msg_{int(time.time() * 1000)}",
            message_type=MessageType.CONTROL_COMMAND,
            source="communication_module",
            destination=target_module,
            payload=data,
            timestamp=time.time()
        )
        
        return self.send_message(message) 