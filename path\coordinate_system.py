"""
坐标系统模块

提供各种坐标系统转换和向量计算功能，支持：
- 笛卡尔坐标系
- 球坐标系  
- 地理坐标系（经纬度）
- ECI (Earth-Centered Inertial) 坐标系
- ECEF (Earth-Centered Earth-Fixed) 坐标系
"""

import math
from typing import Tuple, Optional
from dataclasses import dataclass


@dataclass
class Vector3D:
    """三维向量类"""
    x: float
    y: float  
    z: float
    
    def __add__(self, other: 'Vector3D') -> 'Vector3D':
        """向量加法"""
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other: 'Vector3D') -> 'Vector3D':
        """向量减法"""
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar: float) -> 'Vector3D':
        """向量标量乘法"""
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def __truediv__(self, scalar: float) -> 'Vector3D':
        """向量标量除法"""
        return Vector3D(self.x / scalar, self.y / scalar, self.z / scalar)
    
    def magnitude(self) -> float:
        """计算向量模长"""
        return math.sqrt(self.x**2 + self.y**2 + self.z**2)
    
    def normalize(self) -> 'Vector3D':
        """向量单位化"""
        mag = self.magnitude()
        if mag == 0:
            return Vector3D(0, 0, 0)
        return self / mag
    
    def dot(self, other: 'Vector3D') -> float:
        """向量点积"""
        return self.x * other.x + self.y * other.y + self.z * other.z
    
    def cross(self, other: 'Vector3D') -> 'Vector3D':
        """向量叉积"""
        return Vector3D(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x
        )
    
    def distance_to(self, other: 'Vector3D') -> float:
        """计算到另一个向量的距离"""
        return (self - other).magnitude()
    
    def angle_to(self, other: 'Vector3D') -> float:
        """计算与另一个向量的夹角（弧度）"""
        cos_angle = self.dot(other) / (self.magnitude() * other.magnitude())
        # 处理数值误差
        cos_angle = max(-1.0, min(1.0, cos_angle))
        return math.acos(cos_angle)


@dataclass
class LatLonAlt:
    """地理坐标（纬度、经度、高度）"""
    latitude: float   # 纬度（度）
    longitude: float  # 经度（度） 
    altitude: float   # 高度（米）
    
    def to_radians(self) -> 'LatLonAlt':
        """转换为弧度"""
        return LatLonAlt(
            math.radians(self.latitude),
            math.radians(self.longitude),
            self.altitude
        )
    
    def to_degrees(self) -> 'LatLonAlt':
        """转换为度"""
        return LatLonAlt(
            math.degrees(self.latitude),
            math.degrees(self.longitude),
            self.altitude
        )


class CoordinateSystem:
    """坐标系统转换类"""
    
    # 地球参数 (WGS84)
    EARTH_RADIUS = 6378137.0  # 地球赤道半径 (米)
    EARTH_FLATTENING = 1.0 / 298.257223563  # 地球扁率
    EARTH_ROTATION_RATE = 7.2921159e-5  # 地球自转角速度 (rad/s)
    
    # 常用常数
    DEG_TO_RAD = math.pi / 180.0
    RAD_TO_DEG = 180.0 / math.pi
    
    @classmethod
    def lla_to_ecef(cls, lla: LatLonAlt) -> Vector3D:
        """
        地理坐标转ECEF坐标
        
        Args:
            lla: 地理坐标 (纬度、经度、高度)
            
        Returns:
            ECEF坐标系下的位置向量
        """
        lat_rad = math.radians(lla.latitude)
        lon_rad = math.radians(lla.longitude)
        alt = lla.altitude
        
        # 计算第一偏心率的平方
        e2 = 2 * cls.EARTH_FLATTENING - cls.EARTH_FLATTENING**2
        
        # 计算卯酉圈曲率半径
        N = cls.EARTH_RADIUS / math.sqrt(1 - e2 * math.sin(lat_rad)**2)
        
        # 计算ECEF坐标
        x = (N + alt) * math.cos(lat_rad) * math.cos(lon_rad)
        y = (N + alt) * math.cos(lat_rad) * math.sin(lon_rad)
        z = (N * (1 - e2) + alt) * math.sin(lat_rad)
        
        return Vector3D(x, y, z)
    
    @classmethod
    def ecef_to_lla(cls, ecef: Vector3D) -> LatLonAlt:
        """
        ECEF坐标转地理坐标
        
        Args:
            ecef: ECEF坐标系下的位置向量
            
        Returns:
            地理坐标 (纬度、经度、高度)
        """
        x, y, z = ecef.x, ecef.y, ecef.z
        
        # 计算经度
        lon = math.atan2(y, x)
        
        # 计算纬度和高度（迭代方法）
        p = math.sqrt(x**2 + y**2)
        e2 = 2 * cls.EARTH_FLATTENING - cls.EARTH_FLATTENING**2
        
        # 初始纬度估计
        lat = math.atan2(z, p * (1 - e2))
        
        # 迭代计算精确纬度和高度
        for _ in range(10):  # 通常几次迭代就足够了
            N = cls.EARTH_RADIUS / math.sqrt(1 - e2 * math.sin(lat)**2)
            alt = p / math.cos(lat) - N
            lat_new = math.atan2(z, p * (1 - e2 * N / (N + alt)))
            
            if abs(lat_new - lat) < 1e-12:
                break
            lat = lat_new
        
        # 最终高度计算
        N = cls.EARTH_RADIUS / math.sqrt(1 - e2 * math.sin(lat)**2)
        alt = p / math.cos(lat) - N
        
        return LatLonAlt(
            math.degrees(lat),
            math.degrees(lon),
            alt
        )
    
    @classmethod
    def ecef_to_eci(cls, ecef: Vector3D, greenwich_sidereal_time: float) -> Vector3D:
        """
        ECEF坐标转ECI坐标
        
        Args:
            ecef: ECEF坐标系下的位置向量
            greenwich_sidereal_time: 格林尼治恒星时 (弧度)
            
        Returns:
            ECI坐标系下的位置向量
        """
        cos_gst = math.cos(greenwich_sidereal_time)
        sin_gst = math.sin(greenwich_sidereal_time)
        
        # 旋转矩阵
        x_eci = cos_gst * ecef.x + sin_gst * ecef.y
        y_eci = -sin_gst * ecef.x + cos_gst * ecef.y
        z_eci = ecef.z
        
        return Vector3D(x_eci, y_eci, z_eci)
    
    @classmethod
    def eci_to_ecef(cls, eci: Vector3D, greenwich_sidereal_time: float) -> Vector3D:
        """
        ECI坐标转ECEF坐标
        
        Args:
            eci: ECI坐标系下的位置向量
            greenwich_sidereal_time: 格林尼治恒星时 (弧度)
            
        Returns:
            ECEF坐标系下的位置向量
        """
        cos_gst = math.cos(greenwich_sidereal_time)
        sin_gst = math.sin(greenwich_sidereal_time)
        
        # 旋转矩阵（逆变换）
        x_ecef = cos_gst * eci.x - sin_gst * eci.y
        y_ecef = sin_gst * eci.x + cos_gst * eci.y
        z_ecef = eci.z
        
        return Vector3D(x_ecef, y_ecef, z_ecef)
    
    @classmethod
    def calculate_greenwich_sidereal_time(cls, julian_date: float) -> float:
        """
        计算格林尼治恒星时
        
        Args:
            julian_date: 儒略日
            
        Returns:
            格林尼治恒星时 (弧度)
        """
        # 简化的格林尼治恒星时计算
        t = (julian_date - 2451545.0) / 36525.0  # 世纪数
        
        # 格林尼治平恒星时 (度)
        gmst_deg = 280.46061837 + 360.98564736629 * (julian_date - 2451545.0) + \
                   0.000387933 * t**2 - t**3 / 38710000.0
        
        # 转换为0-360度范围
        gmst_deg = gmst_deg % 360.0
        
        # 转换为弧度
        return math.radians(gmst_deg)
    
    @classmethod
    def spherical_to_cartesian(cls, radius: float, azimuth: float, elevation: float) -> Vector3D:
        """
        球坐标转笛卡尔坐标
        
        Args:
            radius: 径向距离
            azimuth: 方位角 (弧度，从北方向顺时针)
            elevation: 仰角 (弧度，从水平面向上)
            
        Returns:
            笛卡尔坐标
        """
        x = radius * math.cos(elevation) * math.sin(azimuth)
        y = radius * math.cos(elevation) * math.cos(azimuth)
        z = radius * math.sin(elevation)
        
        return Vector3D(x, y, z)
    
    @classmethod
    def cartesian_to_spherical(cls, position: Vector3D) -> Tuple[float, float, float]:
        """
        笛卡尔坐标转球坐标
        
        Args:
            position: 笛卡尔坐标位置向量
            
        Returns:
            (radius, azimuth, elevation) 球坐标
        """
        radius = position.magnitude()
        
        if radius == 0:
            return 0.0, 0.0, 0.0
        
        elevation = math.asin(position.z / radius)
        azimuth = math.atan2(position.x, position.y)
        
        return radius, azimuth, elevation
    
    @classmethod
    def calculate_distance_and_bearing(cls, pos1: LatLonAlt, pos2: LatLonAlt) -> Tuple[float, float]:
        """
        计算两个地理位置之间的距离和方位角
        
        Args:
            pos1: 起始位置
            pos2: 目标位置
            
        Returns:
            (distance, bearing) 距离(米)和方位角(弧度)
        """
        lat1, lon1 = math.radians(pos1.latitude), math.radians(pos1.longitude)
        lat2, lon2 = math.radians(pos2.latitude), math.radians(pos2.longitude)
        
        # 使用Haversine公式计算距离
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = cls.EARTH_RADIUS * c
        
        # 计算方位角
        y = math.sin(dlon) * math.cos(lat2)
        x = math.cos(lat1) * math.sin(lat2) - math.sin(lat1) * math.cos(lat2) * math.cos(dlon)
        bearing = math.atan2(y, x)
        
        return distance, bearing
    
    @classmethod
    def local_to_global(cls, local_pos: Vector3D, reference_lla: LatLonAlt) -> Vector3D:
        """
        局部坐标转全局ECEF坐标
        
        Args:
            local_pos: 局部坐标系位置
            reference_lla: 参考点地理坐标
            
        Returns:
            全局ECEF坐标
        """
        # 将参考点转换为ECEF
        ref_ecef = cls.lla_to_ecef(reference_lla)
        
        # 计算局部坐标系的旋转矩阵
        lat_rad = math.radians(reference_lla.latitude)
        lon_rad = math.radians(reference_lla.longitude)
        
        # ENU to ECEF 旋转矩阵
        sin_lat, cos_lat = math.sin(lat_rad), math.cos(lat_rad)
        sin_lon, cos_lon = math.sin(lon_rad), math.cos(lon_rad)
        
        # 旋转变换
        x_ecef = -sin_lon * local_pos.x - sin_lat * cos_lon * local_pos.y + cos_lat * cos_lon * local_pos.z
        y_ecef = cos_lon * local_pos.x - sin_lat * sin_lon * local_pos.y + cos_lat * sin_lon * local_pos.z
        z_ecef = cos_lat * local_pos.y + sin_lat * local_pos.z
        
        return ref_ecef + Vector3D(x_ecef, y_ecef, z_ecef)
    
    @classmethod
    def global_to_local(cls, global_pos: Vector3D, reference_lla: LatLonAlt) -> Vector3D:
        """
        全局ECEF坐标转局部坐标
        
        Args:
            global_pos: 全局ECEF坐标
            reference_lla: 参考点地理坐标
            
        Returns:
            局部坐标系位置
        """
        # 将参考点转换为ECEF
        ref_ecef = cls.lla_to_ecef(reference_lla)
        
        # 计算相对位置
        rel_pos = global_pos - ref_ecef
        
        # 计算局部坐标系的旋转矩阵
        lat_rad = math.radians(reference_lla.latitude)
        lon_rad = math.radians(reference_lla.longitude)
        
        # ECEF to ENU 旋转矩阵
        sin_lat, cos_lat = math.sin(lat_rad), math.cos(lat_rad)
        sin_lon, cos_lon = math.sin(lon_rad), math.cos(lon_rad)
        
        # 旋转变换
        x_local = -sin_lon * rel_pos.x + cos_lon * rel_pos.y
        y_local = -sin_lat * cos_lon * rel_pos.x - sin_lat * sin_lon * rel_pos.y + cos_lat * rel_pos.z
        z_local = cos_lat * cos_lon * rel_pos.x + cos_lat * sin_lon * rel_pos.y + sin_lat * rel_pos.z
        
        return Vector3D(x_local, y_local, z_local) 