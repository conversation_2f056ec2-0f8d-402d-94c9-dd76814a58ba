# SDT - 卫星数字孪生系统

## 系统概述

SDT（Satellite Digital Twin）是一个完整的卫星数字孪生系统，采用Python语言开发，实现了卫星通信网络的全面仿真和可视化。系统采用分布式架构，支持大规模卫星星座的实时仿真。

## 系统架构

```
SDT系统架构
├── 主程序层 (sdt_main.py)
├── 核心功能层 (core/)
│   ├── 卫星拓扑控制器 (satellite_topology_controller.py)
│   ├── 路径计算控制器 (satellite_path_calculator.py) 
│   ├── 卫星模拟器 (satellite_simulator.py)
│   └── 通信模块 (communication_module.py)
├── 物理层仿真 (physics/)
│   └── 物理层仿真器 (physical_layer_simulator.py)
├── 可视化界面 (gui/)
│   └── 可视化模块 (visualization_module.py)
└── 支撑模块 (utils/)
    ├── 配置管理器 (config_manager.py)
    └── 日志系统 (logger.py)
```

## 核心功能

### 1. 卫星拓扑控制器
- **轨道计算**：基于SGP4模型的精确轨道预测
- **拓扑生成**：星间链路和星地链路的动态建立
- **星座管理**：支持多种星座配置（Starlink、OneWeb等）
- **位置更新**：实时更新卫星位置和速度

### 2. 路径计算控制器
- **多算法支持**：Dijkstra、BFS、Floyd-Warshall算法
- **智能缓存**：LRU缓存机制提升计算效率
- **路由优化**：支持多种路径优化策略
- **批量计算**：支持大规模路径计算请求

### 3. 卫星模拟器
- **离散事件引擎**：高精度时间驱动仿真
- **实时/非实时模式**：支持多种仿真速度
- **半实物接入**：支持硬件在环仿真
- **状态管理**：完整的仿真状态跟踪

### 4. 通信模块
- **消息处理**：高效的消息路由和转发
- **会话管理**：支持多用户并发访问
- **非阻塞优化**：异步通信架构
- **协议支持**：多种通信协议兼容

### 5. 物理层仿真
- **链路预算计算**：完整的功率链路分析
- **传播模型**：自由空间、大气、雨衰等损耗模型
- **天线建模**：多种天线类型和方向图
- **信道仿真**：噪声、多普勒、延迟等特性

### 6. 可视化系统
- **3D/2D显示**：灵活的视角切换
- **实时渲染**：流畅的动画效果
- **交互控制**：丰富的用户交互功能
- **状态监控**：系统运行状态实时显示

## 技术特性

### 性能指标
- 支持2000+颗卫星仿真
- 毫秒级路径计算响应
- 30FPS实时可视化
- 多线程并发处理

### 算法实现
- **SGP4轨道模型**：精确的卫星轨道预测
- **Dijkstra算法**：最短路径计算
- **Floyd-Warshall算法**：全源最短路径
- **滑动窗口算法**：动态拓扑更新

### 工程特性
- 模块化设计，易于扩展
- 完整的日志系统
- 灵活的配置管理
- 异常处理和容错机制

## 快速开始

### 环境要求
- Python 3.8+
- NumPy
- Tkinter（GUI支持）

### 安装依赖
```bash
pip install numpy
```

### 运行系统
```bash
python sdt_main.py
```

### 基本使用
1. 启动系统后，会自动加载默认星座配置
2. 通过可视化界面监控系统状态
3. 使用控制面板调整仿真参数
4. 查看实时统计信息和性能指标

## 配置文件

系统使用JSON格式的配置文件（config.json），包含：
- 系统基本参数
- 星座配置
- 物理层参数
- 可视化设置

示例配置：
```json
{
  "system": {
    "max_satellites": 2000,
    "simulation_mode": "real_time",
    "default_algorithm": "dijkstra"
  },
  "constellations": [
    {
      "name": "Custom-LEO",
      "altitude": 550.0,
      "inclination": 53.0,
      "planes": 10,
      "satellites_per_plane": 20
    }
  ],
  "physics": {
    "frequency": 2.4e9,
    "bandwidth": 10e6,
    "tx_power": 30.0
  }
}
```

## 日志系统

系统提供完整的日志记录功能：
- 控制台实时输出
- 文件循环存储
- 多级别日志（DEBUG、INFO、WARNING、ERROR）
- 自动日志轮转

## API接口

### 主要类和方法

#### SDTSystem（主控制器）
- `initialize_modules()`: 初始化所有模块
- `start_system()`: 启动系统
- `stop_system()`: 停止系统
- `get_system_status()`: 获取系统状态

#### SatelliteTopologyController（拓扑控制器）
- `generate_constellation()`: 生成星座
- `update_satellite_positions()`: 更新卫星位置
- `generate_network_topology()`: 生成网络拓扑

#### SatellitePathCalculator（路径计算器）
- `calculate_path()`: 计算单条路径
- `calculate_multiple_paths()`: 批量路径计算
- `update_network_topology()`: 更新网络拓扑

#### PhysicalLayerSimulator（物理层仿真）
- `calculate_link_budget()`: 计算链路预算
- `update_channel_state()`: 更新信道状态
- `get_statistics()`: 获取统计信息

## 扩展开发

### 添加新的路径算法
1. 在`SatellitePathCalculator`中添加新算法类
2. 实现`calculate_shortest_path`方法
3. 在主计算器中注册新算法

### 添加新的星座类型
1. 在配置文件中定义星座参数
2. 在`SatelliteTopologyController`中添加生成逻辑

### 自定义可视化
1. 继承`VisualizationModule`类
2. 重写绘制方法
3. 添加新的交互功能

## 性能优化

### 内存优化
- 对象池技术
- 缓存管理
- 垃圾回收优化

### 计算优化
- 并行计算
- 算法缓存
- 增量更新

### 渲染优化
- 层次细节（LOD）
- 视锥剔除
- 批量渲染

## 故障排除

### 常见问题
1. **导入错误**：检查Python路径和依赖包
2. **性能问题**：调整仿真参数和可视化设置
3. **内存不足**：减少卫星数量或关闭3D显示

### 调试模式
启用调试模式：
```python
config.system_config.debug_mode = True
config.system_config.log_level = "DEBUG"
```

## 许可证

本项目仅供学习和研究使用。

## 技术支持

如有技术问题，请检查日志文件获取详细错误信息。 