import React from 'react';
import { Card, Typography, Empty, Button } from 'antd';
import { UserOutlined, PlusOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Users = () => {
  return (
    <div className="users-container">
      <div className="page-header">
        <Title level={2}>用户管理</Title>
      </div>

      <Card>
        <Empty
          image={<UserOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
          description="用户管理功能开发中..."
        >
          <Button type="primary" icon={<PlusOutlined />}>
            创建用户
          </Button>
        </Empty>
      </Card>
    </div>
  );
};

export default Users;
