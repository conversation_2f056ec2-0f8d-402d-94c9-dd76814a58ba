"""
文档模型
处理文档库管理相关的数据操作
"""

import os
from datetime import datetime
from typing import Dict, List, Optional, Any

from app.utils.redis_client import redis_client, RedisKeys


class Document:
    """文档模型类"""
    
    # 文档状态定义
    STATUSES = {
        'draft': '草稿',
        'review': '审核中',
        'published': '已发布',
        'archived': '已归档'
    }
    
    # 文档分类定义
    CATEGORIES = {
        'architecture': '架构设计',
        'api': 'API文档',
        'user_guide': '用户手册',
        'development': '开发文档',
        'testing': '测试文档',
        'deployment': '部署文档',
        'requirement': '需求文档',
        'other': '其他'
    }
    
    # 允许的文件类型
    ALLOWED_EXTENSIONS = {
        'pdf', 'doc', 'docx', 'txt', 'md', 'html',
        'ppt', 'pptx', 'xls', 'xlsx'
    }
    
    def __init__(self, doc_data: Dict[str, Any] = None):
        """初始化文档对象"""
        if doc_data:
            self.id = doc_data.get('id')
            self.title = doc_data.get('title')
            self.description = doc_data.get('description')
            self.category = doc_data.get('category', 'other')
            self.version = doc_data.get('version', '1.0.0')
            self.file_path = doc_data.get('file_path')
            self.file_name = doc_data.get('file_name')
            self.file_size = doc_data.get('file_size', 0)
            self.file_type = doc_data.get('file_type')
            self.author = doc_data.get('author')
            self.status = doc_data.get('status', 'draft')
            self.created_at = doc_data.get('created_at')
            self.updated_at = doc_data.get('updated_at')
            self.published_at = doc_data.get('published_at')
            self.tags = doc_data.get('tags', [])
            self.download_count = doc_data.get('download_count', 0)
            self.is_public = doc_data.get('is_public', False)
        else:
            self.id = None
            self.title = None
            self.description = None
            self.category = 'other'
            self.version = '1.0.0'
            self.file_path = None
            self.file_name = None
            self.file_size = 0
            self.file_type = None
            self.author = None
            self.status = 'draft'
            self.created_at = None
            self.updated_at = None
            self.published_at = None
            self.tags = []
            self.download_count = 0
            self.is_public = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'category': self.category,
            'category_name': self.CATEGORIES.get(self.category, self.category),
            'version': self.version,
            'file_path': self.file_path,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'author': self.author,
            'status': self.status,
            'status_name': self.STATUSES.get(self.status, self.status),
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'published_at': self.published_at,
            'tags': self.tags,
            'download_count': self.download_count,
            'is_public': self.is_public
        }
    
    def save(self) -> bool:
        """保存文档到Redis"""
        try:
            if not self.id:
                self.id = redis_client.generate_id('doc_')
                self.created_at = redis_client.get_timestamp()
            
            self.updated_at = redis_client.get_timestamp()
            
            # 如果状态变为已发布，记录发布时间
            if self.status == 'published' and not self.published_at:
                self.published_at = redis_client.get_timestamp()
            
            # 保存文档数据
            doc_key = RedisKeys.DOCUMENT.format(doc_id=self.id)
            doc_data = self.to_dict()
            
            if not redis_client.set(doc_key, doc_data):
                return False
            
            # 添加到主索引
            index_key = RedisKeys.DOCUMENT_INDEX
            if not redis_client.add_to_index(index_key, self.id):
                return False
            
            # 添加到分类索引
            category_key = RedisKeys.DOCUMENT_BY_CATEGORY.format(category=self.category)
            redis_client.add_to_index(category_key, self.id)
            
            return True
            
        except Exception as e:
            print(f"Error saving document: {e}")
            return False
    
    def delete(self) -> bool:
        """删除文档"""
        try:
            if not self.id:
                return False
            
            # 删除文件
            if self.file_path and os.path.exists(self.file_path):
                try:
                    os.remove(self.file_path)
                except OSError:
                    pass  # 文件删除失败不影响数据删除
            
            # 删除文档数据
            doc_key = RedisKeys.DOCUMENT.format(doc_id=self.id)
            redis_client.delete(doc_key)
            
            # 从主索引中移除
            index_key = RedisKeys.DOCUMENT_INDEX
            redis_client.remove_from_index(index_key, self.id)
            
            # 从分类索引中移除
            category_key = RedisKeys.DOCUMENT_BY_CATEGORY.format(category=self.category)
            redis_client.remove_from_index(category_key, self.id)
            
            return True
            
        except Exception as e:
            print(f"Error deleting document: {e}")
            return False
    
    def increment_download_count(self) -> bool:
        """增加下载次数"""
        try:
            self.download_count += 1
            return self.save()
        except Exception as e:
            print(f"Error incrementing download count: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, doc_id: str) -> Optional['Document']:
        """根据ID获取文档"""
        try:
            doc_key = RedisKeys.DOCUMENT.format(doc_id=doc_id)
            doc_data = redis_client.get(doc_key)
            
            if doc_data:
                return cls(doc_data)
            return None
            
        except Exception as e:
            print(f"Error getting document by id: {e}")
            return None
    
    @classmethod
    def get_list(cls, page: int = 1, size: int = 20, **filters) -> Dict[str, Any]:
        """获取文档列表"""
        try:
            # 根据过滤条件选择索引
            if filters.get('category'):
                index_key = RedisKeys.DOCUMENT_BY_CATEGORY.format(category=filters['category'])
            else:
                index_key = RedisKeys.DOCUMENT_INDEX
            
            # 获取所有文档ID
            doc_ids = redis_client.get_from_index(index_key, reverse=True)
            
            # 应用其他过滤条件
            filtered_ids = []
            for doc_id in doc_ids:
                doc = cls.get_by_id(doc_id)
                if doc and cls._match_filters(doc, filters):
                    filtered_ids.append(doc_id)
            
            # 分页
            total = len(filtered_ids)
            start = (page - 1) * size
            end = start + size
            page_ids = filtered_ids[start:end]
            
            # 获取文档数据
            documents = []
            for doc_id in page_ids:
                doc = cls.get_by_id(doc_id)
                if doc:
                    documents.append(doc.to_dict())
            
            return {
                'documents': documents,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total,
                    'pages': (total + size - 1) // size
                }
            }
            
        except Exception as e:
            print(f"Error getting document list: {e}")
            return {
                'documents': [],
                'pagination': {'page': 1, 'size': size, 'total': 0, 'pages': 0}
            }
    
    @classmethod
    def _match_filters(cls, doc: 'Document', filters: Dict[str, Any]) -> bool:
        """检查文档是否匹配过滤条件"""
        if filters.get('status') and doc.status != filters['status']:
            return False
        
        if filters.get('author') and doc.author != filters['author']:
            return False
        
        if filters.get('tags'):
            filter_tags = filters['tags'] if isinstance(filters['tags'], list) else [filters['tags']]
            if not any(tag in doc.tags for tag in filter_tags):
                return False
        
        if filters.get('keyword'):
            keyword = filters['keyword'].lower()
            if (keyword not in doc.title.lower() and 
                keyword not in (doc.description or '').lower()):
                return False
        
        if filters.get('is_public') is not None:
            if doc.is_public != filters['is_public']:
                return False
        
        return True
    
    @classmethod
    def get_categories(cls) -> Dict[str, str]:
        """获取文档分类列表"""
        return cls.CATEGORIES.copy()
    
    @classmethod
    def get_stats(cls) -> Dict[str, Any]:
        """获取文档统计信息"""
        try:
            index_key = RedisKeys.DOCUMENT_INDEX
            doc_ids = redis_client.get_from_index(index_key)
            
            stats = {
                'total': len(doc_ids),
                'by_category': {},
                'by_status': {},
                'by_author': {},
                'total_downloads': 0,
                'published_this_month': 0
            }
            
            current_month = datetime.now().strftime('%Y-%m')
            
            for doc_id in doc_ids:
                doc = cls.get_by_id(doc_id)
                if doc:
                    # 按分类统计
                    category_name = cls.CATEGORIES.get(doc.category, doc.category)
                    stats['by_category'][category_name] = stats['by_category'].get(category_name, 0) + 1
                    
                    # 按状态统计
                    status_name = cls.STATUSES.get(doc.status, doc.status)
                    stats['by_status'][status_name] = stats['by_status'].get(status_name, 0) + 1
                    
                    # 按作者统计
                    if doc.author:
                        stats['by_author'][doc.author] = stats['by_author'].get(doc.author, 0) + 1
                    
                    # 总下载次数
                    stats['total_downloads'] += doc.download_count
                    
                    # 本月发布的文档
                    if (doc.published_at and 
                        doc.published_at.startswith(current_month)):
                        stats['published_this_month'] += 1
            
            return stats
            
        except Exception as e:
            print(f"Error getting document stats: {e}")
            return {
                'total': 0, 'by_category': {}, 'by_status': {}, 
                'by_author': {}, 'total_downloads': 0, 'published_this_month': 0
            }
    
    @classmethod
    def search(cls, keyword: str, category: str = None, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """搜索文档"""
        try:
            filters = {'keyword': keyword}
            if category:
                filters['category'] = category
            
            return cls.get_list(page=page, size=size, **filters)
            
        except Exception as e:
            print(f"Error searching documents: {e}")
            return {
                'documents': [],
                'pagination': {'page': 1, 'size': size, 'total': 0, 'pages': 0}
            }
