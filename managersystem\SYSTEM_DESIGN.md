# SDT管理系统设计方案

## 1. 系统概述

### 1.1 项目背景
基于SDT卫星数字孪生系统，构建一个独立的Web管理系统，用于支持项目开发和运维过程中的管理需求。

### 1.2 核心功能
- **缺陷问题管理**：Bug跟踪、问题分配、状态管理、优先级管理
- **文档库管理**：技术文档、用户手册、API文档的版本管理和分类存储
- **产品库管理**：产品版本管理、发布管理、配置管理
- **账号权限管理**：用户认证、角色管理、权限控制

### 1.3 技术架构
- **后端**：Flask + Redis
- **前端**：React + Ant Design
- **数据存储**：Redis（主存储）+ 文件系统（文档存储）
- **认证**：JWT Token
- **API**：RESTful API

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React前端     │    │   Flask后端     │    │   Redis数据库   │
│                 │    │                 │    │                 │
│ - 用户界面      │◄──►│ - API接口       │◄──►│ - 数据存储      │
│ - 状态管理      │    │ - 业务逻辑      │    │ - 缓存管理      │
│ - 路由管理      │    │ - 权限控制      │    │ - 会话管理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   文件系统      │
                       │                 │
                       │ - 文档存储      │
                       │ - 附件管理      │
                       └─────────────────┘
```

### 2.2 目录结构
```
managersystem/
├── backend/                    # Flask后端
│   ├── app/
│   │   ├── __init__.py
│   │   ├── models/            # 数据模型
│   │   ├── api/               # API路由
│   │   ├── services/          # 业务逻辑
│   │   ├── utils/             # 工具函数
│   │   └── auth/              # 认证模块
│   ├── config.py              # 配置文件
│   ├── requirements.txt       # 依赖包
│   └── run.py                 # 启动文件
├── frontend/                   # React前端
│   ├── public/
│   ├── src/
│   │   ├── components/        # 组件
│   │   ├── pages/             # 页面
│   │   ├── services/          # API服务
│   │   ├── utils/             # 工具函数
│   │   └── store/             # 状态管理
│   ├── package.json
│   └── webpack.config.js
├── storage/                    # 文件存储
│   ├── documents/             # 文档文件
│   ├── attachments/           # 附件文件
│   └── uploads/               # 上传文件
└── docs/                      # 项目文档
    ├── API.md                 # API文档
    ├── DEPLOYMENT.md          # 部署文档
    └── USER_GUIDE.md          # 用户手册
```

## 3. 数据模型设计

### 3.1 Redis数据结构设计

#### 用户管理
```
users:{user_id} = {
    "id": "user_001",
    "username": "admin",
    "email": "<EMAIL>",
    "password_hash": "...",
    "role": "admin",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "last_login": "2024-01-01T00:00:00Z"
}

user_sessions:{token} = {
    "user_id": "user_001",
    "expires_at": "2024-01-01T01:00:00Z"
}
```

#### 缺陷问题管理
```
issues:{issue_id} = {
    "id": "issue_001",
    "title": "系统启动异常",
    "description": "详细描述...",
    "type": "bug",           # bug, feature, task
    "priority": "high",      # low, medium, high, critical
    "status": "open",        # open, in_progress, resolved, closed
    "assignee": "user_001",
    "reporter": "user_002",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "tags": ["backend", "startup"],
    "attachments": ["file_001", "file_002"]
}

issue_comments:{issue_id} = [
    {
        "id": "comment_001",
        "content": "评论内容",
        "author": "user_001",
        "created_at": "2024-01-01T00:00:00Z"
    }
]
```

#### 文档库管理
```
documents:{doc_id} = {
    "id": "doc_001",
    "title": "系统架构文档",
    "category": "architecture",
    "version": "1.0.0",
    "file_path": "/storage/documents/arch_v1.0.0.pdf",
    "file_size": 1024000,
    "file_type": "pdf",
    "author": "user_001",
    "status": "published",   # draft, review, published, archived
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "tags": ["architecture", "design"],
    "download_count": 0
}

doc_categories = {
    "architecture": "架构设计",
    "api": "API文档",
    "user_guide": "用户手册",
    "development": "开发文档"
}
```

#### 产品库管理
```
products:{product_id} = {
    "id": "product_001",
    "name": "SDT卫星数字孪生系统",
    "version": "1.0.0",
    "description": "产品描述",
    "status": "released",    # development, testing, released, deprecated
    "release_date": "2024-01-01",
    "changelog": "版本更新说明",
    "files": ["file_001", "file_002"],
    "dependencies": ["numpy>=1.20.0"],
    "created_by": "user_001",
    "created_at": "2024-01-01T00:00:00Z"
}

product_files:{file_id} = {
    "id": "file_001",
    "filename": "sdt_v1.0.0.zip",
    "file_path": "/storage/products/sdt_v1.0.0.zip",
    "file_size": 10240000,
    "checksum": "md5_hash_value",
    "upload_time": "2024-01-01T00:00:00Z"
}
```

## 4. API接口设计

### 4.1 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息
- `PUT /api/auth/profile` - 更新用户信息

### 4.2 用户管理接口
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `GET /api/users/{id}` - 获取用户详情
- `PUT /api/users/{id}` - 更新用户信息
- `DELETE /api/users/{id}` - 删除用户

### 4.3 缺陷问题管理接口
- `GET /api/issues` - 获取问题列表
- `POST /api/issues` - 创建问题
- `GET /api/issues/{id}` - 获取问题详情
- `PUT /api/issues/{id}` - 更新问题
- `DELETE /api/issues/{id}` - 删除问题
- `POST /api/issues/{id}/comments` - 添加评论
- `GET /api/issues/{id}/comments` - 获取评论列表

### 4.4 文档库管理接口
- `GET /api/documents` - 获取文档列表
- `POST /api/documents` - 上传文档
- `GET /api/documents/{id}` - 获取文档详情
- `PUT /api/documents/{id}` - 更新文档信息
- `DELETE /api/documents/{id}` - 删除文档
- `GET /api/documents/{id}/download` - 下载文档

### 4.5 产品库管理接口
- `GET /api/products` - 获取产品列表
- `POST /api/products` - 创建产品版本
- `GET /api/products/{id}` - 获取产品详情
- `PUT /api/products/{id}` - 更新产品信息
- `DELETE /api/products/{id}` - 删除产品版本
- `POST /api/products/{id}/files` - 上传产品文件
- `GET /api/products/{id}/files/{file_id}/download` - 下载产品文件

## 5. 权限管理设计

### 5.1 角色定义
- **超级管理员(super_admin)**：系统最高权限，可管理所有功能
- **管理员(admin)**：可管理用户、问题、文档、产品
- **开发者(developer)**：可创建和管理问题、上传文档、查看产品
- **测试员(tester)**：可创建问题、查看文档和产品
- **访客(guest)**：只读权限，可查看公开文档和产品

### 5.2 权限矩阵
| 功能模块 | super_admin | admin | developer | tester | guest |
|---------|-------------|-------|-----------|--------|-------|
| 用户管理 | ✓ | ✓ | ✗ | ✗ | ✗ |
| 问题创建 | ✓ | ✓ | ✓ | ✓ | ✗ |
| 问题管理 | ✓ | ✓ | ✓ | ✗ | ✗ |
| 文档上传 | ✓ | ✓ | ✓ | ✗ | ✗ |
| 文档管理 | ✓ | ✓ | ✓ | ✗ | ✗ |
| 文档查看 | ✓ | ✓ | ✓ | ✓ | ✓ |
| 产品管理 | ✓ | ✓ | ✓ | ✗ | ✗ |
| 产品查看 | ✓ | ✓ | ✓ | ✓ | ✓ |

## 6. 前端页面设计

### 6.1 主要页面
1. **登录页面** - 用户认证
2. **仪表板** - 系统概览和统计
3. **问题管理** - 问题列表、详情、创建、编辑
4. **文档库** - 文档分类、上传、下载、预览
5. **产品库** - 产品版本、文件管理、发布管理
6. **用户管理** - 用户列表、角色分配、权限管理
7. **系统设置** - 系统配置、日志查看

### 6.2 UI组件设计
- 使用Ant Design组件库
- 响应式设计，支持移动端
- 统一的主题风格
- 国际化支持（中英文）

## 7. 部署方案

### 7.1 开发环境
- Python 3.8+
- Node.js 16+
- Redis 6.0+

### 7.2 生产环境
- 使用Docker容器化部署
- Nginx作为反向代理
- Redis持久化配置
- 日志收集和监控

## 8. 开发计划

### 阶段一：基础框架搭建（1-2周）
- 后端Flask框架搭建
- 前端React项目初始化
- Redis连接和基础配置
- 用户认证系统

### 阶段二：核心功能开发（3-4周）
- 缺陷问题管理模块
- 文档库管理模块
- 产品库管理模块
- 权限管理完善

### 阶段三：界面优化和测试（1-2周）
- 前端界面优化
- 功能测试和bug修复
- 性能优化
- 文档编写

### 阶段四：部署和上线（1周）
- 生产环境部署
- 数据迁移
- 用户培训
- 系统监控

这个设计方案提供了一个完整的管理系统架构，您觉得这个方案如何？有什么需要调整或补充的地方吗？
