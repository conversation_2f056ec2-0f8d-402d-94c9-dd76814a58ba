#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器模块
管理SDT系统的所有配置参数
"""

import json
import os
from typing import Any, Dict, Optional
from dataclasses import dataclass, asdict


@dataclass
class SystemConfig:
    """系统配置"""
    # 系统基本配置
    system_name: str = "SDT"
    version: str = "1.0.0"
    debug_mode: bool = False
    log_level: str = "INFO"
    
    # 仿真配置
    simulation_mode: str = "real_time"  # real_time, fast, step
    time_acceleration: float = 1.0
    max_satellites: int = 2000
    update_interval: float = 1.0
    
    # 网络配置
    default_algorithm: str = "dijkstra"
    cache_size: int = 10000
    cache_ttl: float = 300.0
    
    # 通信配置
    communication_port: int = 8080
    max_connections: int = 100
    buffer_size: int = 4096
    
    # 可视化配置
    gui_width: int = 1200
    gui_height: int = 800
    update_fps: int = 30
    show_3d: bool = True


@dataclass
class ConstellationConfig:
    """星座配置"""
    name: str = "Default"
    altitude: float = 550.0  # km
    inclination: float = 53.0  # degrees
    planes: int = 10
    satellites_per_plane: int = 20
    total_satellites: int = 200


@dataclass
class PhysicsConfig:
    """物理层配置"""
    frequency: float = 2.4e9  # Hz
    bandwidth: float = 10e6  # Hz
    tx_power: float = 30.0  # dBm
    noise_figure: float = 3.0  # dB
    antenna_gain: float = 15.0  # dBi


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.system_config = SystemConfig()
        self.constellation_configs = [ConstellationConfig()]
        self.physics_config = PhysicsConfig()
        
        # 尝试加载配置文件
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 加载系统配置
                if 'system' in config_data:
                    self.system_config = SystemConfig(**config_data['system'])
                
                # 加载星座配置
                if 'constellations' in config_data:
                    self.constellation_configs = [
                        ConstellationConfig(**cfg) for cfg in config_data['constellations']
                    ]
                
                # 加载物理层配置
                if 'physics' in config_data:
                    self.physics_config = PhysicsConfig(**config_data['physics'])
                
                return True
            else:
                # 创建默认配置文件
                self.save_config()
                return True
                
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            config_data = {
                'system': asdict(self.system_config),
                'constellations': [asdict(cfg) for cfg in self.constellation_configs],
                'physics': asdict(self.physics_config)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_system_config(self) -> SystemConfig:
        """获取系统配置"""
        return self.system_config
    
    def get_constellation_configs(self) -> list:
        """获取星座配置列表"""
        return self.constellation_configs
    
    def get_constellation_config(self, name: str) -> Optional[ConstellationConfig]:
        """获取指定名称的星座配置"""
        for cfg in self.constellation_configs:
            if cfg.name == name:
                return cfg
        return None
    
    def get_physics_config(self) -> PhysicsConfig:
        """获取物理层配置"""
        return self.physics_config
    
    def update_system_config(self, **kwargs):
        """更新系统配置"""
        for key, value in kwargs.items():
            if hasattr(self.system_config, key):
                setattr(self.system_config, key, value)
    
    def add_constellation_config(self, config: ConstellationConfig):
        """添加星座配置"""
        # 检查是否已存在同名配置
        for i, cfg in enumerate(self.constellation_configs):
            if cfg.name == config.name:
                self.constellation_configs[i] = config
                return
        
        self.constellation_configs.append(config)
    
    def remove_constellation_config(self, name: str) -> bool:
        """删除星座配置"""
        for i, cfg in enumerate(self.constellation_configs):
            if cfg.name == name:
                del self.constellation_configs[i]
                return True
        return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'system': {
                'name': self.system_config.system_name,
                'version': self.system_config.version,
                'max_satellites': self.system_config.max_satellites,
                'simulation_mode': self.system_config.simulation_mode
            },
            'constellations': len(self.constellation_configs),
            'constellation_names': [cfg.name for cfg in self.constellation_configs],
            'physics': {
                'frequency': self.physics_config.frequency,
                'bandwidth': self.physics_config.bandwidth
            }
        }
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.system_config = SystemConfig()
        self.constellation_configs = [ConstellationConfig()]
        self.physics_config = PhysicsConfig()
    
    def export_config(self, filename: str) -> bool:
        """导出配置到指定文件"""
        try:
            config_data = {
                'system': asdict(self.system_config),
                'constellations': [asdict(cfg) for cfg in self.constellation_configs],
                'physics': asdict(self.physics_config)
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, filename: str) -> bool:
        """从指定文件导入配置"""
        old_config_file = self.config_file
        self.config_file = filename
        success = self.load_config()
        self.config_file = old_config_file
        return success 