<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDT卫星数字孪生系统 V1.0</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 顶部工具栏 -->
    <div class="toolbar">
        <div class="toolbar-icons">
            <div class="icon">🔍</div>
            <div class="icon">📶</div>
            <div class="icon">🌐</div>
            <div class="icon">💬</div>
            <div class="icon">❓</div>
        </div>
    </div>

    <!-- 主标题 -->
    <div class="main-title">SDT卫星数字孪生系统 V1.0</div>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 左侧控制面板 -->
        <div class="left-panel">
            <!-- 星座选择 -->
            <div class="control-group">
                <h3>Select Constellation</h3>
                <select class="constellation-select">
                    <option value="kuiper_630">kuiper_630</option>
                    <option value="starlink">starlink</option>
                    <option value="oneweb">oneweb</option>
                </select>
                <div class="button-group">
                    <button class="btn btn-primary">Select</button>
                    <button class="btn btn-secondary">Reset</button>
                    <button class="btn btn-secondary">Add</button>
                </div>
            </div>

            <!-- 路径计算 -->
            <div class="control-group">
                <h3>Path Compute</h3>
                <div class="dropdown-row">
                    <select class="path-select">
                        <option value="">No Source</option>
                        <option value="terminal1">Terminal 1</option>
                        <option value="terminal2">Terminal 2</option>
                    </select>
                    <select class="path-select">
                        <option value="">No Destination</option>
                        <option value="terminal1">Terminal 1</option>
                        <option value="terminal2">Terminal 2</option>
                    </select>
                </div>
                <div class="button-group">
                    <button class="btn btn-primary">Compute</button>
                    <button class="btn btn-secondary">Clear</button>
                </div>
            </div>

            <!-- 卫星信息 -->
            <div class="control-group">
                <h3>Satellite Information</h3>
                <div class="info-area">
                    <div class="info-content">
                        <!-- 卫星信息显示区域 -->
                    </div>
                </div>
                <button class="btn btn-secondary">Clear</button>
            </div>
        </div>

        <!-- 中间地球视图 -->
        <div class="center-view">
            <canvas id="earthCanvas" width="800" height="600"></canvas>
        </div>

        <!-- 右侧控制面板 -->
        <div class="right-panel">
            <!-- 顶部按钮组 -->
            <div class="top-buttons">
                <button class="tab-btn active">Simulation</button>
                <button class="tab-btn">Configuration</button>
                <button class="tab-btn">Query</button>
            </div>

            <div class="tab-content">
                <div class="button-row">
                    <button class="btn btn-small">Simulation</button>
                    <button class="btn btn-small">Config</button>
                    <button class="btn btn-small">Export</button>
                </div>
            </div>

            <!-- 离散事件引擎 -->
            <div class="control-group">
                <h4>离散事件引擎</h4>
                <button class="btn btn-run">Run</button>
                <div class="status-row">
                    <span>Status</span>
                    <span class="status-value">Stopped</span>
                </div>
            </div>

            <!-- Terminal 1 -->
            <div class="control-group">
                <h4>Terminal 1</h4>
                <div class="terminal-row">
                    <select class="terminal-select">
                        <option value="">No Terminal</option>
                        <option value="terminal1">Terminal 1</option>
                        <option value="terminal2">Terminal 2</option>
                    </select>
                    <button class="btn btn-small">Connect</button>
                </div>
                <div class="terminal-output">
                    <div class="output-text">Enter Linux command here</div>
                </div>
            </div>

            <!-- Terminal 2 -->
            <div class="control-group">
                <h4>Terminal 2</h4>
                <div class="terminal-row">
                    <select class="terminal-select">
                        <option value="">No Terminal</option>
                        <option value="terminal1">Terminal 1</option>
                        <option value="terminal2">Terminal 2</option>
                    </select>
                    <button class="btn btn-small">Connect</button>
                </div>
                <div class="terminal-output">
                    <div class="output-text">Enter Linux command here</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部时间轴 -->
    <div class="timeline">
        <div class="time-markers">
            <span>Mar 19 2025 14:56:00 UTC</span>
            <span>Mar 19 2025 14:58:00 UTC</span>
            <span>Mar 19 2025 15:00:00 UTC</span>
            <span>Mar 19 2025 15:02:00 UTC</span>
            <span>Mar 19 2025 15:04:00 UTC</span>
            <span>Mar 19 2025 15:06:00 UTC</span>
            <span>Mar 19 2025 15:08:00 UTC</span>
            <span>Mar 19 2025 15:10:00 UTC</span>
            <span>Mar 19 2025 15:12:00 UTC</span>
            <span>Mar 19 2025 15:14:00 UTC</span>
            <span>Mar 19 2025 15:16:00 UTC</span>
            <span>Mar 19 2025 15:18:00 UTC</span>
            <span>Mar 19 2025 15:20:00 UTC</span>
        </div>
        <div class="time-progress">
            <div class="progress-bar"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html> 