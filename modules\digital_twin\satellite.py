#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卫星物理属性类
封装卫星的物理属性，用于高保真物理仿真
"""

import numpy as np
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class SatelliteProperties:
    """卫星基本属性数据类"""
    mass: float                    # 质量 (kg)
    drag_area: float              # 阻力面积 (m²)
    drag_coeff: float             # 阻力系数 Cd (无量纲)
    srp_area: float               # 太阳辐射压面积 (m²)
    srp_coeff: float              # 反射系数 Cr (无量纲)
    
    def __post_init__(self):
        """数据验证"""
        if self.mass <= 0:
            raise ValueError(f"卫星质量必须大于0: {self.mass}")
        if self.drag_area < 0:
            raise ValueError(f"阻力面积不能为负: {self.drag_area}")
        if self.drag_coeff < 0:
            raise ValueError(f"阻力系数不能为负: {self.drag_coeff}")
        if self.srp_area < 0:
            raise ValueError(f"太阳辐射压面积不能为负: {self.srp_area}")
        if self.srp_coeff < 0:
            raise ValueError(f"反射系数不能为负: {self.srp_coeff}")


class Satellite:
    """
    卫星类
    封装卫星的物理属性，按照设计文档要求实现
    """
    
    def __init__(self, mass: float, drag_area: float, drag_coeff: float = 2.2,
                 srp_area: float = None, srp_coeff: float = 1.3,
                 inertia_tensor: np.ndarray = None, name: str = "Unknown"):
        """
        初始化卫星对象
        
        Args:
            mass: 卫星质量 (kg)
            drag_area: 阻力面积 (m²)
            drag_coeff: 阻力系数 Cd (无量纲，默认2.2)
            srp_area: 太阳辐射压面积 (m²，默认等于阻力面积)
            srp_coeff: 反射系数 Cr (无量纲，默认1.3)
            inertia_tensor: 3x3惯量张量 (kg*m²，默认为球体)
            name: 卫星名称
        """
        self.name = name
        self.mass = mass
        self.drag_area = drag_area
        self.drag_coeff = drag_coeff
        self.srp_area = srp_area if srp_area is not None else drag_area
        self.srp_coeff = srp_coeff
        
        # 设置惯量张量
        if inertia_tensor is not None:
            if inertia_tensor.shape != (3, 3):
                raise ValueError(f"惯量张量必须是3x3矩阵: {inertia_tensor.shape}")
            self.inertia_tensor = inertia_tensor.copy()
        else:
            # 默认为球体惯量张量（假设半径1m的均匀球体）
            I_sphere = 0.4 * mass * 1.0**2  # I = (2/5) * m * r²
            self.inertia_tensor = np.diag([I_sphere, I_sphere, I_sphere])
        
        # 验证属性
        self._validate_properties()
        
        # 额外属性
        self.properties = SatelliteProperties(
            mass=self.mass,
            drag_area=self.drag_area,
            drag_coeff=self.drag_coeff,
            srp_area=self.srp_area,
            srp_coeff=self.srp_coeff
        )
    
    def _validate_properties(self):
        """验证卫星属性"""
        if self.mass <= 0:
            raise ValueError(f"卫星质量必须大于0: {self.mass}")
        if self.drag_area < 0:
            raise ValueError(f"阻力面积不能为负: {self.drag_area}")
        if self.drag_coeff < 0:
            raise ValueError(f"阻力系数不能为负: {self.drag_coeff}")
        if self.srp_area < 0:
            raise ValueError(f"太阳辐射压面积不能为负: {self.srp_area}")
        if self.srp_coeff < 0:
            raise ValueError(f"反射系数不能为负: {self.srp_coeff}")
        
        # 检查惯量张量是否对称正定
        if not np.allclose(self.inertia_tensor, self.inertia_tensor.T):
            raise ValueError("惯量张量必须是对称矩阵")
        
        eigenvals = np.linalg.eigvals(self.inertia_tensor)
        if np.any(eigenvals <= 0):
            raise ValueError("惯量张量必须是正定矩阵")
    
    def get_inertia_inverse(self) -> np.ndarray:
        """
        获取惯量张量的逆矩阵
        
        Returns:
            惯量张量逆矩阵
        """
        return np.linalg.inv(self.inertia_tensor)
    
    def get_ballistic_coefficient(self) -> float:
        """
        计算弹道系数 (kg/m²)
        
        Returns:
            弹道系数
        """
        return self.mass / (self.drag_coeff * self.drag_area)
    
    def get_area_to_mass_ratio(self) -> float:
        """
        计算面积质量比 (m²/kg)
        
        Returns:
            面积质量比
        """
        return self.srp_area / self.mass
    
    def update_mass(self, new_mass: float):
        """
        更新卫星质量（例如燃料消耗）
        
        Args:
            new_mass: 新质量 (kg)
        """
        if new_mass <= 0:
            raise ValueError(f"新质量必须大于0: {new_mass}")
        
        # 按比例缩放惯量张量
        mass_ratio = new_mass / self.mass
        self.inertia_tensor *= mass_ratio
        
        # 更新质量
        self.mass = new_mass
        self.properties.mass = new_mass
    
    def set_inertia_tensor(self, inertia_tensor: np.ndarray):
        """
        设置惯量张量
        
        Args:
            inertia_tensor: 3x3惯量张量 (kg*m²)
        """
        if inertia_tensor.shape != (3, 3):
            raise ValueError(f"惯量张量必须是3x3矩阵: {inertia_tensor.shape}")
        
        self.inertia_tensor = inertia_tensor.copy()
        self._validate_properties()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            卫星属性字典
        """
        return {
            'name': self.name,
            'mass': self.mass,
            'drag_area': self.drag_area,
            'drag_coeff': self.drag_coeff,
            'srp_area': self.srp_area,
            'srp_coeff': self.srp_coeff,
            'inertia_tensor': self.inertia_tensor.tolist(),
            'ballistic_coefficient': self.get_ballistic_coefficient(),
            'area_to_mass_ratio': self.get_area_to_mass_ratio()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Satellite':
        """
        从字典创建卫星对象
        
        Args:
            data: 卫星属性字典
            
        Returns:
            Satellite对象
        """
        inertia_tensor = None
        if 'inertia_tensor' in data:
            inertia_tensor = np.array(data['inertia_tensor'])
        
        return cls(
            mass=data['mass'],
            drag_area=data['drag_area'],
            drag_coeff=data.get('drag_coeff', 2.2),
            srp_area=data.get('srp_area'),
            srp_coeff=data.get('srp_coeff', 1.3),
            inertia_tensor=inertia_tensor,
            name=data.get('name', 'Unknown')
        )
    
    @classmethod
    def create_cubesat(cls, mass: float, side_length: float, name: str = "CubeSat") -> 'Satellite':
        """
        创建立方体卫星
        
        Args:
            mass: 质量 (kg)
            side_length: 边长 (m)
            name: 名称
            
        Returns:
            CubeSat对象
        """
        area = side_length * side_length
        
        # 立方体惯量张量
        I = mass * side_length**2 / 6.0
        inertia_tensor = np.diag([I, I, I])
        
        return cls(
            mass=mass,
            drag_area=area,
            drag_coeff=2.2,
            srp_area=area,
            srp_coeff=1.3,
            inertia_tensor=inertia_tensor,
            name=name
        )
    
    @classmethod
    def create_spherical_satellite(cls, mass: float, radius: float, name: str = "SphericalSat") -> 'Satellite':
        """
        创建球形卫星
        
        Args:
            mass: 质量 (kg)
            radius: 半径 (m)
            name: 名称
            
        Returns:
            球形卫星对象
        """
        area = np.pi * radius**2
        
        # 球体惯量张量
        I = 0.4 * mass * radius**2  # I = (2/5) * m * r²
        inertia_tensor = np.diag([I, I, I])
        
        return cls(
            mass=mass,
            drag_area=area,
            drag_coeff=2.2,
            srp_area=area,
            srp_coeff=1.3,
            inertia_tensor=inertia_tensor,
            name=name
        )
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"Satellite({self.name}, mass={self.mass:.1f}kg, "
                f"drag_area={self.drag_area:.2f}m²)")
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"Satellite(name='{self.name}', mass={self.mass}, "
                f"drag_area={self.drag_area}, drag_coeff={self.drag_coeff}, "
                f"srp_area={self.srp_area}, srp_coeff={self.srp_coeff})")
