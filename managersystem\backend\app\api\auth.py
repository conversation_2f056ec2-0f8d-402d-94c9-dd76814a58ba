"""
认证API路由
处理用户登录、登出、注册等认证相关操作
"""

from flask import Blueprint, request, jsonify
from marshmallow import Schema, fields, validate, ValidationError

from app.models.user import User
from app.auth.jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, token_required, get_current_user, get_current_token


# 创建蓝图
auth_bp = Blueprint('auth', __name__)


# 请求数据验证Schema
class LoginSchema(Schema):
    """登录请求验证"""
    username = fields.Str(required=True, validate=validate.Length(min=1, max=50))
    password = fields.Str(required=True, validate=validate.Length(min=1))


class RegisterSchema(Schema):
    """注册请求验证"""
    username = fields.Str(required=True, validate=validate.Length(min=3, max=50))
    email = fields.Email(required=True)
    password = fields.Str(required=True, validate=validate.Length(min=6, max=128))
    role = fields.Str(required=False, validate=validate.OneOf(list(User.ROLES.keys())), missing='guest')
    profile = fields.Dict(required=False, missing={})


class ChangePasswordSchema(Schema):
    """修改密码请求验证"""
    old_password = fields.Str(required=True)
    new_password = fields.Str(required=True, validate=validate.Length(min=6, max=128))


class UpdateProfileSchema(Schema):
    """更新个人信息请求验证"""
    email = fields.Email(required=False)
    profile = fields.Dict(required=False)


@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        # 验证请求数据
        schema = LoginSchema()
        data = schema.load(request.json or {})
        
        # 用户认证
        user = User.authenticate(data['username'], data['password'])
        if not user:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_CREDENTIALS',
                    'message': '用户名或密码错误'
                }
            }), 401
        
        # 生成token
        token_data = JWTHandler.generate_token(user)
        if not token_data:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'TOKEN_GENERATION_FAILED',
                    'message': 'Token生成失败'
                }
            }), 500
        
        return jsonify({
            'success': True,
            'data': token_data,
            'message': '登录成功'
        })
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': '请求数据验证失败',
                'details': e.messages
            }
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@auth_bp.route('/logout', methods=['POST'])
@token_required
def logout():
    """用户登出"""
    try:
        token = get_current_token()
        if token:
            JWTHandler.revoke_token(token)
        
        return jsonify({
            'success': True,
            'message': '登出成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册（仅开发环境或管理员可用）"""
    try:
        # 验证请求数据
        schema = RegisterSchema()
        data = schema.load(request.json or {})
        
        # 检查用户名是否已存在
        existing_user = User.get_by_username(data['username'])
        if existing_user:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'USERNAME_EXISTS',
                    'message': '用户名已存在'
                }
            }), 400
        
        # 创建新用户
        user = User()
        user.username = data['username']
        user.email = data['email']
        user.set_password(data['password'])
        user.role = data['role']
        user.profile = data['profile']
        
        if not user.save():
            return jsonify({
                'success': False,
                'error': {
                    'code': 'USER_CREATION_FAILED',
                    'message': '用户创建失败'
                }
            }), 500
        
        return jsonify({
            'success': True,
            'data': user.to_dict(),
            'message': '注册成功'
        }), 201
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': '请求数据验证失败',
                'details': e.messages
            }
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@auth_bp.route('/profile', methods=['GET'])
@token_required
def get_profile():
    """获取当前用户信息"""
    try:
        user = get_current_user()
        return jsonify({
            'success': True,
            'data': user.to_dict(),
            'message': '获取用户信息成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@auth_bp.route('/profile', methods=['PUT'])
@token_required
def update_profile():
    """更新当前用户信息"""
    try:
        # 验证请求数据
        schema = UpdateProfileSchema()
        data = schema.load(request.json or {})
        
        user = get_current_user()
        
        # 更新用户信息
        if 'email' in data:
            user.email = data['email']
        
        if 'profile' in data:
            user.profile.update(data['profile'])
        
        if not user.save():
            return jsonify({
                'success': False,
                'error': {
                    'code': 'UPDATE_FAILED',
                    'message': '更新用户信息失败'
                }
            }), 500
        
        return jsonify({
            'success': True,
            'data': user.to_dict(),
            'message': '更新用户信息成功'
        })
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': '请求数据验证失败',
                'details': e.messages
            }
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@auth_bp.route('/change-password', methods=['POST'])
@token_required
def change_password():
    """修改密码"""
    try:
        # 验证请求数据
        schema = ChangePasswordSchema()
        data = schema.load(request.json or {})
        
        user = get_current_user()
        
        # 验证旧密码
        if not user.check_password(data['old_password']):
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_OLD_PASSWORD',
                    'message': '原密码错误'
                }
            }), 400
        
        # 设置新密码
        user.set_password(data['new_password'])
        
        if not user.save():
            return jsonify({
                'success': False,
                'error': {
                    'code': 'PASSWORD_UPDATE_FAILED',
                    'message': '密码更新失败'
                }
            }), 500
        
        # 撤销用户的所有会话，强制重新登录
        JWTHandler.revoke_user_sessions(user.id)
        
        return jsonify({
            'success': True,
            'message': '密码修改成功，请重新登录'
        })
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': '请求数据验证失败',
                'details': e.messages
            }
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@auth_bp.route('/sessions', methods=['GET'])
@token_required
def get_sessions():
    """获取当前用户的所有会话"""
    try:
        user = get_current_user()
        sessions = JWTHandler.get_user_sessions(user.id)
        
        return jsonify({
            'success': True,
            'data': sessions,
            'message': '获取会话列表成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@auth_bp.route('/sessions', methods=['DELETE'])
@token_required
def revoke_all_sessions():
    """撤销当前用户的所有会话"""
    try:
        user = get_current_user()
        revoked_count = JWTHandler.revoke_user_sessions(user.id)
        
        return jsonify({
            'success': True,
            'data': {'revoked_count': revoked_count},
            'message': f'已撤销 {revoked_count} 个会话'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@auth_bp.route('/verify', methods=['GET'])
@token_required
def verify_token():
    """验证token有效性"""
    try:
        user = get_current_user()
        return jsonify({
            'success': True,
            'data': {
                'valid': True,
                'user': user.to_dict()
            },
            'message': 'Token有效'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500
