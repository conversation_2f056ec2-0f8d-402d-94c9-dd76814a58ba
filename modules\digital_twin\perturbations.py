#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄动力模型
实现各种摄动力的计算，包括J2、大气阻力、太阳辐射压等
"""

import math
import numpy as np
from typing import Optional
from .satellite import Satellite


class J2Perturbation:
    """
    J2摄动力计算
    地球扁率引起的摄动加速度
    """
    
    # 地球物理常数
    EARTH_MU = 3.986004418e14      # m³/s² 地球引力参数
    EARTH_RADIUS = 6378137.0       # m 地球赤道半径
    J2 = 1.08262998905e-3          # J2项系数
    
    @classmethod
    def calculate(cls, r: np.ndarray) -> np.ndarray:
        """
        计算J2摄动加速度
        
        Args:
            r: 位置向量 [x, y, z] (km)
            
        Returns:
            J2摄动加速度向量 (km/s²)
        """
        # 转换为米
        r_m = r * 1000.0
        
        x, y, z = r_m[0], r_m[1], r_m[2]
        r_norm = np.linalg.norm(r_m)
        
        if r_norm < cls.EARTH_RADIUS:
            # 卫星在地表以下，返回零加速度
            return np.zeros(3)
        
        # 计算常用项
        r2 = r_norm * r_norm
        r3 = r2 * r_norm
        r5 = r3 * r2
        z2_over_r2 = (z * z) / r2
        
        # J2摄动加速度公式
        factor = -1.5 * cls.J2 * cls.EARTH_MU * (cls.EARTH_RADIUS ** 2) / r5
        
        ax = factor * x * (1.0 - 5.0 * z2_over_r2)
        ay = factor * y * (1.0 - 5.0 * z2_over_r2)
        az = factor * z * (3.0 - 5.0 * z2_over_r2)
        
        # 转换回km/s²
        a_j2 = np.array([ax, ay, az]) / 1000.0
        
        return a_j2


class AtmosphericDrag:
    """
    大气阻力计算
    """
    
    # 地球物理常数
    EARTH_OMEGA = 7.2921159e-5     # rad/s 地球自转角速度
    EARTH_RADIUS = 6378.137        # km 地球半径
    
    @classmethod
    def calculate(cls, r: np.ndarray, v: np.ndarray, satellite: Satellite, 
                 jd_utc: float, f107: float = 150.0, ap: float = 15.0) -> np.ndarray:
        """
        计算大气阻力加速度
        
        Args:
            r: 位置向量 [x, y, z] (km)
            v: 速度向量 [vx, vy, vz] (km/s)
            satellite: 卫星对象
            jd_utc: 儒略日UTC时间
            f107: 太阳F10.7通量 (默认150)
            ap: 地磁Ap指数 (默认15)
            
        Returns:
            大气阻力加速度向量 (km/s²)
        """
        # 计算高度
        r_norm = np.linalg.norm(r)
        altitude_km = r_norm - cls.EARTH_RADIUS
        
        # 高度范围检查
        if altitude_km < 80.0 or altitude_km > 1000.0:
            # 超出大气模型范围，返回零阻力
            return np.zeros(3)
        
        # 计算大气层旋转速度
        v_atm = cls._get_atmospheric_velocity(r)
        
        # 计算相对速度
        v_rel = v - v_atm
        v_rel_norm = np.linalg.norm(v_rel)
        
        if v_rel_norm < 1e-10:
            return np.zeros(3)
        
        # 计算大气密度
        rho = cls._get_density(altitude_km, f107, ap)
        
        # 大气阻力公式: F_drag = -0.5 * Cd * A * rho * |v_rel| * v_rel
        drag_factor = -0.5 * satellite.drag_coeff * satellite.drag_area * rho * v_rel_norm / satellite.mass
        a_drag = drag_factor * v_rel
        
        return a_drag
    
    @classmethod
    def _get_atmospheric_velocity(cls, r: np.ndarray) -> np.ndarray:
        """
        计算大气层旋转速度
        
        Args:
            r: 位置向量 [x, y, z] (km)
            
        Returns:
            大气层速度向量 (km/s)
        """
        # 大气层随地球旋转: v_atm = ω × r
        omega_vec = np.array([0.0, 0.0, cls.EARTH_OMEGA])  # rad/s
        v_atm = np.cross(omega_vec, r)  # km/s
        
        return v_atm
    
    @classmethod
    def _get_density(cls, h_km: float, f107: float = 150.0, ap: float = 15.0) -> float:
        """
        计算大气密度（简化模型）
        
        Args:
            h_km: 高度 (km)
            f107: 太阳F10.7通量
            ap: 地磁Ap指数
            
        Returns:
            大气密度 (kg/m³)
        """
        # 简化的大气密度模型（基于设计文档）
        if h_km < 175.0:
            h_km = 175.0  # 模型下限
        
        # 温度计算
        T = 900.0 + 2.5 * (f107 - 70.0) + 1.5 * ap
        
        # 平均分子质量
        mu_mass = 27.0 - 0.012 * (h_km - 200.0)
        if mu_mass < 16.0:
            mu_mass = 16.0  # 下限
        
        # 标高
        H = T / mu_mass
        
        # 密度计算
        rho = 6e-10 * math.exp(-(h_km - 175.0) / H)  # kg/m³
        
        return max(rho, 1e-15)  # 设置最小密度


class SolarRadiationPressure:
    """
    太阳辐射压计算
    """
    
    # 太阳辐射压常数
    SOLAR_FLUX = 1367.0  # W/m² 太阳常数
    LIGHT_SPEED = 299792458.0  # m/s 光速
    P_SRP = SOLAR_FLUX / LIGHT_SPEED  # N/m² 太阳辐射压
    AU = 149597870.7  # km 天文单位
    
    @classmethod
    def calculate(cls, r: np.ndarray, satellite: Satellite, jd_utc: float) -> np.ndarray:
        """
        计算太阳辐射压加速度
        
        Args:
            r: 位置向量 [x, y, z] (km)
            satellite: 卫星对象
            jd_utc: 儒略日UTC时间
            
        Returns:
            太阳辐射压加速度向量 (km/s²)
        """
        # 计算太阳位置（简化模型）
        r_sun = cls._get_sun_position(jd_utc)
        
        # 计算卫星到太阳的向量
        r_sat_to_sun = r_sun - r
        r_sat_to_sun_norm = np.linalg.norm(r_sat_to_sun)
        
        if r_sat_to_sun_norm < 1e-10:
            return np.zeros(3)
        
        # 太阳方向单位向量
        u_sun = r_sat_to_sun / r_sat_to_sun_norm
        
        # 检查地影（简化模型）
        if cls._is_in_earth_shadow(r, r_sun):
            return np.zeros(3)
        
        # 计算太阳距离修正因子
        sun_distance_au = r_sat_to_sun_norm / cls.AU
        distance_factor = 1.0 / (sun_distance_au * sun_distance_au)
        
        # 太阳辐射压加速度
        srp_acceleration_magnitude = (cls.P_SRP * satellite.srp_coeff * satellite.srp_area * 
                                    distance_factor / satellite.mass)
        
        # 转换为km/s²
        a_srp = -srp_acceleration_magnitude * u_sun / 1000.0
        
        return a_srp
    
    @classmethod
    def _get_sun_position(cls, jd_utc: float) -> np.ndarray:
        """
        计算太阳位置（简化模型）
        
        Args:
            jd_utc: 儒略日UTC时间
            
        Returns:
            太阳位置向量 (km)
        """
        # 简化的太阳位置计算
        # 基于J2000.0历元的简化轨道
        
        # 从J2000.0开始的天数
        T = (jd_utc - 2451545.0) / 36525.0
        
        # 平黄经（度）
        L = 280.460 + 36000.771 * T
        L = L % 360.0
        
        # 平近点角（度）
        M = 357.5291092 + 35999.05034 * T
        M = math.radians(M % 360.0)
        
        # 黄经修正
        C = 1.9148 * math.sin(M) + 0.02 * math.sin(2 * M)
        true_longitude = math.radians(L + C)
        
        # 地心距离（AU）
        R = 1.000001018 * (1 - 0.01671123 * math.cos(M))
        
        # 转换为直角坐标（km）
        x_sun = R * cls.AU * math.cos(true_longitude)
        y_sun = R * cls.AU * math.sin(true_longitude)
        z_sun = 0.0  # 简化：忽略黄道倾角
        
        return np.array([x_sun, y_sun, z_sun])
    
    @classmethod
    def _is_in_earth_shadow(cls, r_sat: np.ndarray, r_sun: np.ndarray) -> bool:
        """
        检查卫星是否在地影中（简化模型）
        
        Args:
            r_sat: 卫星位置向量 (km)
            r_sun: 太阳位置向量 (km)
            
        Returns:
            是否在地影中
        """
        # 简化的地影模型：只考虑本影
        
        # 太阳到卫星的向量
        sun_to_sat = r_sat - r_sun
        sun_to_sat_norm = np.linalg.norm(sun_to_sat)
        
        if sun_to_sat_norm < 1e-10:
            return False
        
        # 太阳方向单位向量
        u_sun = sun_to_sat / sun_to_sat_norm
        
        # 卫星在太阳方向上的投影
        sat_projection = np.dot(r_sat, u_sun)
        
        # 如果卫星在太阳的反方向且距离地心足够近，则在地影中
        if sat_projection < 0:
            # 计算卫星到太阳-地心连线的距离
            distance_to_line = np.linalg.norm(r_sat - sat_projection * u_sun)
            
            # 地球半径
            earth_radius = 6378.137  # km
            
            # 如果距离小于地球半径，则在地影中
            return distance_to_line < earth_radius
        
        return False
