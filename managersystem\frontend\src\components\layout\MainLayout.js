import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Typography,
  Space,
  Button,
  Badge,
  Tooltip,
} from 'antd';
import {
  DashboardOutlined,
  BugOutlined,
  FileTextOutlined,
  AppstoreOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';

import { logout } from '../../store/authSlice';
import { toggleSidebar } from '../../store/appSlice';
import './MainLayout.css';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

const MainLayout = ({ children }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  const { user } = useSelector(state => state.auth);
  const { sidebarCollapsed } = useSelector(state => state.app);

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
      permission: null,
    },
    {
      key: '/issues',
      icon: <BugOutlined />,
      label: '问题管理',
      permission: 'issue:read',
    },
    {
      key: '/documents',
      icon: <FileTextOutlined />,
      label: '文档库',
      permission: 'document:read',
    },
    {
      key: '/products',
      icon: <AppstoreOutlined />,
      label: '产品库',
      permission: 'product:read',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
      permission: 'user:read',
      roles: ['super_admin', 'admin'],
    },
  ];

  // 过滤菜单项（根据权限和角色）
  const filteredMenuItems = menuItems.filter(item => {
    // 检查角色限制
    if (item.roles && !item.roles.includes(user?.role)) {
      return false;
    }
    
    // 检查权限
    if (item.permission && !user?.permissions?.includes(item.permission)) {
      return false;
    }
    
    return true;
  });

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => {
        // TODO: 打开个人信息模态框
      },
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      onClick: () => {
        // TODO: 打开设置页面
      },
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        dispatch(logout());
      },
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar());
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const pathname = location.pathname;
    for (const item of filteredMenuItems) {
      if (pathname.startsWith(item.key)) {
        return [item.key];
      }
    }
    return ['/dashboard'];
  };

  return (
    <Layout className="main-layout">
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={sidebarCollapsed}
        className="main-sider"
        width={240}
        collapsedWidth={64}
      >
        <div className="sider-header">
          <div className="logo">
            <AppstoreOutlined className="logo-icon" />
            {!sidebarCollapsed && (
              <Title level={4} className="logo-text">
                SDT管理
              </Title>
            )}
          </div>
        </div>

        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          onClick={handleMenuClick}
          className="main-menu"
        >
          {filteredMenuItems.map(item => (
            <Menu.Item key={item.key} icon={item.icon}>
              {item.label}
            </Menu.Item>
          ))}
        </Menu>
      </Sider>

      {/* 主内容区 */}
      <Layout className="main-content-layout">
        {/* 顶部导航栏 */}
        <Header className="main-header">
          <div className="header-left">
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={handleToggleSidebar}
              className="sidebar-trigger"
            />
          </div>

          <div className="header-right">
            <Space size="middle">
              {/* 通知 */}
              <Tooltip title="通知">
                <Badge count={0} size="small">
                  <Button
                    type="text"
                    icon={<BellOutlined />}
                    className="header-action"
                  />
                </Badge>
              </Tooltip>

              {/* 用户信息 */}
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                trigger={['click']}
              >
                <div className="user-info">
                  <Avatar
                    size="small"
                    icon={<UserOutlined />}
                    className="user-avatar"
                  />
                  <Space direction="vertical" size={0} className="user-details">
                    <Text className="user-name">
                      {user?.profile?.name || user?.username}
                    </Text>
                    <Text type="secondary" className="user-role">
                      {user?.role === 'super_admin' && '超级管理员'}
                      {user?.role === 'admin' && '管理员'}
                      {user?.role === 'developer' && '开发者'}
                      {user?.role === 'tester' && '测试员'}
                      {user?.role === 'guest' && '访客'}
                    </Text>
                  </Space>
                </div>
              </Dropdown>
            </Space>
          </div>
        </Header>

        {/* 内容区域 */}
        <Content className="main-content">
          <div className="content-wrapper">
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
