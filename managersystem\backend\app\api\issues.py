"""
问题管理API路由
处理缺陷问题的CRUD操作和相关功能
"""

from flask import Blueprint, request, jsonify
from marshmallow import Schema, fields, validate, ValidationError

from app.models.issue import Issue
from app.models.user import User
from app.auth.jwt_handler import token_required, permission_required, get_current_user


# 创建蓝图
issues_bp = Blueprint('issues', __name__)


# 请求数据验证Schema
class IssueCreateSchema(Schema):
    """创建问题请求验证"""
    title = fields.Str(required=True, validate=validate.Length(min=1, max=200))
    description = fields.Str(required=True)
    type = fields.Str(required=True, validate=validate.OneOf(list(Issue.TYPES.keys())))
    priority = fields.Str(required=True, validate=validate.OneOf(list(Issue.PRIORITIES.keys())))
    assignee = fields.Str(required=False)
    tags = fields.List(fields.Str(), required=False, missing=[])
    estimated_hours = fields.Float(required=False, missing=0)


class IssueUpdateSchema(Schema):
    """更新问题请求验证"""
    title = fields.Str(required=False, validate=validate.Length(min=1, max=200))
    description = fields.Str(required=False)
    type = fields.Str(required=False, validate=validate.OneOf(list(Issue.TYPES.keys())))
    priority = fields.Str(required=False, validate=validate.OneOf(list(Issue.PRIORITIES.keys())))
    status = fields.Str(required=False, validate=validate.OneOf(list(Issue.STATUSES.keys())))
    assignee = fields.Str(required=False, allow_none=True)
    tags = fields.List(fields.Str(), required=False)
    estimated_hours = fields.Float(required=False)
    actual_hours = fields.Float(required=False)


class CommentCreateSchema(Schema):
    """创建评论请求验证"""
    content = fields.Str(required=True, validate=validate.Length(min=1))


@issues_bp.route('', methods=['GET'])
@permission_required('issue:read')
def get_issues():
    """获取问题列表"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        
        # 过滤参数
        filters = {}
        if request.args.get('type'):
            filters['type'] = request.args.get('type')
        if request.args.get('priority'):
            filters['priority'] = request.args.get('priority')
        if request.args.get('status'):
            filters['status'] = request.args.get('status')
        if request.args.get('assignee'):
            filters['assignee'] = request.args.get('assignee')
        if request.args.get('reporter'):
            filters['reporter'] = request.args.get('reporter')
        if request.args.get('tags'):
            filters['tags'] = request.args.get('tags').split(',')
        if request.args.get('keyword'):
            filters['keyword'] = request.args.get('keyword')
        
        # 获取问题列表
        result = Issue.get_list(page=page, size=size, **filters)
        
        return jsonify({
            'success': True,
            'data': result,
            'message': '获取问题列表成功'
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INVALID_PARAMETER',
                'message': '参数格式错误'
            }
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@issues_bp.route('', methods=['POST'])
@permission_required('issue:create')
def create_issue():
    """创建问题"""
    try:
        # 验证请求数据
        schema = IssueCreateSchema()
        data = schema.load(request.json or {})
        
        # 验证分配人是否存在
        if data.get('assignee'):
            assignee = User.get_by_id(data['assignee'])
            if not assignee:
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'ASSIGNEE_NOT_FOUND',
                        'message': '指定的分配人不存在'
                    }
                }), 400
        
        # 创建问题
        current_user = get_current_user()
        issue = Issue()
        issue.title = data['title']
        issue.description = data['description']
        issue.type = data['type']
        issue.priority = data['priority']
        issue.assignee = data.get('assignee')
        issue.reporter = current_user.id
        issue.tags = data['tags']
        issue.estimated_hours = data['estimated_hours']
        
        if not issue.save():
            return jsonify({
                'success': False,
                'error': {
                    'code': 'ISSUE_CREATION_FAILED',
                    'message': '问题创建失败'
                }
            }), 500
        
        return jsonify({
            'success': True,
            'data': issue.to_dict(),
            'message': '问题创建成功'
        }), 201
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': '请求数据验证失败',
                'details': e.messages
            }
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@issues_bp.route('/<issue_id>', methods=['GET'])
@permission_required('issue:read')
def get_issue(issue_id):
    """获取问题详情"""
    try:
        issue = Issue.get_by_id(issue_id)
        if not issue:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'ISSUE_NOT_FOUND',
                    'message': '问题不存在'
                }
            }), 404
        
        # 获取问题详情和评论
        issue_data = issue.to_dict()
        issue_data['comments'] = issue.get_comments()
        
        return jsonify({
            'success': True,
            'data': issue_data,
            'message': '获取问题详情成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@issues_bp.route('/<issue_id>', methods=['PUT'])
@permission_required('issue:update')
def update_issue(issue_id):
    """更新问题"""
    try:
        # 验证请求数据
        schema = IssueUpdateSchema()
        data = schema.load(request.json or {})
        
        # 获取问题
        issue = Issue.get_by_id(issue_id)
        if not issue:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'ISSUE_NOT_FOUND',
                    'message': '问题不存在'
                }
            }), 404
        
        # 验证分配人是否存在
        if 'assignee' in data and data['assignee']:
            assignee = User.get_by_id(data['assignee'])
            if not assignee:
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'ASSIGNEE_NOT_FOUND',
                        'message': '指定的分配人不存在'
                    }
                }), 400
        
        # 更新问题字段
        for field, value in data.items():
            if hasattr(issue, field):
                setattr(issue, field, value)
        
        if not issue.save():
            return jsonify({
                'success': False,
                'error': {
                    'code': 'ISSUE_UPDATE_FAILED',
                    'message': '问题更新失败'
                }
            }), 500
        
        return jsonify({
            'success': True,
            'data': issue.to_dict(),
            'message': '问题更新成功'
        })
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': '请求数据验证失败',
                'details': e.messages
            }
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@issues_bp.route('/<issue_id>', methods=['DELETE'])
@permission_required('issue:delete')
def delete_issue(issue_id):
    """删除问题"""
    try:
        issue = Issue.get_by_id(issue_id)
        if not issue:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'ISSUE_NOT_FOUND',
                    'message': '问题不存在'
                }
            }), 404
        
        if not issue.delete():
            return jsonify({
                'success': False,
                'error': {
                    'code': 'ISSUE_DELETE_FAILED',
                    'message': '问题删除失败'
                }
            }), 500
        
        return jsonify({
            'success': True,
            'message': '问题删除成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@issues_bp.route('/<issue_id>/comments', methods=['POST'])
@permission_required('issue:read')
def add_comment(issue_id):
    """添加评论"""
    try:
        # 验证请求数据
        schema = CommentCreateSchema()
        data = schema.load(request.json or {})
        
        # 获取问题
        issue = Issue.get_by_id(issue_id)
        if not issue:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'ISSUE_NOT_FOUND',
                    'message': '问题不存在'
                }
            }), 404
        
        # 添加评论
        current_user = get_current_user()
        if not issue.add_comment(data['content'], current_user.id):
            return jsonify({
                'success': False,
                'error': {
                    'code': 'COMMENT_ADD_FAILED',
                    'message': '评论添加失败'
                }
            }), 500
        
        return jsonify({
            'success': True,
            'message': '评论添加成功'
        }), 201
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': '请求数据验证失败',
                'details': e.messages
            }
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@issues_bp.route('/<issue_id>/comments', methods=['GET'])
@permission_required('issue:read')
def get_comments(issue_id):
    """获取评论列表"""
    try:
        issue = Issue.get_by_id(issue_id)
        if not issue:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'ISSUE_NOT_FOUND',
                    'message': '问题不存在'
                }
            }), 404
        
        comments = issue.get_comments()
        
        return jsonify({
            'success': True,
            'data': comments,
            'message': '获取评论列表成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500


@issues_bp.route('/stats', methods=['GET'])
@permission_required('issue:read')
def get_stats():
    """获取问题统计信息"""
    try:
        stats = Issue.get_stats()
        
        return jsonify({
            'success': True,
            'data': stats,
            'message': '获取统计信息成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }
        }), 500
