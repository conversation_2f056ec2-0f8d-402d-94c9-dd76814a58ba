#!/usr/bin/env python3
"""
SDT管理系统启动脚本
用于快速启动后端服务和检查系统状态
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_redis():
    """检查Redis服务是否运行"""
    try:
        import redis
        client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        client.ping()
        print("✓ Redis服务正常")
        return True
    except Exception as e:
        print(f"✗ Redis服务异常: {e}")
        print("请确保Redis服务已启动：")
        print("  Windows: 下载并启动redis-server.exe")
        print("  Linux/Mac: sudo systemctl start redis 或 redis-server")
        return False

def check_python_dependencies():
    """检查Python依赖"""
    try:
        import flask
        import redis
        import jwt
        import marshmallow
        import werkzeug
        print("✓ Python依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ Python依赖缺失: {e}")
        print("请安装依赖：pip install -r backend/requirements.txt")
        return False

def start_backend():
    """启动后端服务"""
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("✗ backend目录不存在")
        return None
    
    print("启动后端服务...")
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['FLASK_ENV'] = 'development'
        env['PYTHONPATH'] = str(backend_dir.absolute())
        
        # 启动Flask应用
        process = subprocess.Popen(
            [sys.executable, "run.py"],
            cwd=backend_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务启动
        time.sleep(3)
        
        # 检查服务是否启动成功
        try:
            response = requests.get("http://localhost:5000/api/auth/verify", timeout=5)
            print("✓ 后端服务启动成功 (http://localhost:5000)")
            return process
        except requests.exceptions.RequestException:
            # 服务可能还在启动中，这是正常的
            print("✓ 后端服务正在启动... (http://localhost:5000)")
            return process
            
    except Exception as e:
        print(f"✗ 后端服务启动失败: {e}")
        return None

def check_frontend_dependencies():
    """检查前端依赖"""
    frontend_dir = Path("frontend")
    node_modules = frontend_dir / "node_modules"
    
    if not node_modules.exists():
        print("✗ 前端依赖未安装")
        print("请安装依赖：")
        print("  cd frontend")
        print("  npm install")
        return False
    
    print("✓ 前端依赖已安装")
    return True

def create_test_data():
    """创建测试数据"""
    try:
        # 等待后端服务完全启动
        time.sleep(2)
        
        # 测试默认管理员登录
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            "http://localhost:5000/api/auth/login",
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✓ 默认管理员账户可用 (admin/admin123)")
            
            # 获取token
            token = response.json()['data']['access_token']
            headers = {'Authorization': f'Bearer {token}'}
            
            # 创建测试问题
            issue_data = {
                "title": "系统测试问题",
                "description": "这是一个测试问题，用于验证系统功能",
                "type": "bug",
                "priority": "medium",
                "tags": ["测试", "系统"]
            }
            
            issue_response = requests.post(
                "http://localhost:5000/api/issues",
                json=issue_data,
                headers=headers,
                timeout=10
            )
            
            if issue_response.status_code == 201:
                print("✓ 测试数据创建成功")
            else:
                print("! 测试数据创建失败，但系统可正常使用")
                
        else:
            print("! 默认管理员账户登录失败，请检查后端服务")
            
    except Exception as e:
        print(f"! 测试数据创建失败: {e}")

def print_usage_info():
    """打印使用说明"""
    print("\n" + "="*50)
    print("SDT管理系统启动完成")
    print("="*50)
    print("后端服务: http://localhost:5000")
    print("前端服务: http://localhost:3000 (需要单独启动)")
    print("\n默认管理员账户:")
    print("  用户名: admin")
    print("  密码: admin123")
    print("\n启动前端服务:")
    print("  cd frontend")
    print("  npm start")
    print("\nAPI文档:")
    print("  查看 docs/API.md")
    print("\n停止服务:")
    print("  按 Ctrl+C 停止后端服务")
    print("="*50)

def main():
    """主函数"""
    print("SDT管理系统启动检查...")
    print("-" * 30)
    
    # 检查依赖
    if not check_redis():
        return
    
    if not check_python_dependencies():
        return
    
    # 启动后端服务
    backend_process = start_backend()
    if not backend_process:
        return
    
    # 检查前端依赖
    check_frontend_dependencies()
    
    # 创建测试数据
    create_test_data()
    
    # 打印使用说明
    print_usage_info()
    
    try:
        # 保持后端服务运行
        print("\n后端服务运行中... (按 Ctrl+C 停止)")
        backend_process.wait()
    except KeyboardInterrupt:
        print("\n正在停止服务...")
        backend_process.terminate()
        backend_process.wait()
        print("服务已停止")

if __name__ == "__main__":
    main()
