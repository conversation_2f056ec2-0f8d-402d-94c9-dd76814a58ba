import React, { useEffect, useState } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Typography, 
  Space,
  Spin,
  Alert,
  Progress,
  List,
  Avatar,
  Tag,
  Button
} from 'antd';
import {
  BugOutlined,
  FileTextOutlined,
  AppstoreOutlined,
  UserOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';

import { issueAPI, documentAPI, productAPI, userAPI } from '../../services/api';

const { Title, Text } = Typography;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    issues: {},
    documents: {},
    products: {},
    users: {}
  });
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 并行获取各模块统计数据
      const [issueStats, documentStats, productStats, userStats] = await Promise.allSettled([
        issueAPI.getStats(),
        documentAPI.getStats(),
        productAPI.getStats(),
        userAPI.getStats()
      ]);

      setStats({
        issues: issueStats.status === 'fulfilled' ? issueStats.value.data.data : {},
        documents: documentStats.status === 'fulfilled' ? documentStats.value.data.data : {},
        products: productStats.status === 'fulfilled' ? productStats.value.data.data : {},
        users: userStats.status === 'fulfilled' ? userStats.value.data.data : {}
      });

    } catch (err) {
      setError('加载仪表板数据失败');
      console.error('Dashboard data loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={loadDashboardData}>
            重试
          </Button>
        }
      />
    );
  }

  // 计算问题解决率
  const issueResolveRate = stats.issues.total > 0 
    ? Math.round((stats.issues.by_status?.['已解决'] || 0) / stats.issues.total * 100)
    : 0;

  return (
    <div className="dashboard-container">
      <div className="page-header">
        <Title level={2}>仪表板</Title>
        <Text type="secondary">系统概览和统计信息</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-24">
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="问题总数"
              value={stats.issues.total || 0}
              prefix={<BugOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                本月解决: {stats.issues.resolved_this_month || 0}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="文档总数"
              value={stats.documents.total || 0}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                总下载: {stats.documents.total_downloads || 0}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="产品版本"
              value={stats.products.total || 0}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                已发布: {stats.products.by_status?.['已发布'] || 0}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="用户总数"
              value={stats.users.total || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                今日活跃: {stats.users.active_today || 0}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 问题统计 */}
        <Col xs={24} lg={12}>
          <Card title="问题统计" className="mb-16">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <div className="flex-between mb-8">
                  <Text>解决率</Text>
                  <Text strong>{issueResolveRate}%</Text>
                </div>
                <Progress 
                  percent={issueResolveRate} 
                  strokeColor="#52c41a"
                  size="small"
                />
              </div>

              <div>
                <Text strong className="mb-8" style={{ display: 'block' }}>按状态分布</Text>
                <Space wrap>
                  {Object.entries(stats.issues.by_status || {}).map(([status, count]) => (
                    <Tag 
                      key={status}
                      color={
                        status === '待处理' ? 'blue' :
                        status === '处理中' ? 'orange' :
                        status === '已解决' ? 'green' :
                        status === '已关闭' ? 'gray' : 'default'
                      }
                    >
                      {status}: {count}
                    </Tag>
                  ))}
                </Space>
              </div>

              <div>
                <Text strong className="mb-8" style={{ display: 'block' }}>按优先级分布</Text>
                <Space wrap>
                  {Object.entries(stats.issues.by_priority || {}).map(([priority, count]) => (
                    <Tag 
                      key={priority}
                      color={
                        priority === '低' ? 'green' :
                        priority === '中' ? 'blue' :
                        priority === '高' ? 'orange' :
                        priority === '紧急' ? 'red' : 'default'
                      }
                    >
                      {priority}: {count}
                    </Tag>
                  ))}
                </Space>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 文档统计 */}
        <Col xs={24} lg={12}>
          <Card title="文档统计" className="mb-16">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong className="mb-8" style={{ display: 'block' }}>按分类分布</Text>
                <List
                  size="small"
                  dataSource={Object.entries(stats.documents.by_category || {})}
                  renderItem={([category, count]) => (
                    <List.Item>
                      <div className="flex-between w-full">
                        <Text>{category}</Text>
                        <Text strong>{count}</Text>
                      </div>
                    </List.Item>
                  )}
                />
              </div>

              <div>
                <Text strong className="mb-8" style={{ display: 'block' }}>按状态分布</Text>
                <Space wrap>
                  {Object.entries(stats.documents.by_status || {}).map(([status, count]) => (
                    <Tag 
                      key={status}
                      color={
                        status === '草稿' ? 'default' :
                        status === '审核中' ? 'orange' :
                        status === '已发布' ? 'green' :
                        status === '已归档' ? 'gray' : 'blue'
                      }
                    >
                      {status}: {count}
                    </Tag>
                  ))}
                </Space>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 用户统计 */}
        <Col xs={24} lg={12}>
          <Card title="用户统计" className="mb-16">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong className="mb-8" style={{ display: 'block' }}>按角色分布</Text>
                <List
                  size="small"
                  dataSource={Object.entries(stats.users.by_role || {})}
                  renderItem={([role, count]) => (
                    <List.Item>
                      <div className="flex-between w-full">
                        <Text>{role}</Text>
                        <Text strong>{count}</Text>
                      </div>
                    </List.Item>
                  )}
                />
              </div>
            </Space>
          </Card>
        </Col>

        {/* 快速操作 */}
        <Col xs={24} lg={12}>
          <Card title="快速操作" className="mb-16">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button type="primary" icon={<BugOutlined />} block>
                创建问题
              </Button>
              <Button icon={<FileTextOutlined />} block>
                上传文档
              </Button>
              <Button icon={<AppstoreOutlined />} block>
                发布产品
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
