#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
状态向量定义
定义描述卫星完整状态的向量结构
"""

import numpy as np
from typing import Union, Tuple


# 定义StateVector的NumPy数据类型
StateVectorDType = np.dtype([
    ('position', np.float64, (3,)),        # [x, y, z] in TEME (km)
    ('velocity', np.float64, (3,)),        # [vx, vy, vz] in TEME (km/s)
    ('quaternion', np.float64, (4,)),      # [q0, q1, q2, q3] (scalar-first)
    ('angular_velocity', np.float64, (3,)) # [ωx, ωy, ωz] in body frame (rad/s)
])


class StateVector:
    """
    卫星状态向量类
    封装卫星的完整状态信息，包括位置、速度、姿态和角速度
    """
    
    def __init__(self, position: np.ndarray = None, velocity: np.ndarray = None,
                 quaternion: np.ndarray = None, angular_velocity: np.ndarray = None):
        """
        初始化状态向量
        
        Args:
            position: 位置向量 [x, y, z] (km)
            velocity: 速度向量 [vx, vy, vz] (km/s)
            quaternion: 四元数 [q0, q1, q2, q3] (scalar-first)
            angular_velocity: 角速度向量 [ωx, ωy, ωz] (rad/s)
        """
        self.position = position if position is not None else np.zeros(3)
        self.velocity = velocity if velocity is not None else np.zeros(3)
        self.quaternion = quaternion if quaternion is not None else np.array([1.0, 0.0, 0.0, 0.0])
        self.angular_velocity = angular_velocity if angular_velocity is not None else np.zeros(3)
        
        # 验证输入
        self._validate()
    
    def _validate(self):
        """验证状态向量的有效性"""
        # 检查数组维度
        if self.position.shape != (3,):
            raise ValueError(f"位置向量必须是3维: {self.position.shape}")
        if self.velocity.shape != (3,):
            raise ValueError(f"速度向量必须是3维: {self.velocity.shape}")
        if self.quaternion.shape != (4,):
            raise ValueError(f"四元数必须是4维: {self.quaternion.shape}")
        if self.angular_velocity.shape != (3,):
            raise ValueError(f"角速度向量必须是3维: {self.angular_velocity.shape}")
        
        # 检查四元数归一化
        quat_norm = np.linalg.norm(self.quaternion)
        if abs(quat_norm - 1.0) > 1e-6:
            # 自动归一化
            self.quaternion = self.quaternion / quat_norm
    
    def to_structured_array(self) -> np.ndarray:
        """
        转换为NumPy结构化数组
        
        Returns:
            结构化数组
        """
        state_array = np.zeros(1, dtype=StateVectorDType)
        state_array['position'][0] = self.position
        state_array['velocity'][0] = self.velocity
        state_array['quaternion'][0] = self.quaternion
        state_array['angular_velocity'][0] = self.angular_velocity
        return state_array[0]
    
    @classmethod
    def from_structured_array(cls, state_array: np.ndarray) -> 'StateVector':
        """
        从NumPy结构化数组创建StateVector
        
        Args:
            state_array: 结构化数组
            
        Returns:
            StateVector对象
        """
        return cls(
            position=state_array['position'].copy(),
            velocity=state_array['velocity'].copy(),
            quaternion=state_array['quaternion'].copy(),
            angular_velocity=state_array['angular_velocity'].copy()
        )
    
    def to_flat_array(self) -> np.ndarray:
        """
        转换为一维数组（用于数值积分）
        
        Returns:
            一维数组 [x,y,z,vx,vy,vz,q0,q1,q2,q3,wx,wy,wz]
        """
        return np.concatenate([
            self.position,
            self.velocity,
            self.quaternion,
            self.angular_velocity
        ])
    
    @classmethod
    def from_flat_array(cls, flat_array: np.ndarray) -> 'StateVector':
        """
        从一维数组创建StateVector
        
        Args:
            flat_array: 一维数组 [x,y,z,vx,vy,vz,q0,q1,q2,q3,wx,wy,wz]
            
        Returns:
            StateVector对象
        """
        if len(flat_array) != 13:
            raise ValueError(f"一维数组长度必须为13: {len(flat_array)}")
        
        return cls(
            position=flat_array[0:3].copy(),
            velocity=flat_array[3:6].copy(),
            quaternion=flat_array[6:10].copy(),
            angular_velocity=flat_array[10:13].copy()
        )
    
    def copy(self) -> 'StateVector':
        """创建副本"""
        return StateVector(
            position=self.position.copy(),
            velocity=self.velocity.copy(),
            quaternion=self.quaternion.copy(),
            angular_velocity=self.angular_velocity.copy()
        )
    
    def get_orbital_energy(self, mu: float = 3.986004418e14) -> float:
        """
        计算轨道能量
        
        Args:
            mu: 地球引力参数 (m³/s²)
            
        Returns:
            比轨道能量 (J/kg)
        """
        r = np.linalg.norm(self.position) * 1000  # 转换为米
        v = np.linalg.norm(self.velocity) * 1000  # 转换为m/s
        
        kinetic_energy = 0.5 * v * v
        potential_energy = -mu / r
        
        return kinetic_energy + potential_energy
    
    def get_orbital_elements(self, mu: float = 3.986004418e14) -> dict:
        """
        计算轨道要素
        
        Args:
            mu: 地球引力参数 (m³/s²)
            
        Returns:
            轨道要素字典
        """
        # 转换单位
        r_vec = self.position * 1000  # km -> m
        v_vec = self.velocity * 1000  # km/s -> m/s
        
        r = np.linalg.norm(r_vec)
        v = np.linalg.norm(v_vec)
        
        # 角动量向量
        h_vec = np.cross(r_vec, v_vec)
        h = np.linalg.norm(h_vec)
        
        # 轨道倾角
        inclination = np.arccos(h_vec[2] / h)
        
        # 升交点向量
        n_vec = np.cross([0, 0, 1], h_vec)
        n = np.linalg.norm(n_vec)
        
        # 升交点赤经
        if n > 1e-10:
            raan = np.arccos(n_vec[0] / n)
            if n_vec[1] < 0:
                raan = 2 * np.pi - raan
        else:
            raan = 0.0
        
        # 偏心率向量
        e_vec = ((v*v - mu/r) * r_vec - np.dot(r_vec, v_vec) * v_vec) / mu
        eccentricity = np.linalg.norm(e_vec)
        
        # 半长轴
        energy = v*v/2 - mu/r
        if abs(energy) > 1e-10:
            semi_major_axis = -mu / (2 * energy) / 1000  # 转换回km
        else:
            semi_major_axis = float('inf')
        
        # 近地点幅角
        if n > 1e-10 and eccentricity > 1e-10:
            arg_of_perigee = np.arccos(np.dot(n_vec, e_vec) / (n * eccentricity))
            if e_vec[2] < 0:
                arg_of_perigee = 2 * np.pi - arg_of_perigee
        else:
            arg_of_perigee = 0.0
        
        # 真近点角
        if eccentricity > 1e-10:
            true_anomaly = np.arccos(np.dot(e_vec, r_vec) / (eccentricity * r))
            if np.dot(r_vec, v_vec) < 0:
                true_anomaly = 2 * np.pi - true_anomaly
        else:
            true_anomaly = 0.0
        
        return {
            'semi_major_axis': semi_major_axis,
            'eccentricity': eccentricity,
            'inclination': np.degrees(inclination),
            'raan': np.degrees(raan),
            'arg_of_perigee': np.degrees(arg_of_perigee),
            'true_anomaly': np.degrees(true_anomaly),
            'orbital_period': 2 * np.pi * np.sqrt(abs(semi_major_axis * 1000)**3 / mu) if semi_major_axis > 0 else float('inf')
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"StateVector(pos={self.position}, vel={self.velocity}, "
                f"quat={self.quaternion}, omega={self.angular_velocity})")
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


def state_to_flat_array(state: Union[StateVector, np.ndarray]) -> np.ndarray:
    """
    将状态转换为一维数组
    
    Args:
        state: StateVector对象或结构化数组
        
    Returns:
        一维数组
    """
    if isinstance(state, StateVector):
        return state.to_flat_array()
    elif isinstance(state, np.ndarray) and state.dtype == StateVectorDType:
        return np.concatenate([
            state['position'],
            state['velocity'],
            state['quaternion'],
            state['angular_velocity']
        ])
    else:
        raise TypeError(f"不支持的状态类型: {type(state)}")


def flat_array_to_state(flat_array: np.ndarray) -> np.ndarray:
    """
    将一维数组转换为结构化状态数组
    
    Args:
        flat_array: 一维数组
        
    Returns:
        结构化状态数组
    """
    if len(flat_array) != 13:
        raise ValueError(f"一维数组长度必须为13: {len(flat_array)}")
    
    state_array = np.zeros(1, dtype=StateVectorDType)
    state_array['position'][0] = flat_array[0:3]
    state_array['velocity'][0] = flat_array[3:6]
    state_array['quaternion'][0] = flat_array[6:10]
    state_array['angular_velocity'][0] = flat_array[10:13]
    
    return state_array[0]
