import pytest
from unittest.mock import Mock, patch
import time
import uuid

from core.communication_module import (
    Message,
    MessageType,
    SessionManager,
    MessageProcessor,
    CommunicationModule
)


@pytest.fixture
def session_manager():
    """Pytest fixture for SessionManager."""
    return SessionManager()


@pytest.fixture
def message_processor():
    """Pytest fixture for MessageProcessor."""
    processor = MessageProcessor()
    processor.start()
    yield processor
    processor.stop()

@pytest.fixture
def communication_module():
    """Pytest fixture for CommunicationModule."""
    with patch('utils.logger.setup_logger'):
        config = Mock()
        config.get.return_value = {'host': 'localhost', 'port': 9999}
        module = CommunicationModule(config)
        module.start()
        yield module
        module.stop()

class TestMessage:
    def test_message_creation(self):
        msg = Message(
            message_id="test_id_123",
            message_type=MessageType.TOPOLOGY_REQUEST,
            source="node_A",
            destination="node_B",
            payload={"key": "value"},
            timestamp=time.time()
        )
        assert msg.message_id == "test_id_123"
        assert msg.message_type == MessageType.TOPOLOGY_REQUEST
        assert msg.source == "node_A"
        assert msg.destination == "node_B"
        assert msg.payload == {"key": "value"}
        assert isinstance(msg.timestamp, float)


class TestSessionManager:
    def test_create_session(self, session_manager):
        session_id = "session_1"
        client_info = {"ip": "127.0.0.1"}
        assert session_manager.create_session(session_id, client_info)
        assert not session_manager.create_session(session_id, client_info)  # Already exists

    def test_get_session(self, session_manager):
        session_id = "session_2"
        client_info = {"ip": "127.0.0.1"}
        session_manager.create_session(session_id, client_info)
        session = session_manager.get_session(session_id)
        assert session is not None
        assert session['id'] == session_id

    def test_update_session_activity(self, session_manager):
        session_id = "session_3"
        client_info = {"ip": "127.0.0.1"}
        session_manager.create_session(session_id, client_info)
        session = session_manager.get_session(session_id)
        last_activity = session['last_activity']
        msg_count = session['message_count']
        time.sleep(0.1)
        session_manager.update_session_activity(session_id)
        updated_session = session_manager.get_session(session_id)
        assert updated_session['last_activity'] > last_activity
        assert updated_session['message_count'] == msg_count + 1

    def test_close_session(self, session_manager):
        session_id = "session_4"
        client_info = {"ip": "127.0.0.1"}
        session_manager.create_session(session_id, client_info)
        session_manager.close_session(session_id)
        session = session_manager.get_session(session_id)
        assert not session['active']

    def test_cleanup_expired_sessions(self, session_manager):
        session_id = "session_5"
        client_info = {"ip": "127.0.0.1"}
        session_manager.create_session(session_id, client_info)
        session = session_manager.get_session(session_id)
        session['last_activity'] = time.time() - 301 # Expired
        session_manager.cleanup_expired_sessions(timeout=300)
        assert session_manager.get_session(session_id) is None


class TestMessageProcessor:
    def test_register_and_process_message(self, message_processor):
        mock_processor = Mock()
        msg_type = MessageType.CONTROL_COMMAND
        message_processor.register_processor(msg_type, mock_processor)

        msg = Message(
            message_id=str(uuid.uuid4()),
            message_type=msg_type,
            source="test",
            destination="processor",
            payload={},
            timestamp=time.time()
        )
        message_processor.send_message(msg)
        time.sleep(0.1)  # Allow time for processing
        mock_processor.assert_called_once_with(msg)

class TestCommunicationModule:
    def test_initialization(self, communication_module):
        assert communication_module.session_manager is not None
        assert communication_module.message_processor is not None
        assert communication_module.request_receiver is not None

    def test_send_message(self, communication_module):
        msg = Message(
            message_id=str(uuid.uuid4()),
            message_type=MessageType.DATA_PACKET,
            source="comm_module_test",
            destination="somewhere",
            payload={'data': 'important'},
            timestamp=time.time()
        )
        # We are not testing the network part, just that it goes to the queue
        with patch.object(communication_module.message_processor, 'send_message') as mock_send:
            communication_module.send_message(msg)
            mock_send.assert_called_once_with(msg)

    def test_create_and_close_session(self, communication_module):
        client_info = {'id': 'client_x'}
        session_id = communication_module.create_session(client_info)
        assert session_id is not None
        session = communication_module.session_manager.get_session(session_id)
        assert session is not None
        
        communication_module.close_session(session_id)
        session = communication_module.session_manager.get_session(session_id)
        assert not session['active'] 