"""
时间管理器模块

负责离散事件仿真的时间管理，包括仿真时间推进、时间统计等功能。
"""

import time
from typing import Dict, List, Optional


class TimeManager:
    """
    时间管理器
    
    管理仿真时间，提供时间推进、时间统计、时间控制等功能。
    """
    
    def __init__(self, start_time: float = 0.0):
        """
        初始化时间管理器
        
        Args:
            start_time: 仿真开始时间
        """
        self.simulation_time = start_time
        self.start_time = start_time
        self.real_start_time = time.time()
        self.paused = False
        self.pause_start_time = None
        self.total_pause_duration = 0.0
        
        # 时间统计
        self.time_advance_count = 0
        self.time_statistics = {
            'min_time_step': float('inf'),
            'max_time_step': 0.0,
            'total_time_steps': 0,
            'average_time_step': 0.0
        }
        
        # 时间检查点
        self.checkpoints = {}
        
        # 时间事件监听器
        self.time_listeners = []
    
    def advance_time(self, new_time: float) -> bool:
        """
        推进仿真时间
        
        Args:
            new_time: 新的仿真时间
            
        Returns:
            bool: 时间推进是否成功
        """
        if self.paused:
            return False
        
        if new_time < self.simulation_time:
            return False  # 不允许时间倒退
        
        # 计算时间步长
        time_step = new_time - self.simulation_time
        
        if time_step > 0:
            # 更新统计信息
            self._update_time_statistics(time_step)
            
            # 通知时间监听器
            self._notify_time_listeners(self.simulation_time, new_time)
        
        self.simulation_time = new_time
        self.time_advance_count += 1
        
        return True
    
    def get_current_time(self) -> float:
        """获取当前仿真时间"""
        return self.simulation_time
    
    def get_elapsed_time(self) -> float:
        """获取已经过的仿真时间"""
        return self.simulation_time - self.start_time
    
    def get_real_elapsed_time(self) -> float:
        """获取真实经过的时间（排除暂停时间）"""
        real_time = time.time() - self.real_start_time - self.total_pause_duration
        if self.paused and self.pause_start_time:
            real_time -= (time.time() - self.pause_start_time)
        return real_time
    
    def pause(self):
        """暂停仿真"""
        if not self.paused:
            self.paused = True
            self.pause_start_time = time.time()
    
    def resume(self):
        """恢复仿真"""
        if self.paused:
            if self.pause_start_time:
                self.total_pause_duration += time.time() - self.pause_start_time
            self.paused = False
            self.pause_start_time = None
    
    def is_paused(self) -> bool:
        """检查仿真是否暂停"""
        return self.paused
    
    def reset(self, new_start_time: float = 0.0):
        """
        重置时间管理器
        
        Args:
            new_start_time: 新的开始时间
        """
        self.simulation_time = new_start_time
        self.start_time = new_start_time
        self.real_start_time = time.time()
        self.paused = False
        self.pause_start_time = None
        self.total_pause_duration = 0.0
        self.time_advance_count = 0
        
        # 重置统计信息
        self.time_statistics = {
            'min_time_step': float('inf'),
            'max_time_step': 0.0,
            'total_time_steps': 0,
            'average_time_step': 0.0
        }
        
        self.checkpoints.clear()
    
    def create_checkpoint(self, name: str):
        """
        创建时间检查点
        
        Args:
            name: 检查点名称
        """
        self.checkpoints[name] = {
            'simulation_time': self.simulation_time,
            'real_time': time.time(),
            'advance_count': self.time_advance_count
        }
    
    def get_checkpoint(self, name: str) -> Optional[Dict]:
        """
        获取时间检查点
        
        Args:
            name: 检查点名称
            
        Returns:
            检查点信息或None
        """
        return self.checkpoints.get(name)
    
    def get_time_since_checkpoint(self, name: str) -> Optional[float]:
        """
        获取距离检查点的仿真时间
        
        Args:
            name: 检查点名称
            
        Returns:
            距离检查点的时间或None
        """
        checkpoint = self.checkpoints.get(name)
        if checkpoint:
            return self.simulation_time - checkpoint['simulation_time']
        return None
    
    def add_time_listener(self, listener_func):
        """
        添加时间监听器
        
        Args:
            listener_func: 监听器函数，接收(old_time, new_time)参数
        """
        if listener_func not in self.time_listeners:
            self.time_listeners.append(listener_func)
    
    def remove_time_listener(self, listener_func):
        """
        移除时间监听器
        
        Args:
            listener_func: 要移除的监听器函数
        """
        if listener_func in self.time_listeners:
            self.time_listeners.remove(listener_func)
    
    def get_time_statistics(self) -> Dict:
        """获取时间统计信息"""
        return self.time_statistics.copy()
    
    def get_simulation_speed(self) -> float:
        """
        获取仿真速度（仿真时间/真实时间）
        
        Returns:
            仿真速度比率
        """
        real_elapsed = self.get_real_elapsed_time()
        if real_elapsed > 0:
            return self.get_elapsed_time() / real_elapsed
        return 0.0
    
    def _update_time_statistics(self, time_step: float):
        """更新时间统计信息"""
        stats = self.time_statistics
        
        if time_step < stats['min_time_step']:
            stats['min_time_step'] = time_step
        
        if time_step > stats['max_time_step']:
            stats['max_time_step'] = time_step
        
        stats['total_time_steps'] += 1
        
        # 计算平均时间步长
        total_time = self.get_elapsed_time()
        if stats['total_time_steps'] > 0:
            stats['average_time_step'] = total_time / stats['total_time_steps']
    
    def _notify_time_listeners(self, old_time: float, new_time: float):
        """通知时间监听器"""
        for listener in self.time_listeners:
            try:
                listener(old_time, new_time)
            except Exception as e:
                # 忽略监听器错误，避免影响仿真
                pass
    
    def __str__(self):
        return (f"TimeManager(current={self.simulation_time:.6f}, "
                f"elapsed={self.get_elapsed_time():.6f}, "
                f"paused={self.paused})")
    
    def __repr__(self):
        return self.__str__() 